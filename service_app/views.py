from django.shortcuts import render

from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from core.auth.custom_auth import CustomUserAuthentication

from django.contrib.auth import get_user_model

from service_app.func import create_sales_account


User = get_user_model()


# Create your views here.
class SalesUserAccount(APIView):
    permission_classes = [
        IsAuthenticated,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        """
        Check if the sales user account exists.
        """
        user = request.user
        all_sales_user = User.objects.filter(
            email__startswith=user.email, username__isnull=False
        )
        if all_sales_user.exists():
            return Response({"status": True, "message": "Sales user account exists."}, status=status.HTTP_204_NO_CONTENT)
        return Response({"exists": False, "message": "Sales user account does not exist."}, status=status.HTTP_404_NOT_FOUND)
    
class CreateSalesUserAccount(APIView):
    permission_classes = [
        IsAuthenticated,
    ]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        """
        Check if the sales user account exists.
        """
        user = request.user
        all_sales_user = User.objects.filter(
            email__startswith=user.email, username__isnull=False, is_default_sales_user=True
        ).first()
        if all_sales_user:
            first_name = all_sales_user.first_name
            last_name = all_sales_user.last_name
            username = all_sales_user.username
            password = all_sales_user.sales_passcode
            response = {
                "status": True,
                "message": "User Details retrieved successfully",
                "first_name": first_name,
                "last_name": last_name,
                "username": username,
                "passcode": password,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            # If the user does not exist, create a new sales account
            # This will create a sales user account with a unique username.
            # The username is generated based on the user's email and a random suffix.
            # The passcode is also generated and returned in the response.

            response = create_sales_account(user=user)

            return Response(response, status=status.HTTP_200_OK)

        