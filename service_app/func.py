import random
import string
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password

User = get_user_model()

def generate_random_username(length: int = 8, prefix: str = "user") -> str:
    """
    Generate a unique random username with optional prefix (e.g., 'user83jd9kq2').
    No max_attempts limit — retries indefinitely until a unique username is found.
    """
    while True:
        random_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))
        username = f"{prefix}{random_part}"
        if not User.objects.filter(username=username).exists():
            return username

def old_create_sales_account(user):
    """
    Create a sales user account with a unique username.
    """
    from core.models import SalesUserRegistrationDump
    from core.services import SalesUser
    from invoicing.models import Tax
    from requisition.models import Company
    from django.utils import timezone
    from account.tasks import create_wema_sales_account

    from service_app.func import generate_random_username
    from stock_inventory.models import Branch

    company_user = Company.objects.filter(user=user)
    incorporation_date = timezone.now().date()

    if company_user.exists():
        company = company_user.first()
    else:
        company = Company.objects.create(
            user=user,
            company_wallet_type="MAIN",
            company_name=f"{user.first_name} Store".title(),
            industry="OTHERS",
            size="1",
            channel="MOBILE",
            is_active=True,
            incorporation_date=incorporation_date
        )

    branch = Branch.objects.create(
        company=company,
        name="MAIN BRANCH 1",
        address=f"{user.street} {user.nearest_landmark} {user.state}",
        vat=0,
        is_super_branch=False,
        created_by=user,
    )
    Tax.add(
        company=company,
        branch=branch,
        title="Value Added Tax",
        rate=0,
        created_by=user,
    )
    create_wema_sales_account.delay(branch.id)
    
    username = generate_random_username()
    first_name = user.first_name
    last_name = user.last_name
    sales_user_role = "ALL"

    passcode = SalesUser.generate_passcode()

    SalesUserRegistrationDump.objects.create(
        username=username,
        first_name=first_name,
        last_name=last_name,
        email=f"{company.user.email}-{username}",
        default_company=company,
        default_branch=branch,
    )
    User.objects.create(
        username=username,
        first_name=first_name,
        last_name=last_name,
        email=f"{company.user.email}-{username}",
        default_company=company,
        default_branch=branch,
        sales_passcode=passcode,
        sales_user_role=sales_user_role,
        channel="MOBILE",
    )
    data = {
        "status": True,
        "message": "User created successfully.",
        "first_name": first_name,
        "last_name": last_name,
        "username": username,
        "passcode": passcode,
    }
    return data

def create_sales_account(user):
    """
    Create a sales user account with a unique username.
    """
    from core.models import SalesUserRegistrationDump, ConstantTable
    from core.services import SalesUser
    from invoicing.models import Tax
    from requisition.models import Company
    from django.utils import timezone
    from account.tasks import create_wema_sales_account

    from service_app.func import generate_random_username
    from stock_inventory.models import Branch


    CONST = ConstantTable.get_constant_instance()

    password = CONST.default_sales_user_password

    company_user = Company.objects.filter(user=user, is_pos_default=True)
    incorporation_date = timezone.now().date()

    if company_user.exists():
        company = company_user.first()
    else:
        company = Company.objects.create(
            user=user,
            company_wallet_type="MAIN",
            company_name=f"{user.first_name} Default Store".title(),
            industry="OTHERS",
            size="1",
            channel="MOBILE",
            is_active=True,
            incorporation_date=incorporation_date,
            is_pos_default=True
        )

    branch_user = Branch.objects.filter(company=company, is_pos_default=True)
    if branch_user.exists():
        branch = branch_user.first()
    else:
        branch = Branch.objects.create(
            company=company,
            name="MAIN BRANCH DEFAULT",
            address=f"{user.street} {user.nearest_landmark} {user.state}",
            vat=0,
            is_super_branch=False,
            created_by=user,
            is_pos_default=True
        )
    Tax.add(
        company=company,
        branch=branch,
        title="Value Added Tax",
        rate=0,
        created_by=user,
    )
    create_wema_sales_account.delay(branch.id)
    
    username = f"{user.id}_{generate_random_username()}"
    first_name = user.first_name
    last_name = user.last_name
    sales_user_role = "ALL"

    SalesUserRegistrationDump.objects.create(
        username=username,
        first_name=first_name,
        last_name=last_name,
        email=f"{company.user.email}-{username}",
        default_company=company,
        default_branch=branch,
    )
    user = User.objects.create(
        username=username,
        first_name=first_name,
        last_name=last_name,
        email=f"{company.user.email}-{username}",
        default_company=company,
        default_branch=branch,
        sales_passcode=password,
        sales_user_role=sales_user_role,
        channel="MOBILE",
        is_default_sales_user=True
    )
    user.password = make_password(password)
    user.save()

    data = {
        "status": True,
        "message": "User Details retrieved successfully",
        "first_name": first_name,
        "last_name": last_name,
        "username": username,
        "passcode": password,
    }
    return data
    