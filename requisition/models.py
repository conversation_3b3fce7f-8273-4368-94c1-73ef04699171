import json
import random
import re
import string
import uuid
from datetime import date, datetime
from typing import Optional

import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import IntegrityError, models, transaction
from django.db.models import Count, F, Q, Sum
from django.template.defaultfilters import truncatechars
from django.utils import timezone

from core.exceptions import InvalidRequestException
from core.helpers.func import calculate_percentage, encrypt_string
from core.models import BaseModel, DeleteHandler
from requisition.helpers.enums import TeamChoices, UserRole, UserStatus

# from requisition.helpers.enums import TeamType, UserRole, UserStatus
#
# from performance_sales_metrics_dashboard.models import SalesOfficer


ENVIRONMENT = settings.ENVIRONMENT

User = get_user_model()

STATUS = (
    ("PENDING", "PENDING"),
    ("APPROVED", "APPROVED"),
    ("SUCCESSFUL", "SUCCESSFUL"),
    ("DECLINED", "DECLINED"),
    ("PROCESSING", "PROCESSING"),
    ("FAILED", "FAILED"),
    ("REVERSED", "REVERSED"),
)

CHANNEL = [
    ("USSD", "USSD"),
    ("WEB", "WEB"),
    ("MOBILE", "MOBILE"),
    ("POS", "POS"),
]

DISBURSEMENT_WALLET_TYPE = (("MAIN", "MAIN"), ("CORPORATE", "CORPORATE"))

PURCHASE_INDENT_APPROVAL_TYPE_CHOICES = (
    ("APPROVE_ONLY", "Approve Only"),
    ("APPROVE_WITH_PO", "Approve with Purchase Order"),
    ("APPROVE_WITH_OTP", "Approve With OTP"),
    ("APPROVE_WITH_INVOICE", "Approved with Invoice"),
    ("PENDING_APPROVAL", "Pending Approval"),
    ("DECLINED", "Declined"),
)

PURCHASE_ORDER_STATUS_CHOICES = (
    ("UNDER_REVIEW", "Under Review"),
    ("ACCEPTED", "Accepted"),
    ("DENIED", "Denied"),
    ("CANCELLED", "Cancelled"),
)

PURCHASE_PAYMENT_TERMS_CHOICES = (
    ("PARTIAL_UPFRONT", "Partial Upfront"),
    ("FULL_UPFRONT", "Full Upfront"),
    ("ON_DELIVERY", "On Delivery"),
    ("AFTER_DELIVERY", "After Delivery"),
)

PURCHASE_DELIVERY_MODE_CHOICES = (
    ("PARTLY_DELIVERED", "Partly Delivered"),
    ("FULLY_DELIVERED", "Fully Delivered"),
    ("NOT_DELIVERED", "Not Delivered"),
)

SUPPLIER_ADDITION_CHOICES = (("PREFILL", "Prefill"), ("INVITE", "Invite"))

DELIVERY_WITH_RETURNS_CHOICES = [
    ("WITH_RETURNS", "With Returns"),
    ("WITHOUT_RETURNS", "Without Returns"),
]

RETURN_REASON_CHOICES = (
    ("DAMAGED", "Damaged Item"),
    ("MISSING", "Missing Item"),
    ("WRONG_SPECIFICATION", "Wrong Specification"),
    ("LATE_DELIVERY", "Late Delivery"),
    ("DEFECTED", "Defected"),
    ("OTHERS", "Others"),
)

AD_GENERATED_USERS_CHANNEL = [
    ("USSD", "USSD"),
    ("WHATSAPP", "WHATSAPP"),
]

AD_GENERATED_USER_ACTIVITY = (
    ("JOTFORM_SENT", "JOTFORM_SENT"),
    ("JOTFORM_DATA_RECEIVED", "JOTFORM_DATA_RECEIVED"),
    ("SMS_TO_AI", "SMS_TO_AI"),
    ("LEARN_ABOUT_PAYBOX360", "LEARN_ABOUT_PAYBOX360"),
    ("REQUEST_A_FREE_DEMO", "REQUEST_A_FREE_DEMO"),
    ("SPEAK_WITH_AN_ADVISOR", "SPEAK_WITH_AN_ADVISOR"),
    ("RECEIVE_BROCHURE_VIA_SMS", "RECEIVE_BROCHURE_VIA_SMS"),
    ("START_A_FREE_TRIAL", "START_A_FREE_TRIAL"),
    ("DOWNLOAD_A_BROCHURE", "DOWNLOAD_A_BROCHURE"),
    ("INTERESTED_IN_HR_MODULE", "INTERESTED_IN_HR_MODULE"),
    ("INTERESTED_IN_SPEND_MANAGEMENT_MODULE", "INTERESTED_IN_SPEND_MANAGEMENT_MODULE"),
    ("INTERESTED_IN_SALES_MODULE", "INTERESTED_IN_SALES_MODULE"),
)

SUPPLIER_UPDATE_CHOICES = (
    ("UPDATE_PROFILE", "Update Profile"),
    ("CHANGE_PASSWORD", "Change Password"),
)

AD_GENERATED_USER_SOURCE = [
    ("BRT", "BRT"),
    ("RADIO_JINGLE", "RADIO_JINGLE"),
    ("WHATSAPP", "WHATSAPP"),
]

ASSET_DEPRECIATION_METHOD_CHOICES = (
    ("STRAIGHT_LINE", "Straight Line Depreciation"),
    ("DOUBLE_DECLINING", "Double Declining Balance Depreciation"),
    ("UNIT_OF_PRODUCTION", "Unit of Production"),
)

ASSET_CATEGORY_CHOICES = (
    ("PROPERTY", "Property"), ("VEHICLE", "Vehicle"), ("ELECTRONICS", "Electronics"), ("MACHINERY", "Machinery"),
    ("HOUSEHOLD", "Household"), ("COLLECTIBLES", "Collectibles"), ("OTHERS", "Others")
)

ASSET_EXPENSES_CATEGORY = (
    ("MAINTENANCE", "Maintenance"), ("OPERATIONS", "Operations")
)


# Create your model(s) here
class CompanyIndustry(models.Model):
    industry = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return str(self.industry)

    def save(self, *args, **kwargs) -> None:
        self.industry = self.industry.upper()
        super(CompanyIndustry, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "INDUSTRY"
        verbose_name_plural = "INDUSTRIES"

    @classmethod
    def add(cls, industry: str):
        if cls.objects.filter(industry=industry.upper()).exists():
            return False
        industry = cls.objects.create(industry=industry)
        return True


class Category(models.Model):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="category_default_user",
        null=True,
        blank=True,
    )
    budget = models.ForeignKey(
        "requisition.Budget",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="category_set",
    )
    requisition = models.ForeignKey("requisition.Requisition", on_delete=models.CASCADE, null=True, blank=True)
    title = models.CharField(max_length=255, null=True, blank=True)
    amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.0)
    other_category = models.CharField(max_length=255, null=True, blank=True)
    paid_budget = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return str(self.title)

    class Meta:
        ordering = ["-id"]
        verbose_name = "TEAM ALLOCATION CATEGORY"
        verbose_name_plural = "TEAM ALLOCATION CATEGORIES"

    @property
    def main_title(self):
        if self.title == "OTHERS":
            return self.other_category
        else:
            return self.title

    @property
    def percentage_off_budget(self):
        return calculate_percentage(amount=self.amount, total_amount=self.budget.budget_amount)

    @property
    def percentage_spent(self):
        budget = self.budget
        company = budget.company

        disbursed_requisition = Requisition.objects.filter(
            company=company,
            is_disbursed=True,
            status="APPROVED",
            requisition_category=self.title,
            approved_at__date__range=[budget.start_date, budget.end_date],
        )

        if disbursed_requisition:
            amount = disbursed_requisition.aggregate(sum_amount=Sum("request_amount"))["sum_amount"]
            percentage = calculate_percentage(amount=amount, total_amount=self.amount)
        else:
            percentage = 0
        return percentage

    # Paid budget properties --------------------------------------------------------------------------------------
    @property
    def amount_recorded(self):
        expenses = Expense.objects.filter(category=self)
        amount = expenses.aggregate(sum_amount=Sum("expense_amount"))["sum_amount"]
        return amount if amount is not None else 0

    @property
    def amount_paid(self):
        return self.requisition.request_amount

    @property
    def amount_spent_percentage(self):
        expenses = Expense.objects.filter(category=self)
        if expenses.exists():
            total_expense_amount_recorded = expenses.aggregate(sum_amount=Sum("expense_amount"))["sum_amount"]
            requisition_amount = self.requisition.request_amount
            percentage = calculate_percentage(amount=total_expense_amount_recorded, total_amount=requisition_amount)
            return percentage

        else:
            return 0

    @classmethod
    def del_category(cls, ids):
        cls.objects.filter(id__in=ids).delete()

    @classmethod
    def update_category(cls, data):
        category_id = data.get("category_id")
        category_value = data.get("category")
        other_category = data.get("other_category")
        amount = data.get("amount")

        category = cls.objects.get(id=int(category_id))

        if category_value != "":
            category.title = category_value
        if amount > 0:
            category.amount = amount
        if other_category != "":
            category.other_category = other_category

        category.save()
        return True


class TeamMember(BaseModel):
    member = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="team_member",
        null=True,
        blank=True,
    )
    email = models.CharField(max_length=255, null=True, blank=True)
    phone_no = models.CharField(max_length=255, null=True, blank=True)
    is_registered = models.BooleanField(default=False)
    status = models.CharField(max_length=255, choices=UserStatus.choices, null=True, blank=True)
    team = models.ForeignKey("requisition.Team", on_delete=models.CASCADE, related_name="team")
    channel = models.CharField(max_length=200, choices=CHANNEL, default="WEB")
    role = models.CharField(max_length=255, choices=UserRole.choices, default=UserRole.MEMBER)
    req_no = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)

    def __str__(self) -> str:
        return str(self.email)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "TEAM MEMBER"
        verbose_name_plural = "TEAM MEMBERS"

    @classmethod
    def member_exists(cls, email, team_ins=None):

        if team_ins is None:
            mem = cls.objects.filter(email=email, is_deleted=False)
        else:
            # print("email and team instance check")
            mem = cls.objects.filter(email=email, team_id=team_ins.id, is_deleted=False)

        if mem.aggregate(count=Count("id"))["count"] > 0:
            return mem
        else:
            return None

    @classmethod
    def create_and_add_team_members_to_expected_team(cls, members, invited_members, team_ins):
        created_team_members = []
        for member in members:
            email = member.get("email")
            phone_no = member.get("phone_no")
            role = member.get("role")

            team_member = cls.member_exists(email=email, team_ins=team_ins)

            user = User.user_exist(email=email, phone_no=phone_no)
            if team_member:
                # Update existing record
                mem = team_member.last()
                mem.email = email
                mem.phone_no = phone_no
                mem.role = "MEMBER" if not role else role
                mem.save()
            else:
                member = cls.objects.create(
                    email=email,
                    phone_no=phone_no,
                    member=user if user is not None else None,
                    team=team_ins,
                    is_registered=True if user is not None else False,
                    status="NOT_JOINED" if not user else "ACTIVE",
                    role="MEMBER" if not role else role,
                    channel=team_ins.channel,
                )
                created_team_members.append(member)

        for member in invited_members:
            email = member.get("email")
            role = member.get("role")

            team_member = cls.member_exists(email=email, team_ins=team_ins)

            if team_member is not None:
                continue

            user = User.user_exist(email=email, phone_no=None)
            member = cls.objects.create(
                email=email,
                member=user if user is not None else None,
                team=team_ins,
                is_registered=True if user is not None else False,
                status="NOT_JOINED" if not user else "ACTIVE",
                role="MEMBER" if not role else role,
                channel=team_ins.channel,
            )
            created_team_members.append(member)

        team_data = Team.add_team_members(team_ins.company, invited_members)

        team_ins.members.add(*created_team_members)


class Company(BaseModel):
    """
    NOTE [ACTIONS/SIGNALS]:
    - default branch creation: on company creation, a signal is sent to the
    'stock_inventory' App and this signal is used to create a default super branch.
    """

    COMPANY_WALLET_TYPE = (
        ("MAIN", "MAIN"),
        ("CORPORATE", "CORPORATE"),
    )

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="company_owner",
        blank=True,
        null=True,
    )
    teams = models.ManyToManyField("requisition.Team", blank=True, related_name="company_related_teams")
    company_name = models.CharField(max_length=255)
    company_wallet_type = models.CharField(max_length=255, choices=COMPANY_WALLET_TYPE, default="MAIN")
    industry = models.CharField(max_length=255, null=True, blank=True)
    cac_num = models.CharField(max_length=255, null=True, blank=True)
    size = models.PositiveIntegerField(default=0)
    corporate_id = models.CharField(max_length=255, null=True, blank=True)
    channel = models.CharField(max_length=200, choices=CHANNEL, default="WEB")
    corporate_account_created = models.BooleanField(default=False)
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    test_account = models.BooleanField(default=False)
    instant_wage = models.BooleanField(default=False)
    auto_approve_suppliers = models.BooleanField(default=False)
    supplier_invite_id = models.CharField(max_length=200, editable=False, default="")
    incorporation_date = models.DateField(null=True, blank=True)
    product_barcode = models.CharField(max_length=255, null=True, blank=True)
    is_pos_default = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY"
        verbose_name_plural = "COMPANIES"

    def save(self, *args, **kwargs):
        if not self.supplier_invite_id:
            self.supplier_invite_id = str(uuid.uuid4()).replace("-", "").upper()[:9]
        super(Company, self).save(*args, **kwargs)

    def __str__(self):
        return str(self.company_name)

    @property
    def overall_running_budget(self):
        total_budget_amount = Budget.objects.filter(company=self, is_active=True).aggregate(
            sum_amount=Sum("budget_amount")
        )["sum_amount"]
        return total_budget_amount

    @property
    def total_current_month_expenses(self):

        today = date.today()
        expenses = Expense.objects.filter(company=self, status="SUCCESSFUL", created_at__month=today.month)
        total_current_month_expenses = expenses.aggregate(sum_amount=Sum("expense_amount"))["sum_amount"] or 0
        return total_current_month_expenses

    @property
    def current_month_expenses(self):
        from requisition.serializers import ExpenseDashboardSerializerList

        today = date.today()
        expenses = Expense.objects.filter(company=self, status="SUCCESSFUL", created_at__month=today.month)
        expense_serializer = ExpenseDashboardSerializerList(expenses, many=True)
        return expense_serializer.data

    @classmethod
    def create_company(cls, user: object, validated_data: object, transaction_pin: object) -> object:

        # print("VALIDATED DATA::::::::::::::::::", validated_data)
        wallet_type = validated_data.get("wallet_type")

        if transaction_pin:
            pin = encrypt_string(pass_code=transaction_pin)
            user.agency_banking_transaction_pin = pin
            user.save()

        cac_num = validated_data.get("cac_num")
        incorporation_date = validated_data.get("incorporation_date")
        company = cls.objects.create(
            user=user,
            company_wallet_type=wallet_type,
            company_name=validated_data.get("company_name").title(),
            industry=validated_data.get("industry"),
            cac_num=cac_num,
            size=validated_data.get("size"),
            channel=validated_data.get("wallet_type"),
            is_active=True,
            incorporation_date=incorporation_date
        )

        if ENVIRONMENT == "dev":  # update cac number on dev to make it reusable
            company.cac_num = f"RC-{company.id}NUM"
            company.save()

        return company

    @classmethod
    def retrieve_company(cls, id: str, user: Optional[User] = None):  # type: ignore
        """
        Retrieve a company using its ID and associated user if provided.
        Args:
            id (str): The ID of the company to retrieve.
            user (User, optional): The user associated with the company.
        Returns:
            Company or None: The retrieved Company object if found and active, or None if not found.
        """
        try:
            if user is not None:
                company = cls.objects.get(id=id, user=user, is_active=True)
            else:
                company = cls.objects.get(id=id, is_active=True)
        except cls.DoesNotExist:
            company = None
        return company


class Team(BaseModel, DeleteHandler):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="team_owner")
    team_name = models.CharField(max_length=255)
    team_type = models.CharField(max_length=255, choices=TeamChoices.choices, default=TeamChoices.SPEND_MGMT)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name="company")
    members = models.ManyToManyField(TeamMember, blank=True, related_name="members")
    channel = models.CharField(max_length=200, choices=CHANNEL, default="WEB")
    requisitions = models.IntegerField(default=0)
    total_allocation = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    branch = models.ForeignKey(
        "stock_inventory.Branch",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name="branch_team",
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "TEAM"
        verbose_name_plural = "TEAMS"

    def __str__(self):
        return self.team_name

    @classmethod
    def create_team(
        cls,
        user: object,
        team_name: str,
        members: list,
        company_id: str,
        invited_members: list,
        team_type: Optional[str] = None,
        channel: Optional[str] = None,
        branch_id: Optional[str] = None,
    ):
        from stock_inventory.models import Branch

        req_team_name = team_name.title()

        with transaction.atomic():
            try:
                if isinstance(branch_id, Branch):
                    branch_id = branch_id.id

                team_ins = cls.objects.create(
                    user=user,
                    company_id=company_id,
                    team_name=req_team_name,
                    team_type="SPEND_MGMT" if team_type is None else team_type,
                    branch_id=branch_id,
                    # channel=channel
                )
                TeamMember.create_and_add_team_members_to_expected_team(
                    members=members, invited_members=invited_members, team_ins=team_ins
                )
                company = team_ins.company
                company.teams.add(team_ins)
                company.save()
                # print(team_ins)
                return {"status": True, "team": team_ins}
            except IntegrityError as error:
                return {
                    "status": False,
                    "message": f"Team type {team_type} exists already in the company's branch.",
                }

    @classmethod
    def update_team(cls, validated_data):
        _team_id = validated_data["team_id"]
        members = validated_data["members"]
        team_name = validated_data["team_name"]
        invited_members = validated_data["invited_members"]

        team_ins = cls.objects.get(id=_team_id)
        TeamMember.create_and_add_team_members_to_expected_team(
            members=members, invited_members=invited_members, team_ins=team_ins
        )
        if team_name:
            team_ins.team_name = team_name.title()
            team_ins.save()
        else:
            pass

        return True

    @classmethod
    def add_team_members(cls, company_ins, members):
        from core.tasks import send_email

        for member in members:
            email = member["email"]
            user = User.objects.filter(email=email).first()
            if not user:
                send_email.delay(
                    recipient=email,
                    subject="Member Invite",
                    template_dir="non_registered_team_member_invite.html",
                    call_back_url=f"https://www.home.paybox360.com/sign-up/get-started",
                    company_name=company_ins.company_name,
                )
            else:
                send_email.delay(
                    recipient=email,
                    subject="Member Invite",
                    template_dir="registered_team_member_invite.html",
                    call_back_url=f"https://www.home.paybox360.com/login",
                    company_name=company_ins.company_name,
                )
        return True


class Requisition(models.Model):
    HIGH = "HIGH"
    LOW = "LOW"
    MEDIUM = "MEDIUM"

    PRIORITY_CHOICES = [
        (HIGH, "high"),
        (LOW, "low"),
        (MEDIUM, "medium"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="request_user")
    approved_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="approve_user",
        null=True,
        blank=True,
    )
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True)
    team = models.ForeignKey(
        Team,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="to_request_team",
    )
    member = models.ForeignKey(
        TeamMember,
        on_delete=models.CASCADE,
        related_name="request_team",
        null=True,
        blank=True,
    )
    requisition_category = models.CharField(max_length=50, null=True, blank=True)
    other_category = models.CharField(max_length=50, null=True, blank=True)
    request_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    request_reason = models.CharField(max_length=500, blank=True, null=True)
    bank_name = models.CharField(max_length=50, blank=True, null=True)
    account_no = models.CharField(max_length=15, blank=True, null=True)
    account_name = models.CharField(max_length=50, blank=True, null=True)
    bank_code = models.CharField(max_length=255, null=True, blank=True)
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS, default="PENDING")
    invoice = models.TextField(blank=True, null=True)
    is_disbursed = models.BooleanField(default=False)
    public = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    approved_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    channel = models.CharField(max_length=200, choices=CHANNEL, default="WEB")
    disbursement_wallet = models.CharField(max_length=20, choices=DISBURSEMENT_WALLET_TYPE, blank=True, null=True)
    is_paid_budget = models.BooleanField(default=False)
    is_inprogress = models.BooleanField(default=False)
    declined_reason = models.TextField(blank=True, null=True)
    payout_payload = models.TextField(blank=True, null=True)
    is_vendor = models.BooleanField(default=False)
    vendor_name = models.CharField(max_length=200, blank=True, null=True)
    vendor_no = models.CharField(max_length=12, blank=True, null=True)
    is_deleted = models.BooleanField(default=False)
    out_of_budget = models.BooleanField(default=False)

    class Meta:
        ordering = ["-id"]
        verbose_name = "REQUISITION"
        verbose_name_plural = "REQUISITIONS"

    def __str__(self):
        return str(self.id)

    @property
    def team_name(self):
        team = self.member.team.team_name
        return f"{team}"

    @property
    def user_email(self):
        email = self.user.email
        return f"{email}"

    @property
    def category(self):
        if self.requisition_category == "OTHERS":
            return self.other_category
        else:
            return self.requisition_category

    @classmethod
    def category_is_active(cls, category_id):
        categories = cls.objects.filter(requisition_category_id=category_id)
        if categories.aggregate(count=Count("id"))["count"] > 0:
            return True
        else:
            return False

    @classmethod
    def requisition_disbursement(cls, requisition, user, account, wallet_type, transaction_pin=None):
        from account.models import Transaction

        transaction_ref = uuid.uuid4()
        bank_code = requisition.bank_code

        # new send money
        payout = Transaction.vfd_funds_transfer(
            bank_code=bank_code,
            bank_name=requisition.bank_name,
            account_name=requisition.account_name,
            account_number=requisition.account_no,
            narration=requisition.request_reason,
            amount=float(requisition.request_amount),
            account=account,
            user=user,
            company_owner=requisition.company.user,
        )

        if isinstance(payout, str):
            from accounting.utils import categorize_transaction

            # Categorize Transaction
            try:
                payout_transaction = Transaction.objects.get(transaction_ref=payout)
                categorize_transaction(
                    payout_transaction,
                    source="spend_management",
                    transaction_type="expenses_and_requisitions",
                )
            except Exception:
                pass

            requisition.status = "PROCESSING"
            requisition.payout_payload = payout
            requisition.is_inprogress = True
            requisition.approved_by = user
            requisition.disbursement_wallet = wallet_type
            requisition.approved_at = timezone.now()
            requisition.save()

            _expense_instance = Expense.objects.get(requisition=requisition)
            _expense_instance.payout_result = payout
            _expense_instance.receipt = requisition.invoice
            _expense_instance.disbursement_wallet = wallet_type
            _expense_instance.ref = transaction_ref
            _expense_instance.transaction_ref = payout
            _expense_instance.payload = payout
            _expense_instance.save()

            from requisition.tasks import request_disbursement_notification

            request_disbursement_notification.delay(
                phone_no=requisition.user.phone_no,
                first_name=requisition.user.first_name,
                request_amount=requisition.request_amount,
            )

            budget_instance = Budget.objects.filter(team=requisition.team, is_active=True).last()
            if requisition.is_paid_budget is True and budget_instance:
                Category.objects.create(
                    user=user,
                    requisition=requisition,
                    budget=budget_instance,
                    title=requisition.requisition_category,
                    other_category=requisition.other_category,
                    amount=requisition.request_amount,
                    paid_budget=True,
                )

        return payout


class Comment(models.Model):
    commenter = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    requisition = models.ForeignKey(Requisition, on_delete=models.CASCADE, related_name="comments")
    comment_text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "COMMENT"
        verbose_name_plural = "COMMENTS"

    def __str__(self):
        return self.requisition


class Budget(models.Model):
    BUDGET_TYPE = (
        ("COMPANY", "COMPANY"),
        ("PERSONAL", "PERSONAL"),
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True)
    team = models.ForeignKey(Team, on_delete=models.CASCADE, null=True, blank=True)
    budget_name = models.CharField(max_length=200, blank=True, null=True)
    budget_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    running_balance = models.FloatField(default=0.0)
    budget_type = models.CharField(max_length=255, choices=BUDGET_TYPE)
    categories = models.ManyToManyField(Category, blank=True, related_name="budget_set")
    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)
    annul_balance = models.BooleanField(default=False)
    start_date = models.DateField()
    end_date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-id"]
        verbose_name = "BUDGET"
        verbose_name_plural = "BUDGETS"

    def __str__(self):
        team_name = self.team.team_name if self.team else ""
        budget_name = str(self.budget_name)
        return f"{team_name} - {budget_name}"

    @classmethod
    def create_budget_categories(cls, data):
        budget_categories = data.get("budget_categories")
        budget = data.get("budget")

        categories = []
        for budget_category in budget_categories:
            category = Category.objects.create(
                user=budget.user,
                budget=budget,
                title=budget_category.get("category"),
                amount=budget_category.get("amount"),
                other_category=budget_category.get("other_category"),
            )
            categories.append(category)

        budget.categories.add(*categories)

    @classmethod
    def update_budget_categories(cls, budget, budget_categories):
        category_ids_from_edit_request = [
            category.get("category_id") for category in budget_categories if category.get("category_id")
        ]

        existing_categories_to_budgets = budget.categories.all()

        to_remove_categories = [
            budget_category
            for budget_category in existing_categories_to_budgets
            if budget_category.id not in category_ids_from_edit_request
        ]

        # remove categories if not included
        budget.categories.remove(*to_remove_categories)

        if to_remove_categories:
            Category.objects.filter(pk__in=[obj.pk for obj in to_remove_categories]).update(
                is_deleted=True, is_active=False
            )

        # update or add to existing categories
        categories = []
        for budget_category in budget_categories:

            category_id = budget_category.get("category_id")

            title = budget_category.get("category")
            amount = budget_category.get("amount")
            other_category = budget_category.get("other_category")

            if category_id is None:
                category = Category.objects.create(
                    user=budget.user,
                    budget=budget,
                    title=title,
                    amount=amount,
                    other_category=other_category,
                )
                categories.append(category)

            else:
                category_in_budget = existing_categories_to_budgets.filter(id=int(category_id)).last()

                if category_in_budget:
                    category_in_budget.title = title
                    category_in_budget.amount = amount
                    category_in_budget.other_category = other_category
                    category_in_budget.save()

        budget.categories.add(*categories)

        return True

    @classmethod
    def update_budget(cls, data):
        budget_id = data.get("budget_id")
        budget_name = data.get("budget_name")
        budget_amount = data.get("budget_amount")
        start_date = data.get("start_date")
        end_date = data.get("end_date")
        budget = cls.objects.get(id=int(budget_id))

        if budget_name != "":
            budget.budget_name = budget_name
        if budget_amount > 0:
            budget.budget_amount = budget_amount
        if start_date != "":
            budget.start_date = start_date
        if end_date != "":
            budget.end_date = end_date

        budget.save()
        return True

    @classmethod
    def get_current_allocated_amount(cls, budget_instance, team_instance) -> dict:
        """
        Get the current allocated amount and purse balance for a specific budget and team.

        Parameters:
            - budget_instance (Budget): The budget instance for which you want to retrieve allocation information.
            - team_instance (Team): The team for which you want to calculate allocation information.

        Returns:
            dict: A dictionary containing the following information:
                - "purse_balance" (float): The remaining balance after deducting the total request amount made so far
                 from the allocated amount.
                - "total_active_allocation" (float): The total allocated amount for the budget.
                - "total_request_amount_made_so_far" (float): The total requested amount
                (N.B statuses  PENDING, PROCESSING, APPROVED, SUCCESSFUL).
        """
        if budget_instance:
            start_date = budget_instance.start_date
            end_date = budget_instance.end_date

            # Considering Procurement Requisitions too
            requisitions_sum = (
                Requisition.objects.filter(
                    Q(status="PENDING") | Q(status="PROCESSING") | Q(status="APPROVED") | Q(status="SUCCESSFUL"),
                    team=team_instance,
                    created_at__range=[start_date, end_date],
                ).aggregate(sum_amount=Sum("request_amount"))["sum_amount"]
                or 0
            )

            approved_purchase_indent_sum = (
                PurchaseIndent.objects.filter(
                    approval_status__in=[
                        "APPROVE_ONLY",
                        "APPROVE_WITH_PO",
                        "APPROVE_WITH_INVOICE",
                    ],
                    approved_by__isnull=False,
                    created_at__range=[start_date, end_date],
                ).aggregate(Sum("estimated_cost"))["estimated_cost__sum"]
                or 0
            )

            total_request_amount_made_so_far = float(requisitions_sum) + float(approved_purchase_indent_sum)

            allocations = BudgetAllocation.objects.filter(budget=budget_instance, is_active=True)
            allocated_amount = allocations.aggregate(sum_amount=Sum("amount"))["sum_amount"] or 0

            purse_balance = float(allocated_amount) - float(total_request_amount_made_so_far)

        else:
            purse_balance = 0.0
            allocated_amount = 0.0
            total_request_amount_made_so_far = 0.0

        current_budget_details = {
            "purse_balance": purse_balance,
            "total_active_allocation": allocated_amount,
            "total_request_amount_made_so_far": total_request_amount_made_so_far,
            "budget_instance": budget_instance,
        }
        return current_budget_details


class Expense(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    budget = models.ForeignKey(Budget, on_delete=models.CASCADE, null=True, blank=True)
    team = models.ForeignKey(Team, on_delete=models.CASCADE, null=True, blank=True)
    expense_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    budget_balance_before = models.FloatField(default=0.0)
    budget_balance_after = models.FloatField(default=0.0)
    purse_value_before = models.FloatField(default=0.0)
    purse_value_after = models.FloatField(default=0.0)
    status = models.CharField(max_length=20, choices=STATUS, default="PENDING")
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True)
    requisition = models.ForeignKey(Requisition, on_delete=models.CASCADE, null=True, blank=True)
    expense_category = models.CharField(max_length=50, blank=True, null=True)
    expense_other_category = models.CharField(max_length=50, blank=True, null=True)
    ref = models.CharField(max_length=50, blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.PROTECT, null=True, blank=True)
    disbursement_wallet = models.CharField(max_length=20, choices=DISBURSEMENT_WALLET_TYPE, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    in_budget = models.BooleanField(default=False)
    updated_at = models.DateTimeField(auto_now=True)
    transaction_ref = models.CharField(max_length=250, blank=True, null=True)
    payload = models.TextField(null=True, blank=True)
    receipt = models.TextField(blank=True, null=True)
    is_deleted = models.BooleanField(default=False)
    mode_of_payment = models.CharField(max_length=50, blank=True, null=True)
    invoice_number = models.CharField(max_length=50, blank=True, null=True)
    tax_information = models.CharField(max_length=50, blank=True, null=True)
    location = models.TextField(null=True, blank=True)
    item_description = models.TextField(null=True, blank=True)
    invoice_date = models.DateField(blank=True, null=True)

    class Meta:
        verbose_name = "EXPENSE"
        verbose_name_plural = "EXPENSES"

    def __str__(self):
        return str(self.id)

    @property
    def budget_amount(self):
        return calculate_percentage(total_amount=self.budget.budget_amount)

    @property
    def requested_by(self):
        user_instance = self.requisition.user
        return f"{user_instance.full_name}"

    @property
    def team_name(self):
        team = self.requisition.member.team.team_name
        return f"{team}"

    @property
    def category_title(self):
        if self.expense_category == "OTHERS":
            return self.expense_other_category
        else:
            return self.expense_category

    @classmethod
    def track_budget_by_creating_expense_object(cls, user, validated_data):
        category = validated_data.get("category")
        # receipt = validated_data.get("receipt")
        # spend_date = validated_data.get("spend_date")
        expense_amount = validated_data.get("expense_amount")

        if category.requisition:
            cls.objects.create(
                user=user,
                company=category.requisition.member.team.company,
                requisition=category.requisition,
                category=category,
                expense_category=category.title,
                budget=category.budget,
                expense_amount=expense_amount,
                status="APPROVED",
            )
        else:
            raise InvalidRequestException({"message": "Requisition not found for selected category"})
        # upload_file_aws_s3_bucket(model_instance_id=expense.id, file=receipt, model_name="Expense")

    @classmethod
    def update_balance_before_and_after_requisitions(
        cls, expense_id, budget_balance_after, purse_balance, expensed_amount
    ):

        expenses = cls.objects.filter(id=expense_id)
        expenses.update(
            budget_balance_before=F("budget_balance_before") + float(budget_balance_after) + expensed_amount,
            budget_balance_after=F("budget_balance_after") + budget_balance_after,
            purse_value_before=F("purse_value_before") + purse_balance + expensed_amount,
            purse_value_after=F("purse_value_after") + purse_balance,
        )


class CompanyVerificationInfo(BaseModel):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    tax_identification_number = models.CharField(max_length=255, null=True, blank=True)
    registration_no = models.CharField(max_length=255, null=True, blank=True)
    document_type = models.CharField(max_length=255, null=True, blank=True)
    document_number = models.CharField(max_length=255, null=True, blank=True)
    type_of_entity = models.CharField(max_length=500, null=True, blank=True)
    registration_date = models.DateTimeField(null=True, blank=True)
    registration_submission_date = models.DateTimeField(null=True, blank=True)
    director_date_of_birth = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=False)
    paid_share_capital = models.CharField(max_length=255, null=True, blank=True)
    verified = models.BooleanField(default=False)
    verification_response = models.JSONField()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY VERIFICATION INFO"
        verbose_name_plural = "COMPANY VERIFICATION INFO"

    @property
    def response_data(self):
        verification_response_str = json.dumps(self.verification_response)
        return truncatechars(verification_response_str, 20)


class CompanyVerificationMetaData(BaseModel):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    registration_no = models.CharField(max_length=255, null=True, blank=True)
    is_active = models.BooleanField(default=False)
    verification_response = models.JSONField()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY VERIFICATION METADATA"
        verbose_name_plural = "COMPANY VERIFICATION METADATA"

    @property
    def response_data(self):
        verification_response_str = json.dumps(self.verification_response)
        return truncatechars(verification_response_str, 20)


class BudgetAllocation(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    budget = models.ForeignKey(Budget, on_delete=models.SET_NULL, null=True, blank=True)
    team = models.ForeignKey(Team, on_delete=models.SET_NULL, null=True, blank=True)
    amount = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    is_active = models.BooleanField(default=True)
    transferred_balance = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "BUDGET ALLOCATION"
        verbose_name_plural = "BUDGET ALLOCATIONS"


class UserLinkedBanks(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    account_id = models.CharField(max_length=200, blank=True, null=True)
    name = models.CharField(max_length=200, blank=True, null=True)
    account_type = models.CharField(max_length=200, blank=True, null=True)
    account_number = models.CharField(max_length=200, blank=True, null=True)
    balance = models.CharField(max_length=200, blank=True, null=True)
    bank_name = models.CharField(max_length=200, blank=True, null=True)
    bank_code = models.CharField(max_length=200, blank=True, null=True)
    auth_method = models.CharField(max_length=200, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    payload = models.TextField(blank=True, null=True)
    has_details = models.BooleanField(default=False)

    class Meta:
        verbose_name = "USER LINKED BANK"
        verbose_name_plural = "USER LINKED BANKS"

    def __str__(self):
        return str(self.id)


class MonoTopUpRecord(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True)
    account_id = models.ForeignKey(UserLinkedBanks, on_delete=models.CASCADE, null=True, blank=True)
    event = models.CharField(max_length=200, blank=True, null=True)
    institution = models.CharField(max_length=100, blank=True, null=True)
    account_no = models.CharField(max_length=20, blank=True, null=True)
    user_name = models.CharField(max_length=200, blank=True, null=True)
    acct_type = models.CharField(max_length=50, blank=True, null=True)
    currency = models.CharField(max_length=12, blank=True, null=True)
    balance = models.CharField(max_length=12, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    payload = models.TextField()

    class Meta:
        verbose_name = "MONO TOP UP RECORD"
        verbose_name_plural = "MONO TOP UP RECORD"

    def __str__(self):
        return str(self.id)


class TeamMemberInvite(models.Model):
    INVITE_STATUS = (
        ("PENDING", "PENDING"),
        ("APPROVED", "APPROVED"),
        ("REJECTED", "REJECTED"),
    )
    email = models.EmailField(null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True)
    first_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100, blank=True, null=True)
    status = models.CharField(max_length=200, choices=INVITE_STATUS, default="PENDING")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "TEAM MEMBER INVITATION"
        verbose_name_plural = "TEAM MEMBER INVITATIONS"

    def __str__(self):
        return str(self.email)


# PROCUREMENT MODELS HERE
class IndentProduct(BaseModel):
    product = models.ForeignKey("stock_inventory.Product", on_delete=models.SET_NULL, blank=True, null=True)
    estimated_amount = models.FloatField(default=0)
    quantity = models.IntegerField(default=0)
    delivered = models.BooleanField(default=False)
    received = models.IntegerField(default=0)


class PurchaseIndent(BaseModel):
    team = models.ForeignKey(Team, on_delete=models.CASCADE)
    products = models.ManyToManyField(IndentProduct)
    indent_no = models.CharField(max_length=150, blank=True, null=True)
    approval_status = models.CharField(
        max_length=50,
        choices=PURCHASE_INDENT_APPROVAL_TYPE_CHOICES,
        default="PENDING_APPROVAL",
    )
    requested_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="pi_requested_by",
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="pi_approved_by",
    )
    declined_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="pi_declined_by",
    )
    supplier = models.ForeignKey("stock_inventory.Supplier", on_delete=models.SET_NULL, blank=True, null=True)
    payment_term = models.CharField(max_length=50, choices=PURCHASE_PAYMENT_TERMS_CHOICES, default="ON_DELIVERY")
    delivery_address = models.CharField(max_length=200, blank=True, null=True)
    decline_reason = models.CharField(max_length=300, blank=True, null=True)
    estimated_cost = models.FloatField(default=0)
    instant_request = models.BooleanField(default=False)
    expected_date_of_delivery = models.DateTimeField(blank=True, null=True)
    actual_date_of_delivery = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"{self.team.team_name}: {self.created_at} - {self.approval_status}"


class ProcurementPurchaseOrder(BaseModel):
    indent = models.OneToOneField(PurchaseIndent, on_delete=models.CASCADE)
    order_no = models.CharField(max_length=150, blank=True, null=True)
    initial_cost = models.FloatField(default=0)
    accepted_cost = models.FloatField(default=0)
    status = models.CharField(max_length=50, choices=PURCHASE_ORDER_STATUS_CHOICES, default="UNDER_REVIEW")
    delivery_status = models.CharField(max_length=50, choices=PURCHASE_DELIVERY_MODE_CHOICES, default="NOT_DELIVERED")
    decline_reason = models.CharField(max_length=300, blank=True, null=True)
    supplier_fulfilled = models.BooleanField(default=False)
    otp_phone_no = models.CharField(max_length=20, blank=True, null=True)
    code_expiry = models.DateTimeField(blank=True, null=True)
    otp_text = models.TextField(blank=True, null=True)
    confirm_delivery_date = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"{self.indent.id}"


class ProcurementEscrow(BaseModel):
    team = models.OneToOneField(Team, on_delete=models.CASCADE)
    balance = models.FloatField(default=0)

    def __str__(self):
        return f"{self.team.company.company_name} - {self.team.team_name}: {self.balance}"


class PurchaseOrderComment(BaseModel):
    purchase_order = models.ForeignKey(ProcurementPurchaseOrder, on_delete=models.CASCADE)
    sender = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="po_comment_creator",
    )
    parent_comment = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        related_name="po_parent_comment",
        blank=True,
        null=True,
    )
    message = models.TextField()
    edited_at = models.DateTimeField(blank=True, null=True)


def order_number() -> str:
    digits = "".join(random.choices(string.digits, k=6))
    letters = "".join(random.choices(string.ascii_uppercase, k=2))
    order_num = letters + digits
    return order_num


world_currencies = {}


def get_currencies():
    uri = "https://openexchangerates.org/api/currencies.json"
    response = requests.get(uri)
    if response.status_code != 200:
        return {"error": "Could not get results"}
    currencies = response.json()
    for key, value in currencies.items():
        world_currencies[key] = value


WORLD_CURRENCIES = tuple(world_currencies.items())


class ProcurementPurchaseInvoice(BaseModel):
    class ApprovalStatus(models.TextChoices):
        PENDING = "PENDING", "Pending"
        IN_PROGRESS = "IN_PROGRESS", "In progrss"
        FUllFILLED = "FULLFILLED", "Fullfilled"

    class ProcurementType(models.TextChoices):
        FOREIGN = "FOREIGN", "Foreign"
        LOCAL = "LOCAL", "Local"

    class PaidOption(models.TextChoices):
        PENDING = "PENDING", "Pending"
        PAID = "PAID", "Paid"
        PARTLY_PAID = "PARTLY_PAID", "Partly_paid"
        FUllFILLED = "FULLFILLED", "Fullfilled"

    class PaymentTermsStatus(models.TextChoices):
        PARTIAL_UPFRONT = "PARTIAL_UPFRONT", "Partial Upfront"
        FULL_UPFRONT = "FULL_UPFRONT", "Full Upfront"
        ON_DELIVERY = "ON_DELIVERY", "On Delivery"
        AFTER_DELIVERY = "AFTER_DELIVERY", "After Delivery"

    WORLD_CURRENCIES = WORLD_CURRENCIES
    # Tuple Constants #####

    po_invoice_number = models.CharField(max_length=11, default=order_number)
    document_date = models.DateTimeField(default=timezone.now)
    Due_date = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=ApprovalStatus.choices, default=ApprovalStatus.PENDING)
    purchase_order = models.ForeignKey(ProcurementPurchaseOrder, on_delete=models.CASCADE)
    pi_requester = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="purch_invoice_requested_by",
    )
    pi_approver = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="purch_invoice_approved_by",
    )
    procured_for = models.ForeignKey(
        Team,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="procured_for",
    )
    vendor = models.ForeignKey("stock_inventory.Supplier", on_delete=models.CASCADE, blank=True, null=False)
    procurement_type = models.CharField(max_length=8, choices=ProcurementType.choices, default=ProcurementType.LOCAL)
    no_of_items = models.PositiveBigIntegerField(verbose_name="No of item invoiced", default=1)
    no_shipped = models.PositiveIntegerField("No of items to be shipped", default=1)
    shipping_fee = models.FloatField(default=0.0)
    vat = models.FloatField(default=7.5, null=True, blank=True, validators=[MaxValueValidator(100)])
    hard_copy_upload = models.FileField(upload_to="purchase_invoices", blank=True, null=True)
    accepted = models.BooleanField(default=False)
    allocation_balance = models.ForeignKey(ProcurementEscrow, on_delete=models.CASCADE, null=True, blank=True)
    paid = models.BooleanField(default=False)
    total = models.FloatField(default=0.0)
    terms_and_conditions = models.CharField(max_length=100, default="...", blank=True, null=True)
    amount_paid = models.FloatField(default=0.00)
    received_date = models.DateField(blank=True, null=True)
    payment_terms = models.CharField(
        max_length=100,
        choices=PaymentTermsStatus.choices,
        default=PaymentTermsStatus.ON_DELIVERY,
    )
    payment_status = models.CharField(max_length=100, choices=PaidOption.choices, default=PaidOption.PENDING)
    note = models.TextField(blank=True, null=True)

    def subtotal(self):
        return self.purchase_order.accepted_cost

    def get_payment_terms(self):
        self.payment_terms = self.purchase_order.indent.payment_term
        return self.payment_terms

    def calculate_total(self, *args, **kwargs):
        accepted_cost = self.purchase_order.accepted_cost or 0.00
        shipping_fee = self.shipping_fee or 0.00
        self.total = (accepted_cost * (self.vat / 100)) + accepted_cost + shipping_fee

        super(ProcurementPurchaseInvoice, self).save(*args, **kwargs)
        return self.total

    def items(self):
        return self.purchase_order.indent.products.all()

    def __str__(self):
        return self.po_invoice_number


class ProcurementPurchaseInvoiceUploadedFIles(BaseModel):
    invoice_no = models.CharField(max_length=20, blank=True, null=True)
    file = models.FileField(upload_to="purchase_invoices", blank=True, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return self.invoice_no


class Notes(BaseModel):
    invoice_no = models.CharField(max_length=20, blank=True, null=False)
    notes = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)

    def __str__(self) -> str:
        return self.invoice_no


class Flags(BaseModel):
    invoice_no = models.CharField(max_length=20, blank=True, null=False)
    no_of_items_flag = models.CharField(max_length=100, blank=True, null=True)
    total_flag = models.CharField(max_length=100, blank=True, null=True)

    def __str__(self) -> str:
        return self.invoice_no


class JotFormDataSync(BaseModel):
    PROCESSING_REQUEST_STATUS = [
        ("DATA_RECEIVED", "DATA_RECEIVED"),
        ("REQUEST_DATA_FORMATTED", "REQUEST_DATA_FORMATTED"),
        (
            "FAILED_TO_SERIALISED_USER_SUBMITTED_DATA",
            "FAILED_TO_SERIALISED_USER_SUBMITTED_DATA",
        ),
        ("USER_ALREADY_EXIST", "USER_ALREADY_EXIST"),
        (
            "FAILED_TO_CREATE_USE_ON_AGENCY_BANKING",
            "FAILED_TO_CREATE_USE_ON_AGENCY_BANKING",
        ),
        ("USER_PROFILE_CREATED", "USER_PROFILE_CREATED"),
        ("COMPANY_ALREADY_EXIST", "COMPANY_ALREADY_EXIST"),
    ]

    PURPOSE = [
        ("COMPANY_ONBOARDING", "COMPANY_ONBOARDING"),
        ("USSD_CAMPAIGN", "USSD_CAMPAIGN"),
    ]
    data = models.TextField()
    request_id = models.CharField(max_length=400, unique=True)
    serialised_data = models.TextField(blank=True, null=True)
    processing_request_status = models.CharField(
        max_length=300, choices=PROCESSING_REQUEST_STATUS, default="DATA_RECEIVED"
    )
    agency_banking_response = models.TextField(blank=True, null=True)
    temp_transaction_pin = models.CharField(max_length=100, blank=True, null=True)
    temp_passcode = models.CharField(max_length=100, blank=True, null=True)
    company_details_created = models.BooleanField(default=False)
    has_set_passcode_on_agency_banking = models.BooleanField(default=False)
    full_name = models.CharField(max_length=300, blank=True, null=True)
    email = models.CharField(max_length=300, blank=True, null=True)
    bvn = models.CharField(max_length=300, blank=True, null=True)
    gender = models.CharField(max_length=300, blank=True, null=True)
    state = models.CharField(max_length=300, blank=True, null=True)
    lga = models.CharField(max_length=300, blank=True, null=True)
    nearest_landmark = models.CharField(max_length=300, blank=True, null=True)
    street = models.CharField(max_length=300, blank=True, null=True)
    business_name = models.CharField(max_length=300, blank=True, null=True)
    company_size = models.CharField(max_length=300, blank=True, null=True)
    industry = models.CharField(max_length=300, blank=True, null=True)
    type_of_entity = models.CharField(max_length=300, blank=True, null=True)
    registration_number = models.CharField(max_length=300, blank=True, null=True)
    directors_first_name = models.CharField(max_length=300, blank=True, null=True)
    directors_last_name = models.CharField(max_length=300, blank=True, null=True)
    directors_date_of_birth = models.CharField(max_length=300, blank=True, null=True)
    sales_rep_name = models.CharField(max_length=300, blank=True, null=True)
    sales_rep_email = models.CharField(max_length=300, blank=True, null=True)
    purpose = models.CharField(
        max_length=100,
        choices=PURPOSE,
        null=True,
        blank=True,
        default="COMPANY_ONBOARDING",
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "JOT FORM DATA SYNC"
        verbose_name_plural = "JOT FORM DATA SYNCS"

    @classmethod
    def generate_unique_username(cls, first_name):
        # Get the current timestamp in a compact format
        timestamp = int(datetime.now().timestamp())  # This gives a unique number based on the current time
        random_string = "".join(random.choices(string.ascii_lowercase + string.digits, k=4))  # 4 random characters

        # Create the base username
        base_username = f"{first_name[:10]}{timestamp}{random_string}"  # Limit first_name to 10 characters

        # Ensure the username is below 26 characters
        if len(base_username) > 25:
            base_username = base_username[:25]  # Truncate if necessary

        return base_username

    @classmethod
    def process_datasync(cls, data):

        from core.tasks import send_email
        from payroll_app.models import CompanyEmployeeList
        from payroll_app.services import EmployeeOnboarding

        request_id = str(uuid.uuid4())
        instance = cls.objects.create(request_id=request_id, data=data)

        query_dict = cls.querydict_to_dict(data)
        instance.serialised_data = query_dict
        instance.processing_request_status = "REQUEST_DATA_FORMATTED"
        instance.save()

        if not isinstance(query_dict, dict):
            return "Not formatted to a dictonary"

        # print(f"""
        #         query_dict: {query_dict}
        #         \n\n\n\n
        #     """)

        form_data = query_dict.get("pretty")
        # print(f"""
        #         form_data: {form_data}
        #         \n\n\n\n
        #     """)
        serialised_form_data = cls.pretty_to_dict(form_data)

        # print(f"""
        #     serialised_form_data: {serialised_form_data}
        #     \n\n
        #     """)

        if not isinstance(serialised_form_data, dict):
            print(serialised_form_data)
            instance.processing_request_status = "FAILED_TO_SERIALISED_USER_SUBMITTED_DATA"
            instance.save()

        phone_number = serialised_form_data.get("Phone Number")
        if phone_number != None:
            phone_number = str(phone_number).strip()
            phone_number = f"234{phone_number[-10:]}"

        full_name = serialised_form_data.get("Name")
        email = str(serialised_form_data.get("Email")).lower()
        bvn = serialised_form_data.get("BVN")
        gender = serialised_form_data.get("Gender")
        state = serialised_form_data.get("State")
        lga = serialised_form_data.get("LGA")
        nearest_landmark = serialised_form_data.get("Nearest landmark")
        street = serialised_form_data.get("Street")
        business_name = serialised_form_data.get("Business name")
        if business_name != None:
            business_name = str(business_name).title()

        company_size = serialised_form_data.get("Company size", 1)
        company_size = int(company_size)

        industry = serialised_form_data.get("Industry")
        type_of_entity = serialised_form_data.get("Type of entity")
        registration_number = serialised_form_data.get("Registration number")
        directors_first_name = serialised_form_data.get("Director's Firstname")
        directors_last_name = serialised_form_data.get("Director's Lastname")
        directors_date_of_birth = serialised_form_data.get("Director's Date of birth")
        company_registration_document = serialised_form_data.get("Company registration document")
        sales_rep_name = serialised_form_data.get("Sales Rep Name")
        sales_rep_email = serialised_form_data.get("Sales Rep Email")

        instance.full_name = full_name
        instance.email = email
        instance.bvn = bvn
        instance.gender = gender
        instance.state = state
        instance.lga = lga
        instance.nearest_landmark = nearest_landmark
        instance.street = street
        instance.business_name = business_name
        instance.company_size = company_size
        instance.industry = industry
        instance.type_of_entity = type_of_entity
        instance.registration_number = registration_number
        instance.directors_first_name = directors_first_name
        instance.directors_last_name = directors_last_name
        instance.sales_rep_email = sales_rep_email
        instance.sales_rep_name = sales_rep_name
        instance.save()

        if directors_date_of_birth != None:

            try:
                directors_date_of_birth = datetime.strptime(directors_date_of_birth, "%m %d %Y").date()
            except:
                directors_date_of_birth = datetime.now().date()

        else:
            directors_date_of_birth = datetime.now().date()

        wallet_type = "CORPORATE"

        ########### CREATE USER

        # check if user exists

        # payload for creatiing user on agency banking
        user_name = phone_number[-10:]
        first_name = full_name
        last_name = full_name

        if full_name != None:
            full_name = str(full_name).replace("-", " ")
            splitted_name = str(full_name).split(" ")
            user_name = f"{splitted_name[0]}.{phone_number[-3:]}.{int(datetime.now().time())}"
            first_name = splitted_name[0]
            last_name = splitted_name[-1]

        # Temp added
        first_name = serialised_form_data.get("Firstname")
        last_name = serialised_form_data.get("Lastname")

        first_name = str(first_name).replace("-", " ")
        last_name = str(last_name).replace("-", " ")

        # user_time = datetime.now().time()
        # user_name = f"{first_name}{user_time.hour}{user_time.second}"

        user_name = cls.generate_unique_username(first_name)

        # user_name = f"{first_name}.{phone_number[-3:]}.{int(datetime.now().time())}"

        first_name_list = str(first_name).split(" ")
        first_name = first_name_list[0]
        first_name_list = str(first_name).split(",")
        first_name = first_name_list[0]

        last_name_list = str(last_name).split(" ")
        last_name = last_name_list[0]
        last_name_list = str(last_name).split(",")
        last_name = last_name_list[0]

        agency_banking_user_creation_payload = {
            "phone_number": phone_number,
            "username": user_name,
            "first_name": first_name,
            "last_name": last_name,
            "email": email,
            "state": state,
            "lga": lga,
            "nearest_landmark": nearest_landmark,
            "street": street,
            "gender": gender,
            "device_type": "WEB",
        }

        agency_data = CompanyEmployeeList.agency_banking_onboarding(**agency_banking_user_creation_payload)

        if not agency_data.get("success"):
            instance.processing_request_status = "FAILED_TO_CREATE_USE_ON_AGENCY_BANKING"
            instance.agency_banking_response = agency_data.get("data")
            instance.save()

            try:
                if "already exists" in agency_data.get("data", {}).get("data", {}).get("message"):
                    pass
                else:
                    return "FAILED TO CREATE USE ON AGENCY BANKING"
            except:

                return "FAILED TO CREATE USE ON AGENCY BANKING"
        else:
            AdGeneneratedUsers.objects.filter(phone_number=phone_number).update(libertypay_account_created=True)

        try:
            user_instance = User.objects.get(email=email)
            instance.processing_request_status = "USER_ALREADY_EXIST"
            instance.save()

        except User.DoesNotExist:
            user_instance, created = User.objects.get_or_create(
                email=email,
                defaults={
                    "phone_no": phone_number,
                    "first_name": first_name,
                    "last_name": last_name,
                    "state": state,
                    "lga": lga,
                    "nearest_landmark": nearest_landmark,
                    "street": street,
                    "gender": gender,
                    "bvn_number": bvn,
                },
            )

            AdGeneneratedUsers.objects.filter(phone_number=phone_number).update(paybox360_account_created=True)

        instance.processing_request_status = "USER_PROFILE_CREATED"
        instance.save()

        transaction_pin = "".join(str(random.randint(1, 9)) for _ in range(4))
        pass_code = "".join(str(random.randint(1, 9)) for _ in range(6))

        instance.temp_passcode = pass_code
        instance.temp_transaction_pin = transaction_pin
        instance.save()

        # create company payload
        company_payload = {
            "company_name": business_name,
            "industry": industry,
            "cac_num": registration_number,
            "size": company_size,
            "wallet_type": wallet_type,
        }

        if not Company.objects.filter(user__id=user_instance.id).exists():
            Company.create_company(
                user=user_instance,
                validated_data=company_payload,
                transaction_pin=transaction_pin,
            )

            CompanyVerificationInfo.objects.create(
                user=user_instance,
                registration_no=registration_number,
                type_of_entity=type_of_entity,
                director_date_of_birth=directors_date_of_birth,
                is_active=True,
                verification_response=serialised_form_data,
            )

            instance.company_details_created = True
            instance.save()

            AdGeneneratedUsers.objects.filter(phone_number=phone_number).update(company_details_created=True)

        # create passcode on agency banking
        phone = f"0{phone_number[-10:]}"
        passcode_agency_banking_response = (
            EmployeeOnboarding.create_passcode_for_user_on_agency_banking_and_activate_user_email(
                passcode=pass_code, phone_number=phone
            )
        )

        # print("passcode_agency_banking_response", passcode_agency_banking_response)
        
        
        send_email(
            recipient="<EMAIL>",
            subject="Company onboarded successfully",
            template_dir="jotform_onboarding_email.html",
            use_template=True,
            user_name="Sales lead",
            company=business_name,
            email=email,
            passcode=pass_code,
            trans_pin=transaction_pin,
        )
        

        if passcode_agency_banking_response.get("error") == False:
            instance.has_set_passcode_on_agency_banking = True
            instance.save()

            # send congratulating email to user on account setup, with login details
            send_email(
                recipient=email,
                subject="Company onboarded successfully",
                template_dir="jotform_onboarding_email.html",
                use_template=True,
                user_name=user_instance.first_name,
                company=business_name,
                email=email,
                passcode=pass_code,
                trans_pin=transaction_pin,
            )

            if sales_rep_email != None:
                send_email(
                    recipient=sales_rep_email,
                    subject="Company onboarded successfully",
                    template_dir="jotform_onboarding_email.html",
                    use_template=True,
                    user_name=sales_rep_name,
                    company=business_name,
                    email=email,
                    passcode=pass_code,
                    trans_pin=transaction_pin,
                )

            send_email(
                recipient="<EMAIL>",
                subject="Company onboarded successfully",
                template_dir="jotform_onboarding_email.html",
                use_template=True,
                user_name="Sales lead",
                company=business_name,
                email=email,
                passcode=pass_code,
                trans_pin=transaction_pin,
            )

            

        return "Done"

    @classmethod
    def pretty_to_dict(cls, pretty_str):
        # Remove the surrounding brackets and split into items
        pretty_data = pretty_str.strip("[]")  # Remove the outer brackets
        items = pretty_data.split(", ")  # Split the string into key-value pairs

        # Create a dictionary from the items
        formatted_data = {}
        for item in items:
            # Split on the first colon and handle potential errors
            if ":" in item:
                key, value = item.split(":", 1)  # Split on the first colon
                formatted_data[key.strip()] = value.strip()  # Strip whitespace
            else:
                # Log or handle the malformed item as needed
                print(f"Skipping malformed item: {item}")

        cleaned_data = {key.replace('"', "").strip(): value for key, value in formatted_data.items()}
        return cleaned_data

    @classmethod
    def querydict_to_dict(cls, query_dict):
        """
        Converts a QueryDict into a dictionary, decoding JSON strings if needed.
        """
        result = {}
        for key, value_list in query_dict.lists():
            # Access the first value (assuming single-value QueryDict entries)
            raw_value = value_list[0]
            try:
                # Attempt to parse as JSON
                parsed_value = json.loads(raw_value)
                result[key] = parsed_value
            except (json.JSONDecodeError, TypeError):
                # If not JSON, store the raw value
                result[key] = raw_value
        return result

    @classmethod
    def process_datasync_for_ussd_campaign(cls, data):

        request_id = str(uuid.uuid4())
        instance = cls.objects.create(request_id=request_id, data=data, purpose="USSD_CAMPAIGN")

        query_dict = cls.querydict_to_dict(data)
        instance.serialised_data = query_dict
        instance.processing_request_status = "REQUEST_DATA_FORMATTED"
        instance.save()

        if not isinstance(query_dict, dict):
            return "Not formatted to a dictonary"

        form_data = query_dict.get("pretty")

        serialised_form_data = cls.pretty_to_dict(form_data)

        if not isinstance(serialised_form_data, dict):
            print(serialised_form_data)
            instance.processing_request_status = "FAILED_TO_SERIALISED_USER_SUBMITTED_DATA"
            instance.save()

        full_name = serialised_form_data["Name"]
        first_name = str(full_name).split(" ")[0]
        last_name = str(full_name).split(" ")[-1]
        email = serialised_form_data["Email"]
        phone_number = serialised_form_data["Phone Number"]
        owned_a_company = serialised_form_data["Owned a company?"]

        _owned_a_compnay = True if str(owned_a_company).lower() == "yes" else False

        phone = f"234{phone_number[-10:]}"

        AdGeneneratedUsers.objects.filter(phone_number=phone).update(
            first_name=first_name,
            last_name=last_name,
            email=email,
            jot_form_data_submitted=True,
        )


class ProcurementReturnProduct(BaseModel):
    indent_product = models.ForeignKey(IndentProduct, on_delete=models.CASCADE)
    quantity = models.IntegerField(default=0)
    image = models.ImageField(upload_to="return-images")
    return_reason = models.CharField(max_length=100, choices=RETURN_REASON_CHOICES, default="DEFECTED")


class ProcurementReturn(BaseModel):
    purchase_order = models.ForeignKey(ProcurementPurchaseOrder, on_delete=models.CASCADE)
    return_no = models.CharField(max_length=200, blank=True, null=True)
    return_products = models.ManyToManyField(ProcurementReturnProduct)
    description = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=50, choices=PURCHASE_ORDER_STATUS_CHOICES, default="UNDER_REVIEW")


class PurchaseIndentHistory(BaseModel):
    indent = models.ForeignKey(PurchaseIndent, on_delete=models.CASCADE)
    detail = models.CharField(max_length=300)


class ProcurementCreditNote(BaseModel):
    procurement_return = models.OneToOneField(ProcurementReturn, on_delete=models.CASCADE)
    total = models.FloatField(default=0)
    used = models.BooleanField(default=False)


class AdGeneneratedUsers(BaseModel):
    phone_number = models.CharField(max_length=100, unique=True)
    first_name = models.CharField(max_length=300, blank=True, null=True)
    last_name = models.CharField(max_length=300, blank=True, null=True)
    age = models.CharField(max_length=100, blank=True, null=True)
    email = models.CharField(max_length=300, blank=True, null=True)
    jot_form_link_sent = models.BooleanField(default=False)
    jot_form_data_submitted = models.BooleanField(default=False)
    has_a_company = models.BooleanField(default=False)
    last_activity = models.CharField(max_length=100, choices=AD_GENERATED_USER_ACTIVITY, default="JOTFORM_SENT")
    libertypay_account_created = models.BooleanField(default=False)
    paybox360_account_created = models.BooleanField(default=False)
    company_details_created = models.BooleanField(default=False)
    channel = models.CharField(max_length=100, choices=AD_GENERATED_USERS_CHANNEL, default="USSD")
    source = models.CharField(max_length=100, choices=AD_GENERATED_USER_SOURCE, default="BRT")
    completed_sms_sequence = models.BooleanField(default=False)
    added_to_brevo_contact_list = models.BooleanField(default=False)
    paybox_agent_phone_number = models.CharField(max_length=100, blank=True, null=True)
    paybox_agent_name = models.CharField(max_length=100, blank=True, null=True)
    is_assigned = models.BooleanField(default=False)
    whatsapp_message_sent = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "AD GENERATED USERS"
        verbose_name_plural = "AD GENERATED USERS"

    @classmethod
    def create_record(cls, **kwards):

        from requisition.tasks import assign_leads_to_paybox_staff

        phone_number = kwards.get("phone_number")
        activity = kwards.get("activity")
        channel = kwards.get("channel", "USSD")
        source = kwards.get("campaign_source", "BRT")

        if phone_number == None:
            return

        try:
            instance = cls.objects.get(phone_number=phone_number)
        except cls.DoesNotExist:
            instance = cls.objects.create(phone_number=phone_number, source=source)

        # assign_leads_to_paybox_staff.delay(phone_number)

        try:
            assign_leads_to_paybox_staff(phone_number)
        except:
            pass

        instance.refresh_from_db()

        jot_form_link_sent = kwards.get("jot_form_link_sent", instance.jot_form_link_sent)
        jot_form_data_submitted = kwards.get("jot_form_data_submitted", instance.jot_form_data_submitted)

        instance.last_activity = activity
        instance.jot_form_link_sent = jot_form_link_sent
        instance.jot_form_data_submitted = jot_form_data_submitted
        instance.save()

        AdGeneneratedUserActivity.objects.create(
            phone_number=phone_number,
            first_name=instance.first_name,
            last_name=instance.last_name,
            activity=activity,
            channel=channel,
            email=instance.email,
            source=source,
            paybox_agent_phone_number=instance.paybox_agent_phone_number,
            paybox_agent_name=instance.paybox_agent_name,
        )

    @classmethod
    def update_record(cls, **kwards):
        phone_number = kwards.get("phone_number")
        full_name = kwards.get("full_name")
        email = kwards.get("email")

        if phone_number == None:
            return

        try:
            instance = cls.objects.get(phone_number=phone_number)
        except cls.DoesNotExist:
            return

        if full_name != None:
            name_split = str(full_name).split(" ")
            first_name = name_split[0]
            instance.first_name = first_name

            if len(name_split) > 1:
                last_name = name_split[-1]
                instance.last_name = last_name

        if email != None:
            instance.email = email

        instance.save()

        return

    @classmethod
    def update_whatsapp_conversation(cls, **kwards):
        phone_number = kwards.get("phone_number")
        book_a_demo = kwards.get("book_a_demo", False)
        start_a_fre_trial = kwards.get("start_a_fre_trial", False)
        download_brochure = kwards.get("download_brochure", False)
        hr = kwards.get("hr", False)
        spend_management = kwards.get("spend_management", False)
        sales_module = kwards.get("sales_module", False)
        user_name = kwards.get("user_name")
        user_email = kwards.get("user_email")
        speak_to_an_agent = kwards.get("speak_to_an_agent", False)

        try:
            user_instance = cls.objects.get(phone_number=phone_number)
        except cls.DoesNotExist:
            user_instance = cls.objects.create(phone_number=phone_number, source="WHATSAPP")

        if user_email != None:
            if user_instance.email == None:
                user_instance.email = user_email

        if user_name != None:
            if user_instance.first_name == None:
                user_instance.first_name = user_name

        user_instance.save()

        activities = [
            ("BOOK_A_DEMO", book_a_demo),
            ("START_A_FREE_TRIAL", start_a_fre_trial),
            ("SPEAK_WITH_AN_ADVISOR", speak_to_an_agent),
            ("DOWNLOAD_A_BROCHURE", download_brochure),
            ("INTERESTED_IN_HR_MODULE", hr),
            ("INTERESTED_IN_SPEND_MANAGEMENT_MODULE", spend_management),
            ("INTERESTED_IN_SALES_MODULE", sales_module),
        ]

        for activity, condition in activities:
            if condition:
                AdGeneneratedUserActivity.objects.create(
                    phone_number=phone_number,
                    activity=activity,
                    channel="WHATSAPP",
                    source="WHATSAPP",
                    first_name=user_instance.first_name,
                    last_name=user_instance.last_name,
                    email=user_instance.email,
                    paybox_agent_phone_number=user_instance.paybox_agent_phone_number,
                    paybox_agent_name=user_instance.paybox_agent_name,
                )


class AdGeneneratedUserActivity(BaseModel):
    phone_number = models.CharField(max_length=100)
    first_name = models.CharField(max_length=300, blank=True, null=True)
    last_name = models.CharField(max_length=300, blank=True, null=True)
    email = models.CharField(max_length=300, blank=True, null=True)
    activity = models.CharField(max_length=100, choices=AD_GENERATED_USER_ACTIVITY)
    channel = models.CharField(max_length=100, choices=AD_GENERATED_USERS_CHANNEL)
    source = models.CharField(max_length=100, choices=AD_GENERATED_USER_SOURCE, default="BRT")
    paybox_agent_phone_number = models.CharField(max_length=100, blank=True, null=True)
    paybox_agent_name = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "AD GENERATED USER ACTIVITY"
        verbose_name_plural = "AD GENERATED ACTIVITIES"


class AdSmsSequence(BaseModel):
    AD_SMS_SEQUENCE_PURPOSE = [("FILL_JOTFORM", "FILL_JOTFORM")]

    phone_number = models.CharField(max_length=100)
    purpose = models.CharField(max_length=100, choices=AD_SMS_SEQUENCE_PURPOSE, default="FILL_JOTFORM")

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "AD SMS SEQUENCE"
        verbose_name_plural = "AD SMS SEQUENCE"


class AssetImage(BaseModel):
    team = models.ForeignKey(Team, on_delete=models.CASCADE)
    image = models.TextField(blank=True, null=True)


class Asset(BaseModel):
    product = models.ForeignKey("stock_inventory.Product", on_delete=models.CASCADE)
    indent_product = models.ForeignKey(IndentProduct, on_delete=models.SET_NULL, blank=True, null=True)
    team = models.ForeignKey(
        Team,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="asset_team",
    )
    supplier = models.ForeignKey("stock_inventory.Supplier", on_delete=models.SET_NULL, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    assigned_to = models.ForeignKey(TeamMember, on_delete=models.CASCADE, blank=True, null=True)
    asset_category = models.CharField(max_length=200, choices=ASSET_CATEGORY_CHOICES, default="others")
    images = models.ManyToManyField(AssetImage)
    warranty_expiry_date = models.DateTimeField(blank=True, null=True)
    asset_no = models.CharField(max_length=300, blank=True, null=True)
    purchase_cost = models.FloatField(default=0)
    purchase_date = models.DateTimeField(blank=True, null=True)
    depreciation_method = models.CharField(
        max_length=200,
        choices=ASSET_DEPRECIATION_METHOD_CHOICES,
        default="STRAIGHT_LINE",
    )
    useful_life = models.PositiveIntegerField(
        default=1, help_text="Number of asset months for depreciation calculation"
    )
    residual_value = models.FloatField(default=0)
    capex_amount = models.FloatField(default=0)
    net_income = models.FloatField(default=0, blank=True, null=True)
    depreciated = models.BooleanField(default=False)
    depreciation_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0.0)
    total_estimated_units = models.PositiveIntegerField(
        help_text="Only used for UNIT_OF_PRODUCTION", null=True, blank=True
    )
    units_produced = models.PositiveIntegerField(
        help_text="This is units produced annually. Only used for UNIT_OF_PRODUCTION",
        default=0,
    )

    def __str__(self):
        return str(self.product.name)


class TempPayboxStaffData(BaseModel):
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField()
    phone_number = models.CharField(max_length=20)
    is_available = models.BooleanField(default=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "TEMP PAYBOX STAFF DATA"
        verbose_name_plural = "TEMP PAYBOX STAFF DATA"

    def __str__(self):
        return self.first_name


class AssetExpense(BaseModel):
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE)
    amount = models.FloatField(default=0)
    expense_type = models.CharField(max_length=100, choices=ASSET_EXPENSES_CATEGORY, default="OPERATIONS")
    note = models.TextField(blank=True, null=True)
    added_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="asset_expense_creator")
    edited_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="asset_expense_editor")

    def __str__(self):
        return self.asset_id
