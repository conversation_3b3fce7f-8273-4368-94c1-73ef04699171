import json
import requests
import logging
import re
import uuid
from calendar import month_name
from collections import defaultdict
from datetime import date, timedelta, datetime

from dateutil.parser import parse
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import check_password, make_password
from django.contrib.auth.password_validation import validate_password
from django.core.validators import MinLengthValidator, MaxValueValidator
from django.db.models import Count, Q, Sum
from django.utils import timezone
from django.utils.crypto import get_random_string
from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied

from account.models import AccountSystem, DebitCreditRecordOnAccount, Wallet
from core.exceptions import InvalidRequestException
from core.helpers.func import calculate_percentage, is_valid_date_format
from core.models import ConstantTable
from requisition.helpers.enums import UserRole
from requisition.helpers.func import find_duplicate_email
from requisition.tasks import (
    insufficient_balance_notification_on_requisition,
    perform_bulk_requisition,
)
from stock_inventory.models import Product, StockDetail, Supplier
from core.tasks import send_email, upload_file_aws_s3_bucket
from subscription_and_invoicing.reuseable import SubscriptionService

from .models import (
    Budget,
    BudgetAllocation,
    Category,
    Comment,
    Company,
    CompanyIndustry,
    CompanyVerificationInfo,
    Expense,
    Flags,
    Requisition,
    Team,
    TeamMember,
    TeamMemberInvite,
    PurchaseIndent,
    IndentProduct,
    PURCHASE_PAYMENT_TERMS_CHOICES,
    PURCHASE_INDENT_APPROVAL_TYPE_CHOICES,
    ProcurementPurchaseOrder,
    ProcurementEscrow,
    PURCHASE_ORDER_STATUS_CHOICES,
    SUPPLIER_ADDITION_CHOICES,
    PurchaseOrderComment,
    ProcurementPurchaseInvoice,
    PURCHASE_DELIVERY_MODE_CHOICES,
    DELIVERY_WITH_RETURNS_CHOICES,
    ProcurementReturnProduct,
    ProcurementReturn,
    RETURN_REASON_CHOICES,
    PurchaseIndentHistory,
    ProcurementPurchaseInvoiceUploadedFIles,
    ProcurementCreditNote,
    SUPPLIER_UPDATE_CHOICES,
    Notes,
    Asset,
    ASSET_DEPRECIATION_METHOD_CHOICES,
    ASSET_CATEGORY_CHOICES,
    AssetImage,
    ASSET_EXPENSES_CATEGORY,
    AssetExpense,
)
from .utils import (
    create_ident_products,
    purchase_ident_with_budget_check,
    extract_from_excel_or_csv,
    add_retrieve_procurement_history,
    format_phone_number,
    send_fund_to_vendor_bank,
    is_supplier_check,
    onboard_supplier,
)

import tempfile, os
from pytesseract import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import spacy

nlp = spacy.load("en_core_web_sm")

logger = settings.LOGGER
User = get_user_model()
login_url = f"{settings.SUPPLIER_INVITE_FRONTEND_URL}/login"


# Company
class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ("id", "company_name")


class TeamMemberSerializer(serializers.ModelSerializer):
    user_email = serializers.CharField(source="member.email", allow_null=True)
    user_phone_no = serializers.CharField(source="member.phone_no", allow_null=True)
    first_name = serializers.CharField(source="member.first_name", allow_null=True)
    last_name = serializers.CharField(source="member.last_name", allow_null=True)
    team_name = serializers.CharField(source="team.team_name", allow_null=True)
    company_name = serializers.CharField(
        source="team.company.company_name", allow_null=True
    )
    branch_name = serializers.CharField(source="team.branch.name", allow_null=True)

    class Meta:
        model = TeamMember
        fields = (
            "id",
            "req_no",
            "phone_no",
            "email",
            "user_email",
            "first_name",
            "last_name",
            "team_name",
            "company_name",
            "branch_name",
            "user_phone_no",
            "role",
            "is_registered",
            "created_at",
            "status",
        )


class CompanyRelatedTeamSerializer(serializers.ModelSerializer):
    members = TeamMemberSerializer(read_only=True, many=True)

    class Meta:
        model = Team
        fields = ("id", "team_name", "members")


class TeamSerializer(serializers.ModelSerializer):
    class Meta:
        model = Team
        fields = (
            "id",
            "team_name",
        )


class ListTeamSerializer(serializers.ModelSerializer):
    members = TeamMemberSerializer(read_only=True, many=True)
    company_name = serializers.CharField(source="company.company_name", allow_null=True)
    company_id = serializers.CharField(source="company.id", allow_null=True)
    branch = serializers.CharField(source="branch.name", allow_null=True)

    class Meta:
        model = Team
        fields = (
            "id",
            "team_name",
            "team_type",
            "company_id",
            "company_name",
            "branch",
            "members",
            "requisitions",
            "created_at",
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        budget_allocations = BudgetAllocation.objects.filter(
            team=instance, is_active=True, is_deleted=False
        )

        representation["allocated_amount"] = (
            budget_allocations.aggregate(sum_amount=Sum("amount"))["sum_amount"] or 0
        )

        approved_disbursed_requisitions = Requisition.objects.filter(
            company=instance.company,
            is_disbursed=True,
            team=instance,
            status="APPROVED",
        )

        representation["spent_amount"] = (
            approved_disbursed_requisitions.aggregate(sum_amount=Sum("request_amount"))[
                "sum_amount"
            ]
            or 0
        )

        budget = Budget.objects.filter(team=instance, is_active=True).first()

        allocation_record = Budget.get_current_allocated_amount(
            budget_instance=budget, team_instance=instance
        )

        representation["purse_balance"] = allocation_record.get("purse_balance")

        # representation["total_active_allocation"] = allocation_record.get("total_active_allocation")
        representation["total_active_allocation"] = allocation_record.get(
            "total_request_amount_made_so_far"
        )
        representation["total_active_allocation"] = allocation_record.get(
            "total_active_allocation"
        )
        # representation["total_active_allocation"] = allocation_record.get("total_active_allocation")
        representation["total_active_allocation"] = allocation_record.get(
            "total_request_amount_made_so_far"
        )

        representation["budget"] = (
            {
                "id": budget.id,
                "budget_name": budget.budget_name,
                "budget_amount": budget.budget_amount,
                "team_id": budget.team.id if budget.team else "",
                "team_name": budget.team.team_name if budget.team else "",
                "is_active": budget.is_active,
                "start_date": budget.start_date,
                "end_date": budget.end_date - timedelta(days=1),
            }
            if budget
            else None
        )

        requisitions = Requisition.objects.filter(member__team=instance)
        representation["no_of_members"] = instance.members.aggregate(count=Count("id"))[
            "count"
        ]
        representation["approved_req"] = requisitions.filter(
            status="APPROVED"
        ).aggregate(count=Count("id"))["count"]
        representation["pending_req"] = requisitions.filter(status="PENDING").aggregate(
            count=Count("id")
        )["count"]

        return representation


class AccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountSystem
        exclude = (
            "user",
            "payload",
            "available_balance",
            # "date_created_on_liberty_pay",
            "created_at",
            "updated_at",
        )


class ListOfCompanySerializer(serializers.ModelSerializer):
    teams = ListTeamSerializer(read_only=True, many=True)
    account = AccountSerializer(read_only=True)

    class Meta:
        model = Company
        fields = (
            "id",
            "overall_running_budget",
            "company_name",
            "account",
            "teams",
            "created_at",
            "total_current_month_expenses",
            "current_month_expenses",
        )

    def to_representation(self, instance):

        # Access the request user
        request_user = self.context["request"].user

        representation = super().to_representation(instance)

        # allocated_amount = BudgetAllocation.objects.filter(team__company=instance, budget__is_active=True).aggregate(
        #     sum_amount=Sum('amount'))["sum_amount"] or 0

        budget_allocations = BudgetAllocation.objects.filter(team__company=instance)

        allocated_amount = (
            budget_allocations.aggregate(sum_amount=Sum("amount"))["sum_amount"] or 0
        )

        # Get the current month and year
        current_month = timezone.now().month

        present_month_allocated_amount = budget_allocations.filter(
            created_at__month=current_month
        )

        representation["allocated_amount"] = allocated_amount
        representation["present_month_allocated_amount"] = (
            present_month_allocated_amount.aggregate(sum_amount=Sum("amount"))[
                "sum_amount"
            ]
            or 0
        )

        wallet_type = instance.company_wallet_type

        account_type = "NON_CORP_SPEND_MGMT" if wallet_type == "MAIN" else "SPEND_MGMT"

        representation["account_type"] = account_type

        _account_system = AccountSystem.objects.filter(user=instance.user)
        _wallet_system = Wallet.objects.filter(user=instance.user)

        spend_mgmt_account_details = None

        wallet_data = None

        account_info = _account_system.filter(
            company=instance, account_type=account_type
        ).first()

        account_details = {
            "account_provider": (
                "" if not account_info else account_info.account_provider
            ),
            "account_number": "" if not account_info else account_info.account_number,
            "account_name": "" if not account_info else account_info.account_name,
            "account_type": "" if not account_info else account_info.account_type,
            "bank_name": "" if not account_info else account_info.bank_name,
            "bank_code": "" if not account_info else account_info.bank_code,
        }

        if wallet_type == "MAIN":
            _personal_account = _account_system.filter(
                company=instance, account_type="NON_CORP_SPEND_MGMT"
            ).first()
            _personal_wallet = _wallet_system.filter(
                account=_personal_account, wallet_type="NON_CORP_SPEND_MGMT"
            ).first()

            available_balance = _personal_wallet.balance if _personal_wallet else 0.0

            all_time_credit = (
                DebitCreditRecordOnAccount.objects.filter(
                    wallet=_personal_wallet, entry="CREDIT"
                ).aggregate(sum_amount=Sum("amount"))["sum_amount"]
                or 0
            )

            wallet_data = {
                "wallet_type": wallet_type,
                "available_balance": available_balance,
                "main_balance": available_balance,
                "final_balance": all_time_credit - allocated_amount,
            }

            spend_mgmt_account_details = {
                "account_name": (
                    _personal_account.account_name if _personal_account else ""
                ),
                "account_number": (
                    _personal_account.account_number if _personal_account else ""
                ),
                "bank_name": "VFD Microfinance Bank",
            }

        elif wallet_type == "CORPORATE":
            _corporate_account = _account_system.filter(
                account_type="SPEND_MGMT", company=instance
            ).first()
            _corporate_wallet = _wallet_system.filter(
                account=_corporate_account, wallet_type="SPEND_MGMT"
            ).first()

            available_balance = _corporate_wallet.balance if _corporate_wallet else 0.0

            # get all wallet credit
            all_time_credit = (
                DebitCreditRecordOnAccount.objects.filter(
                    wallet=_corporate_wallet, entry="CREDIT"
                ).aggregate(sum_amount=Sum("amount"))["sum_amount"]
                or 0
            )

            wallet_data = {
                "wallet_type": wallet_type,
                "available_balance": available_balance,
                "main_balance": available_balance,
                "final_balance": all_time_credit - allocated_amount,
            }

            spend_mgmt_account_details = {
                "account_name": (
                    _corporate_account.account_name if _corporate_account else ""
                ),
                "account_number": (
                    _corporate_account.account_number if _corporate_account else ""
                ),
                "bank_name": _corporate_account.bank_name if _corporate_account else "",
            }

        # hide wallet data from non company owner

        if request_user != instance.user:

            representation["wallet"] = {
                "wallet_type": wallet_type,
                "available_balance": 0.0,
                "main_balance": 0.0,
                "final_balance": 0.0,
            }

            representation["spend_mgmt_account_details"] = spend_mgmt_account_details
        else:

            representation["wallet"] = wallet_data

            representation["spend_mgmt_account_details"] = spend_mgmt_account_details

        requisitions = Requisition.objects.filter(member__team__company=instance)
        representation["no_of_teams"] = instance.teams.aggregate(count=Count("id"))[
            "count"
        ]
        representation["total_req"] = requisitions.aggregate(count=Count("id"))["count"]
        representation["approved_req"] = requisitions.filter(
            status="APPROVED"
        ).aggregate(count=Count("id"))["count"]
        representation["pending_req"] = requisitions.filter(status="PENDING").aggregate(
            count=Count("id")
        )["count"]

        total_team_member_in_company = 0
        for team in instance.teams.all():
            member_count = team.members.aggregate(count=Count("id"))["count"]
            total_team_member_in_company += member_count

        representation["no_of_members"] = total_team_member_in_company
        budgets = Budget.objects.filter(company=instance, is_active=True)

        representation["budget"] = (
            budgets.values(
                "id",
                "company_id",
                "team_id",
                "budget_name",
                "budget_amount",
                "budget_type",
                "start_date",
                "end_date",
                "created_at",
            )
            if budgets
            else []
        )
        representation["default"] = (
            True if request_user.default_company == instance else False
        )
        representation["account_details"] = account_details

        # Add subscription information
        subscription_service = SubscriptionService(instance)
        representation = subscription_service.get_subscription_info(representation)

        return representation


class CreateCompanySerializer(serializers.ModelSerializer):
    wallet_type = serializers.CharField(max_length=255)
    transaction_pin = serializers.CharField(allow_blank=True, allow_null=True)
    id_number = serializers.CharField(allow_blank=True, allow_null=True)
    paid_shared_capital = serializers.CharField(allow_blank=True, allow_null=True)
    type_of_entity = serializers.CharField(allow_blank=True, allow_null=True)
    director_date_of_birth = serializers.CharField(allow_blank=True, allow_null=True)

    class Meta:
        model = Company
        fields = [
            "company_name",
            "industry",
            "size",
            "cac_num",
            "wallet_type",
            "channel",
            "transaction_pin",
            "id_number",
            "paid_shared_capital",
            "type_of_entity",
            "director_date_of_birth",
        ]

    def validate(self, attrs):
        context = self.context
        request = context.get("request")
        user = request.user
        wallet_type = attrs.get("wallet_type")
        transaction_pin = attrs.get("transaction_pin")
        company_name = attrs.get("company_name").title()

        if Company.objects.filter(company_name=company_name).exists():
            raise serializers.ValidationError(
                {"message": f"Company name: {company_name.upper()} already exists"}
            )

        # Default incorporation_date to None
        incorporation_date = None

        if wallet_type == "CORPORATE":
            cac_num = attrs.get("cac_num")
            type_of_entity = attrs.get("type_of_entity")
            id_number = attrs.get("id_number")
            paid_shared_capital = attrs.get("paid_shared_capital")
            director_date_of_birth = attrs.get("director_date_of_birth")

            if Company.objects.filter(cac_num=cac_num, is_active=True).exists():
                raise serializers.ValidationError(
                    {
                        "message": f"Company with registered number {cac_num} already exists"
                    }
                )

            existing_company_info = CompanyVerificationInfo.objects.filter(
                user=user, registration_no=cac_num
            ).first()

            if existing_company_info:
                # Incorporation date is derived here
                incorporation_date = (
                    existing_company_info.registration_submission_date
                    or existing_company_info.registration_date
                )

                document_number = existing_company_info.document_number
                info_shared_capital = existing_company_info.paid_share_capital

                if cac_num is None or (cac_num is not None and len(cac_num) == 0):
                    raise serializers.ValidationError(
                        {"message": "CAC number is required"}
                    )

                if document_number and id_number is None:
                    raise serializers.ValidationError(
                        {"message": "Document number is required"}
                    )

                if info_shared_capital and paid_shared_capital is None:
                    raise serializers.ValidationError(
                        {"message": "Paid shared capital is required"}
                    )

                if existing_company_info.director_date_of_birth and (
                    director_date_of_birth is None or director_date_of_birth == ""
                ):
                    raise serializers.ValidationError(
                        {"message": "Director's date of birth capital is required"}
                    )

                if document_number and id_number != document_number:
                    raise serializers.ValidationError(
                        {"message": "Invalid document number"}
                    )

                if not existing_company_info.paid_share_capital:
                    pass
                else:
                    if (
                        info_shared_capital
                        and paid_shared_capital != info_shared_capital
                    ):
                        raise serializers.ValidationError(
                            {"message": "Paid share capital does not match"}
                        )

                if existing_company_info.director_date_of_birth:
                    verification_info_directors_dob = (
                        existing_company_info.director_date_of_birth
                    )

                    try:
                        formatted_dob = is_valid_date_format(
                            date_string=director_date_of_birth
                        )
                    except TypeError:
                        raise serializers.ValidationError(
                            {"message": "Date of birth format should be in 'YYYY-MM-DD"}
                        )

                    request_year = formatted_dob.year if formatted_dob else None
                    request_month = formatted_dob.month if formatted_dob else None

                    verification_info_directors_year = (
                        verification_info_directors_dob.year
                        if verification_info_directors_dob
                        else "00-00-00"
                    )
                    verification_info_directors_month = (
                        verification_info_directors_dob.month
                        if verification_info_directors_dob
                        else "00-00-00"
                    )

                    # verify director's name with month and year only
                    if (verification_info_directors_year != request_year) and (
                        verification_info_directors_month != request_month
                    ):
                        raise serializers.ValidationError(
                            {"message": "Director's Date of birth does not match"}
                        )

                existing_company_info.verified = True
                existing_company_info.type_of_entity = type_of_entity
                existing_company_info.save()
            else:
                raise serializers.ValidationError(
                    {
                        "message": "Kindly verify company by providing company verification number"
                    }
                )

        attrs["incorporation_date"] = incorporation_date
        return attrs


class VerifyCorporateCompanyRegistrationNumberSerializer(serializers.Serializer):
    cac_num = serializers.CharField(max_length=255)


class VerifyCorporateCompanyInfoSerializer(serializers.Serializer):
    verification_id = serializers.PrimaryKeyRelatedField(
        queryset=CompanyVerificationInfo.objects.all()
    )
    tin = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    director_lastname = serializers.CharField(max_length=255)
    director_firstname = serializers.CharField(max_length=255)

    def validate(self, attrs):
        verification_instance = attrs.get("verification_id")
        request_tin = attrs.get("tin")
        director_lastname = attrs.get("director_lastname", "").title()
        director_firstname = attrs.get("director_firstname", "").title()

        logger.info(f"INPUT DIRECTOR'S LAST NAME: {director_lastname}")
        logger.info(f"INPUT DIRECTOR'S FIRST NAME: {director_firstname}")

        verification_data = verification_instance.verification_response.get("data")
        tin = verification_data.get("tin")
        key_personnel = verification_data.get("keyPersonnel")

        registrationDate = verification_data.get("registrationDate")
        registrationSubmissionDate = verification_data.get("registrationSubmissionDate")
        paidShareCapital = verification_data.get("paidShareCapital")

        cac_num = verification_instance.registration_no
        company = Company.objects.filter(cac_num=cac_num, is_active=True)

        if company.exists():
            raise serializers.ValidationError(
                {"message": f"Company with registered number {cac_num} already exists"}
            )

        logger.info(f"BACKEND TIN {tin}")
        logger.info(f"BACKEND TIN {tin}")

        if not tin:
            pass
        else:
            if request_tin != tin:
                raise serializers.ValidationError(
                    {"message": f"Invalid Tax Identification Number"}
                )

        director_objects = {}

        key_personnel_fullname = f"{director_lastname} {director_firstname}"

        if not key_personnel:
            raise serializers.ValidationError(
                {
                    "message": f"Your company currently has no key personnel listed. Please contact support."
                }
            )
        for personnel in key_personnel:
            personnels = ["PROPRIETOR", "DIRECTOR"]

            designation = personnel.get("designation")
            personnel_status = str(personnel.get("status")).title()

            personnel_status = str(personnel.get("status")).title()

            logger.info(f"DESIGNATION: {designation}")
            logger.info(f"DESIGNATION: {designation}")

            logger.info(f"SPLITTED NAMES: {key_personnel_fullname}")
            logger.info(f"SPLITTED NAMES: {key_personnel_fullname}")
            logger.info(f"SPLITTED NAMES: {key_personnel_fullname}")

            # if designation in personnels and personnel.get("status") == "ACTIVE":
            # if designation in personnels:
            if designation in personnels and str(personnel_status).upper() != "REMOVED":
                name = personnel.get("name").title()

                name_list = [name.strip() for name in name.replace(",", " ").split()]

                logger.info(f"DATA NAME: {name}")
                logger.info(f"DATA NAME: {name}")
                logger.info(f"DATA NAME: {name}")

                # if director_lastname not in name and director_firstname not in name:
                if not (director_lastname or director_firstname) in name_list:
                    continue
                else:

                    dateOfBirth = personnel.get("dateOfBirth")

                    if dateOfBirth is None:
                        formatted_dob = None
                    else:
                        formatted_dob = parse(dateOfBirth).strftime("%Y-%m-%d")
                    doc_type = personnel.get("documentType")

                    documentNumber = personnel.get("documentNumber")

                    if documentNumber:
                        doc_number_hint = (
                            f"{documentNumber[:3]}***{documentNumber[-2:]}"
                        )
                        doc_type_and_number_hint = f"{doc_type} {doc_number_hint}"
                    else:
                        doc_type_and_number_hint = doc_type

                    director_objects[key_personnel_fullname] = {
                        "documentType": doc_type_and_number_hint,
                        "documentNumber": personnel.get("documentNumber"),
                        "nationality": personnel.get("nationality"),
                        "dateOfBirth": formatted_dob,
                        "registrationDate": registrationDate,
                        "registrationSubmissionDate": registrationSubmissionDate,
                        "paidShareCapital": paidShareCapital,
                    }

                    if personnel.get("designation") == "DIRECTOR":
                        break

        if not director_objects:
            raise serializers.ValidationError(
                {
                    "message": f"Director {key_personnel_fullname} does not exist or inactive"
                }
            )

        attrs["directors"] = director_objects
        return attrs


class CreateUpdateTeamSerializer(serializers.Serializer):
    team_name = serializers.CharField(max_length=255, allow_null=True, allow_blank=True)
    team_id = serializers.UUIDField(required=False, allow_null=True)
    team_type = serializers.CharField(required=False)
    company_id = serializers.UUIDField(required=False, allow_null=True)
    members = serializers.ListField(child=serializers.JSONField())
    branch_id = serializers.UUIDField(required=False, allow_null=True)
    invited_members = serializers.ListField(
        required=False, allow_null=True, child=serializers.JSONField()
    )

    def validate(self, attrs):
        context = self.context
        company_id = attrs.get("company_id")
        team_id = attrs.get("team_id")
        team_name = attrs.get("team_name", "").title()
        members = attrs.get("members", [])
        invited_members = attrs.get("invited_members", [])
        user = context.get("user")

        # Email validation regex pattern
        email_pattern = re.compile(r"^[\w\.-]+@[\w\.-]+\.\w+$")
        invalid_emails = []
        invalid_roles = []
        invalid_phone_numbers = []

        for member in members:
            if not isinstance(member, dict):
                raise serializers.ValidationError(
                    {"members": "Each member must be a dictionary"}
                )

            # Email validation
            if not member.get("email") or not email_pattern.match(member.get("email")):
                invalid_emails.append(member.get("email"))

            # Role validation
            role = member.get("role")
            if not role or role not in UserRole.values:
                invalid_roles.append(role if role else "Empty role")

            # Phone number validation (assuming standard international format)
            phone_no = member.get("phone_no", "")
            if phone_no and not re.match(r"^\+\d{1,15}$", phone_no):
                invalid_phone_numbers.append(phone_no)

        # Validate invited team members
        for member in invited_members:
            if not isinstance(member, dict):
                raise serializers.ValidationError(
                    {"invited_members": "Each invited member must be a dictionary"}
                )

            # Email validation
            if not member.get("email") or not email_pattern.match(member.get("email")):
                invalid_emails.append(member.get("email"))

            # Role validation
            role = member.get("role")
            if not role or role not in UserRole.values:
                invalid_roles.append(role if role else "Empty role")

        if invalid_emails:
            raise serializers.ValidationError({"email": "A valid email is required"})

        if invalid_roles:
            raise serializers.ValidationError(
                {"role": f"Invalid role(s) provided: {', '.join(invalid_roles)}"}
            )

        if invalid_phone_numbers:
            raise serializers.ValidationError(
                {
                    "phone_no": f"Invalid phone number(s) provided: {', '.join(invalid_phone_numbers)}"
                }
            )

        # Validate company properties if creating a team
        if context.get("create_team") is True:
            if not company_id:
                raise serializers.ValidationError(
                    {"company_id": "Company ID is required"}
                )
            if not team_name:
                raise serializers.ValidationError(
                    {"team_name": "Team name is required"}
                )

            company = Company.objects.filter(pk=company_id)
            if not company.exists():
                raise serializers.ValidationError({"message": "Invalid company"})

            company_instance = company.last()

            if user != company_instance.user:
                super_admin_roles = ["ADMIN", "OWNER", "SUB_ADMIN"]
                user_in_super_admin_roles = TeamMember.objects.filter(
                    team__company=company_instance,
                    member=user,
                    role__in=super_admin_roles,
                )
                if not user_in_super_admin_roles.exists():
                    raise PermissionDenied(
                        "You do not have the necessary permissions to perform this action"
                    )

            existing_teams = Team.objects.filter(
                team_name=team_name, company=company_instance
            )
            if existing_teams.exists():
                raise serializers.ValidationError(
                    {"message": f"You have an existing team named {team_name}"}
                )

        # Validate properties if updating a team
        if context.get("create_team") is False:
            if not team_id:
                raise serializers.ValidationError({"team_id": "Team ID is required"})
            try:
                team_ins = Team.objects.get(pk=team_id)
            except Team.DoesNotExist:
                raise serializers.ValidationError({"message": "Invalid Team"})

            # Check if any of the provided members already exist in the team
            emails = [member.get("email") for member in members if member.get("email")]
            invited_emails = [
                member.get("email") for member in invited_members if member.get("email")
            ]

            emails.extend(invited_emails)

            # existing_members = TeamMember.objects.filter(
            #     email__in=emails, team=team_ins
            # )
            # if existing_members.exists():
            #     raise serializers.ValidationError(
            #         {"message": "Team member already exists on the selected team"}
            #     )
        attrs["invited_members"] = invited_members

        return attrs


class EditTeamMemberPermissionAndOthersSerializer(serializers.Serializer):
    team_id = serializers.UUIDField()
    team_member_id = serializers.UUIDField()
    admin_password = serializers.CharField(
        required=False,
        allow_blank=True,
        allow_null=True,
    )
    role = serializers.CharField(max_length=255)

    def validate(self, attrs):
        role = attrs.get("role", "unknown")
        role = role.upper()

        logger.debug(f"EDIT ROLE<<<<<<<<<<<<<<<<<<<<<<<<<<")

        logger.debug(f"ROLE: {role}")
        logger.debug(f"ROLE: {role}")

        logger.debug(f"EDIT ROLE<<<<<<<<<<<<<<<<<<<<<<<<<<")

        roles = [
            "DISBURSER",
            "MEMBER",
            "OWNER",
            "SUB_ADMIN",
            "REVIEWER",
            "SALES_SUPER_ADMIN",
            "SALES_LEAD",
            "SALES_OFFICER",
            "ADMIN",
        ]

        if role not in roles:
            raise serializers.ValidationError({"message": "Invalid role selection"})

        return attrs


class RequisitionSerializer(serializers.ModelSerializer):
    invoice = serializers.FileField(required=False, allow_null=True)
    team = serializers.PrimaryKeyRelatedField(queryset=Team.objects.all())

    class Meta:
        model = Requisition
        fields = [
            "id",
            "member",
            "request_amount",
            "is_paid_budget",
            "is_vendor",
            "vendor_name",
            "vendor_no",
            "request_reason",
            "requisition_category",
            "other_category",
            "bank_name",
            "bank_code",
            "account_name",
            "account_no",
            "invoice",
            "team",
            "priority",
            "public",
            "created_at",
            "status",
        ]

    def validate(self, attrs):

        request = self.context.get("request")
        user = request.user
        # is_paid_budget = attrs.get('is_paid_budget')
        member_object = attrs.get("member")
        team_obj = attrs.get("team")
        request_amount = attrs.get("request_amount")
        is_vendor = attrs.get("is_vendor")
        # requisition_category = attrs.get('requisition_category')
        vendor_name = attrs.get("vendor_name")
        vendor_no = attrs.get("vendor_no")
        team_name = team_obj.team_name
        try:
            member_id = member_object.id
        except AttributeError:
            member_id = None

        requisition_category = attrs.get("requisition_category", None)
        other_category = attrs.get("other_category", None)

        if requisition_category == "OTHERS" and not other_category:
            raise serializers.ValidationError(
                {"message": "Please provide details for 'OTHERS' category."}
            )

        try:
            team_member_ins = TeamMember.objects.get(id=member_id, member=user)
        except TeamMember.DoesNotExist:
            raise serializers.ValidationError(
                {
                    "message": "Requisition requests can only be made by members of the team."
                }
            )

        active_budget = Budget.objects.filter(team=team_obj, is_active=True).first()

        if not active_budget:
            raise serializers.ValidationError(
                {
                    "message": f"The selected team {team_obj.team_name} currently doesn't have an active budget."
                }
            )

        category = Category.objects.filter(
            budget=active_budget,
            title=requisition_category.upper() if requisition_category else None,
        ).first()

        # if not category:
        #     raise serializers.ValidationError(
        #         {"message": f"There is no allocated amount for the selected
        #         category {requisition_category.upper()}."})

        allocations = BudgetAllocation.objects.filter(budget=active_budget)

        if not allocations:
            raise serializers.ValidationError(
                {
                    "message": "The team has not been assigned any budget allocations at this time."
                }
            )

        allocated_amount = (
            allocations.aggregate(sum_amount=Sum("amount"))["sum_amount"] or 0
        )

        _all_expense_to_active_budget = Expense.objects.filter(
            budget=active_budget, status="SUCCESSFUL", is_deleted=False
        )

        expensed_amount = (
            _all_expense_to_active_budget.aggregate(sum_amount=Sum("expense_amount"))[
                "sum_amount"
            ]
            or 0
        )

        start_date = active_budget.start_date
        end_date = active_budget.end_date

        requisitions = Requisition.objects.filter(
            Q(status="PENDING")
            | Q(status="PROCESSING")
            | Q(status="APPROVED")
            | Q(status="SUCCESSFUL"),
            team=team_obj,
            created_at__range=[start_date, end_date],
        )

        # print(requisitions)

        total_request_amount_made_so_far = (
            requisitions.aggregate(sum_amount=Sum("request_amount"))["sum_amount"] or 0
        )

        budget_balance_before = (
            active_budget.budget_amount - total_request_amount_made_so_far
        )

        # watch spending below current active budget
        if budget_balance_before <= 0:
            raise serializers.ValidationError(
                {
                    "message": f"You cannot spend beyond the current budget balance of {budget_balance_before}."
                }
            )

        # if expensed_amount > allocated_amount:
        if total_request_amount_made_so_far > allocated_amount:
            # exceeded_amount = float(expensed_amount) - float(allocated_amount)

            exceeded_amount = float(total_request_amount_made_so_far) - float(
                allocated_amount
            )
            raise serializers.ValidationError(
                {
                    "message": f"{team_name} has exceeded the budget limit by {exceeded_amount}."
                }
            )

        purse_balance = float(allocated_amount) - float(
            total_request_amount_made_so_far
        )

        if float(request_amount) > purse_balance:
            company_owner = team_obj.company.user
            number = company_owner.phone_no

            insufficient_balance_notification_on_requisition.delay(
                num=number,
                team_name=team_name,
                purse_balance=purse_balance,
                requested_amount=request_amount,
            )
            raise serializers.ValidationError(
                {
                    "message": f"The amount you've requested exceeds the current balance of {purse_balance} in the "
                    f"team's purse."
                }
            )

        if is_vendor is True and not vendor_name:
            raise serializers.ValidationError(
                {"message": "Kindly provide vendor's name"}
            )

        if is_vendor is True and not vendor_no:
            raise serializers.ValidationError(
                {"message": "Kindly provide vendor's phone number"}
            )

        return attrs


class ListReqSerializer(serializers.ModelSerializer):
    class Meta:
        model = Requisition
        fields = (
            "id",
            "requisition_category",
            "approved_at",
            "other_category",
            "request_amount",
            "status",
            "created_at",
            "company",
            "member",
            "user_email",
            "invoice",
            "request_reason",
            "bank_name",
            "account_name",
            "account_no",
        )

    def get_user_first_name(self, instance):
        return (
            instance.user.first_name + " " + instance.user.last_name
            if instance.user
            else None
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        requisition_category = representation["requisition_category"]

        if requisition_category == "OTHERS":
            representation["requisition_category"] = instance.other_category

        representation["user_name"] = self.get_user_first_name(instance)
        representation["team_id"] = instance.team.id
        representation["team_name"] = instance.team.team_name
        representation["approved_by"] = (
            instance.approved_by.full_name if instance.approved_by else ""
        )
        return representation


class ListCreateCatergorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ["id", "main_title"]


class DelCatergoriesSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.IntegerField())

    def validate(self, attrs):
        return attrs


class CommentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Comment
        fields = ["id", "requisition", "comment_text"]

    def validate(self, attrs):
        request = self.context.get("request")
        request_user = request.user
        req = attrs.get("requisition")
        req_creator = req.user
        member = req.member
        role = req.member.role

        team_lead = req.member.team.user
        if role in ["DISBURSER", "SUB_ADMIN", "REVIEWER"]:
            pass

        elif role == "MEMBER" and (request_user == req_creator):
            pass

        elif request_user == team_lead:
            pass

        else:
            raise serializers.ValidationError(
                {"message": "you are not allowed to contribute to this requisition"}
            )

        return attrs


class DisbursementSerializer(serializers.Serializer):
    requisition_id = serializers.CharField()
    transaction_pin = serializers.CharField()

    # password = serializers.CharField()

    def validate(self, attrs):
        request = self.context.get("request")
        user = request.user
        transaction_pin = attrs.get("transaction_pin")
        requisition_id = attrs.get("requisition_id")
        logger.debug(
            ">>>>>>>>>>>>>>>>>>>>>>>>>>>>> DISBURSEMENT SERIALIZER <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
        )
        logger.debug(f"USER EMAIL: {user.email}")
        logger.debug(f"REQUISITION ID: {requisition_id}")
        try:
            requisition = Requisition.objects.get(id=requisition_id)
        except Requisition.DoesNotExist:
            raise serializers.ValidationError({"message": "invalid requisition"})

        company = requisition.company
        wallet_type = company.company_wallet_type

        if requisition.is_disbursed:
            raise serializers.ValidationError(
                {
                    "message": "Money has already been disbursed. Multiple disbursements not allowed."
                }
            )

        elif requisition.is_inprogress:
            raise serializers.ValidationError(
                {"message": "Requisition Disbursement in progress"}
            )

        if requisition.status != "PENDING":
            raise serializers.ValidationError(
                {"message": f"Requisition Status is currently {requisition.status}"}
            )

        if user.requisition_transaction_pin is None:
            raise serializers.ValidationError(
                {
                    "message": "You do not have a transaction pin kindly create one to enable you perform transactions"
                }
            )

        password_match = check_password(
            transaction_pin, user.requisition_transaction_pin
        )
        if password_match is False:
            raise serializers.ValidationError({"message": "Incorrect transaction pin"})
        attrs["requisition"] = requisition
        attrs["company"] = company
        attrs["wallet_type"] = wallet_type
        return attrs


class DeclineReqSerializer(serializers.Serializer):
    requisition_id = serializers.CharField()
    declined_reason = serializers.CharField()

    def validate(self, attrs):
        # request = self.context.get('request')

        requisition_id = attrs.get("requisition_id")
        # print(requisition_id)
        try:
            requisition = Requisition.objects.get(id=requisition_id)
        except Requisition.DoesNotExist:
            raise serializers.ValidationError({"message": "invalid requisition"})

        user = requisition.company.user
        # print(user)
        attrs["user"] = user
        attrs["requisition"] = requisition
        return attrs


class RequisitionExpenseSerializer(serializers.ModelSerializer):
    receipt = serializers.FileField(required=False, allow_null=True)

    class Meta:
        model = Expense
        fields = [
            "id",
            "company",
            "expense_category",
            "expense_other_category",
            "budget",
            "expense_amount",
            "receipt",
            "created_at",
            "in_budget",
            "invoice_date",
            "updated_at",
            "status",
            "invoice_number",
            "tax_information",
            "location",
            "item_description",
            "mode_of_payment",
        ]

    def validate(self, attrs):
        expense_category = attrs.get("expense_category", None)
        expense_other_category = attrs.get("expense_other_category", None)
        in_budget = attrs.get("in_budget")
        budget = attrs.get("budget")

        if expense_category == "OTHERS" and not expense_other_category:
            raise serializers.ValidationError(
                {"message": "Please provide details for 'OTHERS' category."}
            )

        if in_budget is True and not budget:
            raise serializers.ValidationError(
                {"message": "Please provide budget details."}
            )

        if not expense_category:
            raise serializers.ValidationError(
                {"message": "Kindly provide expense category."}
            )
        return attrs


class ExpenseDashboardSerializerList(serializers.ModelSerializer):
    class Meta:
        model = Expense
        fields = (
            "requested_by",
            "status",
            "expense_amount",
            "expense_category",
            "expense_other_category",
            "team_name",
            "category_title",
            "created_at",
        )


class ExpenseDashboardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Expense
        exclude = (
            "transaction_ref",
            "payload",
            # "payout_verification_payload",
            "user",
            "budget",
        )

    def get_team_name(self, instance):
        if instance.requisition:
            # Convert TeamMember object to string
            return str(instance.requisition.member.team.team_name)
        return None

    def get_req_category(self, instance):
        if instance.requisition:
            # Convert TeamMember object to string
            return str(instance.requisition.other_category)

    def get_user_names(self, instance):
        return (
            instance.user.first_name + " " + instance.user.last_name
            if instance.user
            else None
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        expense_category = representation["expense_category"]
        expense_other_category = representation["expense_other_category"]

        representation["user_name"] = self.get_user_names(instance)
        representation["team_name"] = self.get_team_name(instance)

        if expense_category == "OTHERS" and not expense_other_category:
            representation["expense_other_category"] = self.get_req_category(instance)

        return representation


class BudgetSerializer(serializers.ModelSerializer):
    class Meta:
        model = Budget
        fields = [
            "id",
            "team",
            "company",
            "budget_name",
            "budget_amount",
            "budget_type",
            "start_date",
            "end_date",
        ]

    def validate(self, attrs):

        request = self.context.get("request")
        user = request.user
        current_date = date.today()
        start_date = attrs.get("start_date")
        company = attrs.get("company")
        team = attrs.get("team")
        end_date = attrs.get("end_date")
        # budget_type = attrs.get("budget_type")

        if not team and company.user != user:
            raise PermissionDenied(
                "You do not have the necessary permissions to perform this action"
            )
        # print(user, "\n\n")
        if team:
            team_members = team.members
            member_instance = team_members.filter(member=user).last()

            if not member_instance:
                raise PermissionDenied(
                    "You do not have the necessary permissions to perform this action"
                )

            roles = ["ADMIN", "OWNER", "SUB_ADMIN", "DISBURSER"]
            if member_instance.role not in roles:
                raise PermissionDenied(
                    "You do not have the necessary permissions to perform this action"
                )

        days_difference = (end_date - start_date).days

        # validate date
        if start_date < current_date:
            raise serializers.ValidationError(
                {"message": "start date cannot be less than current date"}
            )

        elif start_date > end_date:
            raise serializers.ValidationError(
                {"message": "start date must be less than end date"}
            )

        elif days_difference < 5:
            raise serializers.ValidationError(
                {"message": "date difference should have at least 5 days interval"}
            )

        # validate that only company owners are allowed to create budget for a company
        if team:
            # check if team has an active budget
            active_budget = Budget.objects.filter(team=team, is_active=True)
            if active_budget:
                raise serializers.ValidationError(
                    {"message": "Team has an active budget"}
                )

        return attrs


class AllTeamBudgetSerializer(serializers.ModelSerializer):
    class Meta:
        model = Budget
        fields = "__all__"


class EditBudgetSerializer(serializers.Serializer):
    budget_id = serializers.CharField(max_length=250, required=True)
    budget_name = serializers.CharField(
        max_length=250, required=False, allow_blank=True, allow_null=True
    )
    budget_amount = serializers.IntegerField(required=False, min_value=0)
    start_date = serializers.CharField(
        max_length=250, allow_blank=True, allow_null=True
    )
    end_date = serializers.CharField(max_length=250, allow_blank=True, allow_null=True)

    def validate(self, attrs):
        request = self.context.get("request")
        edit = self.context.get("edit")
        user = request.user
        # current_date = date.today()
        budget_id = attrs.get("budget_id")
        start_date = attrs.get("start_date")
        end_date = attrs.get("end_date")
        budget_amount = attrs.get("budget_amount")

        formatted_start_date = None
        formatted_end_date = None
        if start_date != "" or end_date != "":
            formatted_start_date = is_valid_date_format(date_string=start_date)
            formatted_end_date = is_valid_date_format(date_string=end_date)

            if start_date != "" and formatted_start_date is False:
                raise serializers.ValidationError(
                    {"message": "Date format should be in 'YYYY-MM-DD"}
                )
            if end_date != "" and formatted_end_date is False:
                raise serializers.ValidationError(
                    {"message": "Date format should be in 'YYYY-MM-DD"}
                )

        try:
            budget = Budget.objects.get(id=int(budget_id))
        except Budget.DoesNotExist:
            raise serializers.ValidationError({"message": "Budget does not exist"})

        # if budget.company.user != user:
        #     raise serializers.ValidationError(
        #         {
        #             "message": "Sorry, you do not have the permission to edit this budget."
        #         }
        #     )

        # Commented out the above to allow other permitted users edit budget

        member = None
        team_member = budget.team.members.filter(member=user)
        if team_member:
            member = team_member.first()
        allowed_roles = [UserRole.ADMIN, UserRole.DISBURSER, UserRole.OWNER]
        if [(member and member.role in allowed_roles) or (budget.company.user == user)][
            0
        ] is False:
            raise serializers.ValidationError(
                {"message": "Sorry, you do not have the permission to edit this budget"}
            )

        if budget.is_active is False:
            raise serializers.ValidationError(
                {
                    "message": "Sorry, you cannot edit the budget because it is no longer active."
                }
            )

        # if edit:
        categories = budget.categories.all()
        allocated_amount = (
            categories.aggregate(sum_amount=Sum("amount"))["sum_amount"] or 0
        )

        if budget_amount < allocated_amount:
            raise serializers.ValidationError(
                {"message": "Amount cannot be less than initial allocated amount."}
            )

        budget_ins_start_date = budget.start_date

        days_difference = (formatted_end_date - formatted_start_date).days

        if start_date != "" and (formatted_start_date < budget_ins_start_date):
            raise serializers.ValidationError(
                {"message": "Budget initial start date cannot be less than start date"}
            )

        elif start_date != "" and end_date != "" and days_difference < 5:
            raise serializers.ValidationError(
                {"message": "date difference should have at least 5 days interval"}
            )
        elif start_date == "" and end_date != "":
            days_difference = (formatted_end_date - budget_ins_start_date).days
            if days_difference < 5:
                raise serializers.ValidationError(
                    {"message": "date difference should have at least 5 days interval"}
                )

        return attrs


class CreateBudgetCategoriesSerializer(serializers.Serializer):
    budget = serializers.PrimaryKeyRelatedField(queryset=Budget.objects.all())
    budget_categories = serializers.ListField(
        child=serializers.JSONField(), validators=[MinLengthValidator(1)]
    )

    def validate(self, attrs):
        budget_ins = attrs.get("budget")
        budget_categories = attrs.get("budget_categories")
        for category in budget_categories:
            category_title = category.get("category")
            other_category = category.get("other_category")
            if (
                category_title == "OTHERS"
                and Category.objects.filter(
                    budget=budget_ins, other_category=other_category
                ).exists()
            ):
                raise serializers.ValidationError(
                    {
                        "message": f"Budget with this other category title {other_category} already exists"
                    }
                )
            else:
                if Category.objects.filter(
                    budget=budget_ins, title=category_title
                ).exists():
                    raise serializers.ValidationError(
                        {
                            "message": f"Budget with this category title {category_title} already exists"
                        }
                    )

        return attrs


class BudgetEditCategoriesSerializerIn(serializers.Serializer):
    category = serializers.CharField()
    other_category = serializers.CharField(allow_null=True, allow_blank=True)
    amount = serializers.FloatField()


class EditBudgetCategorySerializer(serializers.Serializer):
    budget = serializers.PrimaryKeyRelatedField(queryset=Budget.objects.all())
    categories = serializers.ListField(child=BudgetEditCategoriesSerializerIn())

    def validate(self, attrs):
        categories = attrs.get("categories")

        track_title = []
        for category_title in categories:

            title = category_title.get("category")

            if title in track_title:
                raise serializers.ValidationError(
                    {"message": f"Duplicate category title {title}"}
                )
            else:
                track_title.append(title)

        return attrs


class TrackBudgetSerializer(serializers.Serializer):
    category = serializers.PrimaryKeyRelatedField(queryset=Category.objects.all())
    expense_amount = serializers.DecimalField(max_digits=8, decimal_places=2)
    spend_date = serializers.CharField(
        max_length=250, allow_blank=True, allow_null=True
    )
    receipt = serializers.FileField(required=False, allow_null=True)

    def validate(self, attrs):
        spend_date = attrs.get("spend_date")
        if spend_date != "":
            if is_valid_date_format(date_string=spend_date) is False:
                raise serializers.ValidationError(
                    {"message": "Date format should be in 'YYYY-MM-DD"}
                )

        return attrs


class ListRequisitionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Requisition
        fields = ["id"]


class ListCategoriesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = [
            "id",
            "title",
            "amount",
            "other_category",
            "main_title",
            "percentage_off_budget",
            "percentage_spent",
            "created_at",
        ]


class ListBudgetSerializer(serializers.ModelSerializer):
    categories = ListCategoriesSerializer(read_only=True, many=True)
    team = TeamSerializer(read_only=True)

    class Meta:
        model = Budget
        fields = [
            "id",
            "budget_name",
            "budget_amount",
            "budget_type",
            "start_date",
            "end_date",
            "created_at",
            "updated_at",
            "categories",
            "team",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        representation["end_date"] = instance.end_date - timedelta(days=1)

        filtered_categories = [
            category
            for category in instance.categories.filter(is_active=instance.is_active)
            if not category.paid_budget
        ]
        representation["categories"] = ListCategoriesSerializer(
            filtered_categories, many=True
        ).data

        representation["company"] = instance.company.company_name
        representation["company_id"] = instance.company.id
        categories = instance.categories.filter(is_active=instance.is_active)
        allocated_amount = (
            categories.aggregate(sum_amount=Sum("amount"))["sum_amount"]
            if categories
            else 0.0
        )

        representation["percentage"] = calculate_percentage(
            amount=allocated_amount, total_amount=instance.budget_amount
        )
        representation["allocated_amount"] = allocated_amount

        representation["budget_balance"] = float(instance.budget_amount) - float(
            allocated_amount
        )

        all_expensed_amount_on_current_active_budget = (
            Expense.objects.filter(budget=instance, status="SUCCESSFUL").aggregate(
                sum_amount=Sum("expense_amount")
            )["sum_amount"]
            or 0
        )

        percent_amount_spent = calculate_percentage(
            total_amount=instance.budget_amount,
            amount=all_expensed_amount_on_current_active_budget,
        )

        representation["amount_spent"] = all_expensed_amount_on_current_active_budget

        total_outside_expense_amount = Expense.objects.filter(
            budget__isnull=True,
            status="SUCCESSFUL",
            company=instance.company,
            created_at__date__range=[instance.start_date, instance.end_date],
        ).aggregate(sum_amount=Sum("expense_amount"))["sum_amount"]
        representation["outside_budget_expense"] = (
            total_outside_expense_amount if total_outside_expense_amount else 0.0
        )
        representation["percent_amount_spent"] = percent_amount_spent
        representation["durations"] = (instance.end_date - instance.start_date).days

        return representation


class ListPaidBudgetCategoriesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = (
            "id",
            "main_title",
            "amount_recorded",
            "amount_paid",
            "amount_spent_percentage",
        )


class CreateRequisitionTransactionPinSerializer(serializers.Serializer):
    pin1 = serializers.CharField(max_length=4, min_length=4, required=True)
    pin2 = serializers.CharField(max_length=4, min_length=4, required=True)

    def validate(self, attrs):
        pin1 = attrs.get("pin1")
        pin2 = attrs.get("pin2")

        if pin1 != pin2:
            raise serializers.ValidationError(
                {"message": f"pin1-{pin1} does not match pin2-{pin2}"}
            )

        if not (str(pin1).isnumeric() or str(pin2).isnumeric()):
            raise serializers.ValidationError(
                {"message": "Only numeric values are required for PIN setup"}
            )
        return attrs


class UpdateAgencyTransactionPinSerializer(serializers.Serializer):
    transaction_pin = serializers.CharField(max_length=4, min_length=4, required=True)

    def validate(self, attrs):
        transaction_pin = attrs.get("transaction_pin")
        request = self.context.get("request")
        token = request.headers.get("Authorization").split(" ")[1]

        return attrs


class LinkTeamToBudgetSerializer(serializers.Serializer):
    budget = serializers.PrimaryKeyRelatedField(queryset=Budget.objects.all())
    team = serializers.PrimaryKeyRelatedField(queryset=Team.objects.all())

    def validate(self, attrs):
        # budget = attrs.get('budget')
        team = attrs.get("team")
        _active_budget = Budget.objects.filter(team=team, is_active=True).last()
        if _active_budget:
            raise serializers.ValidationError(
                {"message": f"Team {team.team_name} has a running budget"}
            )

        return attrs


class FundWalletSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    transaction_pin = serializers.CharField()
    # company = serializers.CharField()


class ReceiptDataSerializer(serializers.Serializer):
    receipt = serializers.ImageField()


class WallwtWithdrawalSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    transaction_pin = serializers.CharField()
    bank_code = serializers.CharField()
    account_no = serializers.CharField()
    account_name = serializers.CharField()
    bank_name = serializers.CharField()


class IndustrySerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyIndustry
        fields = "__all__"


class BudgetAllocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = BudgetAllocation
        fields = (
            "budget",
            "team",
            "amount",
        )

    def validate(self, attrs):
        budget = attrs.get("budget")
        # team = attrs.get('team')
        amount = attrs.get("amount")

        if not budget.team:
            raise serializers.ValidationError(
                {"message": "Kindly link a team the selected budget"}
            )
        allocations = BudgetAllocation.objects.filter(budget=budget, is_active=True)
        current_allocation = (
            allocations.aggregate(sum_amount=Sum("amount"))["sum_amount"] or 0
        )
        if amount > budget.budget_amount:
            raise serializers.ValidationError(
                {
                    "message": "Allocation amount cannot be greater than the running budget amount"
                }
            )

        initial_allocation_and_current_allocation = float(current_allocation) + float(
            amount
        )
        if initial_allocation_and_current_allocation > budget.budget_amount:
            raise serializers.ValidationError(
                {
                    "message": "Overall Allocation and current amount cannot be greater than the running budget amount"
                }
            )
        return attrs


class MonoPaymentHookSerializer(serializers.Serializer):
    event = serializers.CharField(required=True)
    data = serializers.DictField(required=True)


class GetMonokeyExchangCodeSerializer(serializers.Serializer):
    code = serializers.CharField()


class AnnulAllocationToCompanySerializer(serializers.Serializer):
    id = serializers.UUIDField()

    def validate(self, attrs):
        id = attrs.get("id")
        request = self.context.get("request")
        company_instance = Company.objects.filter(user=request.user, id=id).first()

        if not company_instance:
            raise serializers.ValidationError({"message": "Invalid company"})

        in_active_budget = Budget.objects.filter(
            company=company_instance,
            is_active=False,
            running_balance__gt=0,
            annul_balance=False,
        )

        # Calculate the total un_utilized budget by summing the 'running_balance' of the inactive budgets
        total_un_utilized = (
            in_active_budget.aggregate(sum_amount=Sum("running_balance"))["sum_amount"]
            or 0
        )

        if total_un_utilized <= 0:
            raise serializers.ValidationError(
                {"message": "No current running balance available."}
            )

        attrs["in_active_budget"] = in_active_budget
        attrs["total_un_utilized"] = total_un_utilized
        attrs["company_instance"] = company_instance
        return attrs


class TeamEmailSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, allow_null=False)

    def validate(self, attrs):
        email = attrs["email"].lower()
        company = self.context.get("company")
        attrs["email"] = email
        get_invite = TeamMemberInvite.objects.filter(
            company=company, email=email, status__in=["PENDING", "APPROVED"]
        ).first()
        if get_invite:
            if get_invite.status == "PENDING":
                raise serializers.ValidationError(
                    {"email": f"{email} has a pending invite request"}
                )
            elif get_invite.status == "APPROVED":
                raise serializers.ValidationError(
                    {"email": f"{email} has already been added to the company"}
                )

        return attrs


class MembersInviteSerializer(serializers.Serializer):
    members = serializers.ListSerializer(child=TeamEmailSerializer())

    def validate(self, attrs):
        company = self.context.get("company")
        members = attrs.get("members")
        if len(members) < 1:
            raise serializers.ValidationError({"members": "members cannot be empty"})
        # get_members = TeamMemberInvite.objects.filter(company=company, status__in=["PENDING", "APPROVED"])

        # team_member_emails = [member.email for member in get_members]

        # existing_emails = [
        #     email["email"] for email in members if email["email"] in team_member_emails
        # ]
        duplicate_email = find_duplicate_email(members)
        if duplicate_email:
            raise serializers.ValidationError(
                {"message": "duplicate email(s)", "data": duplicate_email}
            )
        return attrs


class SetDefaultCompanySerializer(serializers.Serializer):
    company_id = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())

    def validate(self, attrs):
        attrs["company"] = attrs.get("company_id")
        return super().validate(attrs)


class TeamMemberInviteSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamMemberInvite
        fields = ["id", "email", "company", "status"]

    def to_representation(self, instance):
        serialized_data = super().to_representation(instance)
        if serialized_data["company"]:
            serialized_data["company_name"] = instance.company.company_name
        else:
            serialized_data["company_name"] = ""
        return serialized_data


class TeamInviteApprovalSerializer(serializers.Serializer):
    TOGGLE_TYPE = (
        ("APPROVE", "APPROVE"),
        ("REJECT", "REJECT"),
    )
    toggle_type = serializers.ChoiceField(
        required=True, allow_blank=False, choices=TOGGLE_TYPE
    )

    toggle_type = serializers.ChoiceField(
        required=True, allow_blank=False, choices=TOGGLE_TYPE
    )


class PaginatedTeamMembersSerializer(serializers.Serializer):
    member_first_name = serializers.CharField()
    member_email = serializers.CharField()
    member_last_name = serializers.CharField()
    company_name = serializers.CharField()
    role_status = serializers.CharField()
    type = serializers.CharField()


class BulkDisbursementSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    requisitions = serializers.ListSerializer(child=serializers.IntegerField())
    company_id = serializers.CharField()
    team_id = serializers.CharField()
    transaction_pin = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    disburse_now = serializers.BooleanField(default=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        requisitions = validated_data.get("requisitions")
        company_id = validated_data.get("company_id")
        team_id = validated_data.get("team_id")
        transaction_pin = validated_data.get("transaction_pin")
        disburse = validated_data.get("disburse_now")

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise InvalidRequestException({"message": "Company not found"})

        try:
            team = Team.objects.get(id=team_id)
        except Team.DoesNotExist:
            raise InvalidRequestException({"message": "Invalid team ID or not found"})

        member = None
        team_member = team.members.filter(member=user)
        if team_member:
            member = team_member.first()
        allowed_roles = [
            UserRole.ADMIN,
            UserRole.DISBURSER,
            UserRole.OWNER,
            UserRole.SUB_ADMIN,
        ]
        if [(member and member.role in allowed_roles) or (company.user == user)][
            0
        ] is False:
            raise InvalidRequestException(
                {"message": "You do not have permission to perform this action"}
            )

        if len(requisitions) < 1:
            raise InvalidRequestException({"message": "Requisition cannot be empty"})

        wallet_type = company.company_wallet_type

        account_instance = AccountSystem.objects.filter(
            company=company,
            account_type=(
                "SPEND_MGMT" if wallet_type == "CORPORATE" else "NON_CORP_SPEND_MGMT"
            ),
        ).first()

        available_balance = 0
        wallet_obj = Wallet.objects.filter(account=account_instance)
        if wallet_obj.exists():
            wallet = wallet_obj.last()
            available_balance = wallet.balance

        amount_to_disburse = 0
        requisitions_not_found = list()
        req_requests = list()
        # Compute summary
        for req_id in requisitions:
            if Requisition.objects.filter(
                id=req_id, is_disbursed=False, is_inprogress=False, status="PENDING"
            ).exists():
                req = Requisition.objects.get(id=req_id)
                amount_to_disburse += req.request_amount
                req_requests.append(req)
            else:
                requisitions_not_found.append(req_id)

        if disburse is False:
            budget_amount = (
                Budget.objects.filter(team_id=team_id, is_active=True).aggregate(
                    Sum("budget_amount")
                )["budget_amount__sum"]
                or 0
            )
            spent_amount = (
                Requisition.objects.filter(
                    team_id=team_id, is_disbursed=True
                ).aggregate(Sum("request_amount"))["request_amount__sum"]
                or 0
            )
            data = {
                "success": True,
                "message": "Approval summary compiled",
                # "available_balance": available_balance,
                "amount_to_disburse": amount_to_disburse,
                "allocated_amount": budget_amount,
                "amount_spent": spent_amount,
                "requisitions_to_disburse_count": len(req_requests),
                "requisitions_not_found_count": len(requisitions_not_found),
            }
            return data

        if not req_requests:
            raise InvalidRequestException(
                {
                    "message": "Selected requisition(s) do not meet approval criteria or requisition not found"
                }
            )

        # Disburse now is True
        if amount_to_disburse > available_balance:
            raise InvalidRequestException(
                {"message": "Insufficient fund in company's wallet"}
            )

        if not transaction_pin:
            raise InvalidRequestException({"message": "Transaction PIN is required"})

        if user.requisition_transaction_pin is None:
            raise InvalidRequestException(
                {
                    "message": "Your transaction pin is not set. Please create, to perform transaction"
                }
            )

        password_match = check_password(
            transaction_pin, user.requisition_transaction_pin
        )

        if not password_match:
            raise InvalidRequestException({"message": "Invalid transaction pin"})

        user_id = str(user.id)
        acct_id = str(account_instance.id)
        requisitions_to_approve = [str(req.id) for req in req_requests]
        perform_bulk_requisition.delay(
            user_id, acct_id, wallet_type, requisitions_to_approve
        )

        return {"message": "Requisitions approved successfully"}


class ProcurementReturnProductSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = ProcurementReturnProduct
        exclude = []


class IndentProductSerializerOut(serializers.ModelSerializer):
    current_stock_details = serializers.SerializerMethodField()
    name = serializers.CharField(source="product.name")
    category_name = serializers.CharField(source="product.category.name")
    product_id = serializers.CharField(source="product.id")
    rejected = serializers.SerializerMethodField()

    def get_rejected(self, obj):
        items = list()
        rejects = ProcurementReturnProduct.objects.filter(indent_product=obj)
        if rejects.exists():
            items = ProcurementReturnProductSerializerOut(
                rejects, many=True, context={"request": self.context.get("request")}
            ).data
        return items

    def get_current_stock_details(self, obj):
        result = dict()
        current_count = current_price = 0
        if obj.product:
            stock_detail = StockDetail.objects.filter(item=obj.product)
            if stock_detail:
                current_count = stock_detail.last().quantity
                current_price = stock_detail.last().stock_price
        result["count"] = current_count
        result["price"] = current_price
        return result

    class Meta:
        model = IndentProduct
        exclude = ["product"]


class DeliveryDateValidator:
    def __call__(self, value):
        if datetime.strptime(value, "%Y-%m-%d").date() < datetime.now().date():
            raise InvalidRequestException(
                {"message": "Cannot select a past date for delivery"}
            )


class IndentProductSerializerIn(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    product_id = serializers.UUIDField()
    amount = serializers.FloatField()
    quantity = serializers.IntegerField()
    is_asset = serializers.BooleanField(default=False)
    depreciation_method = serializers.ChoiceField(
        choices=ASSET_DEPRECIATION_METHOD_CHOICES, required=False
    )
    useful_life = serializers.IntegerField(required=False)
    residual_value = serializers.FloatField(required=False)
    total_estimated_units = serializers.IntegerField(required=False)
    units_produced = serializers.IntegerField(required=False)
    depreciation_rate = serializers.DecimalField(
        required=False, decimal_places=2, max_digits=10
    )

    def validate(self, attrs):
        if attrs["is_asset"]:
            deep_method = attrs["depreciation_method"]
            lifetime = attrs["useful_life"]
            residual_value = attrs["residual_value"]

            if not all([deep_method, lifetime, residual_value]):
                raise serializers.ValidationError(
                    {
                        "asset": "Depreciation Method, Useful Life and Residual Value are required fields for asset items"
                    }
                )

        return attrs


class PurchaseIndentSerializerOut(serializers.ModelSerializer):
    products = serializers.SerializerMethodField()
    history = serializers.SerializerMethodField()
    supplier_name = serializers.CharField(source="supplier.name")
    supplier_address = serializers.CharField(source="supplier.get_full_address")
    supplier_phone = serializers.CharField(source="supplier.phone_number")
    company_info = serializers.SerializerMethodField()

    def get_company_info(self, obj):
        data = dict()
        data["company_name"] = obj.team.company.company_name if obj.team.company else ""
        data["address"] = obj.team.branch.address if obj.team.branch else ""
        data["phone_number"] = obj.team.branch.phone if obj.team.branch else ""
        return data

    def get_history(self, obj):
        history = list()
        history_data = PurchaseIndentHistory.objects.filter(indent=obj).order_by(
            "-created_at"
        )
        if history_data.exists():
            grouped_data = defaultdict(list)
            for history in history_data:
                date_str = history.created_at.strftime("%Y-%m-%d")
                grouped_data[date_str].append(history.detail)
            history = [
                {"date_created": date_string, "history": details}
                for date_string, details in grouped_data.items()
            ]
        return history

    def get_products(self, obj):
        result = list()
        if obj.products:
            for prod in obj.products.all():
                result.append(
                    IndentProductSerializerOut(
                        prod, context={"request": self.context.get("request")}
                    ).data
                )
        return result

    class Meta:
        model = PurchaseIndent
        exclude = []
        depth = 1

    def to_representation(self, instance):
        data = super().to_representation(instance)
        keys_to_remove = [
            "password",
            "agency_banking_transaction_pin",
            "requisition_transaction_pin",
            "liberty_pay_user_hash_passkey",
        ]
        if "requested_by" in data and data["requested_by"] is not None:
            for item in keys_to_remove:
                if item in data["requested_by"]:
                    del data["requested_by"][item]

        if "approved_by" in data and data["approved_by"] is not None:
            for item in keys_to_remove:
                if item in data["approved_by"]:
                    del data["approved_by"][item]

        return data


class PurchaseIndentSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    team_id = serializers.UUIDField()
    products = serializers.ListField(child=IndentProductSerializerIn(), required=False)
    indent_id = serializers.CharField(required=False, allow_null=True)
    vendor_id = serializers.UUIDField()
    payment_condition = serializers.ChoiceField(choices=PURCHASE_PAYMENT_TERMS_CHOICES)
    deliver_to = serializers.CharField(
        required=False, max_length=200, allow_blank=True, allow_null=True
    )
    delivery_date = serializers.CharField(
        required=False, validators=[DeliveryDateValidator()]
    )
    instant = serializers.BooleanField(default=False, required=False)
    otp_phone = serializers.CharField(required=False)

    # delivery_date = serializers.DateTimeField(required=False, validators=[DeliveryDateValidator()])

    def create(self, validated_data):
        user = validated_data.get("user")
        team_id = validated_data.get("team_id")
        products = validated_data.get("products")
        indent_id = validated_data.get("indent_id")
        vendor_id = validated_data.get("vendor_id")
        payment_condition = validated_data.get("payment_condition")
        dt = validated_data.get("delivery_date")
        deliver_to = validated_data.get("deliver_to")
        instant = validated_data.get("instant")
        otp_phone = validated_data.get("otp_phone")

        if not products:
            raise InvalidRequestException({"message": "Products are required"})

        try:
            member = TeamMember.objects.get(
                member=user, team__id=team_id, is_active=True
            )
        except TeamMember.DoesNotExist:
            raise InvalidRequestException(
                {
                    "message": "Please confirm that team exists and you're an active member"
                }
            )

        team = member.team

        try:
            supplier = Supplier.objects.get(id=vendor_id, company=team.company)
        except Supplier.DoesNotExist:
            raise InvalidRequestException({"message": "Supplier not found"})

        # GET ALL PRODUCTS
        # Confirm if each product in products belongs to team company

        indent_products, total_cost = create_ident_products(
            products, team, supplier, None
        )
        if not indent_products:
            raise InvalidRequestException(
                {"message": "Please select products within your domain/company"}
            )

        # Check remaining allocation against total cost
        success, detail = purchase_ident_with_budget_check(team, total_cost)
        if success is False:
            # Delete created Indent products and Asset Instances
            ip_ids = [ip.id for ip in indent_products]
            Asset.objects.filter(indent_product_id__in=ip_ids).delete()
            IndentProduct.objects.filter(id__in=ip_ids).delete()
            raise InvalidRequestException({"message": detail})

        if not indent_id:
            indent_id = (
                str(team.company.company_name).upper()[:4]
                + "-PI"
                + str(uuid.uuid4()).replace("-", "")[:20]
            )
        # Create Purchase indent
        # new_history = {"created_on": str(timezone.now()), "detail": "Procurement request created"}
        # history_data = add_retrieve_procurement_history("", str(new_history))
        delivery_date = datetime.strptime(dt, "%Y-%m-%d")
        indent = PurchaseIndent.objects.create(
            team=member.team,
            indent_no=indent_id,
            requested_by=user,
            supplier=supplier,
            payment_term=payment_condition,
            estimated_cost=total_cost,
            expected_date_of_delivery=delivery_date,
            delivery_address=deliver_to,
        )
        PurchaseIndentHistory.objects.create(
            detail="Procurement request created", indent=indent
        )

        for prd in indent_products:
            indent.products.add(prd)

        # Send emails to Admin(s) and owner
        allowed_roles = [
            UserRole.ADMIN,
            UserRole.DISBURSER,
            UserRole.OWNER,
            UserRole.SUB_ADMIN,
        ]
        emails = [
            str(team_member.email).lower()
            for team_member in TeamMember.objects.filter(
                team_id=team_id, role__in=[allowed_roles], is_active=True
            ).exclude(member=user)
        ]
        if emails:
            request_by = (
                str(user.first_name).title() + " " + str(user.last_name).title()
            )
            supplier_name = str(supplier.name).title()
            for email in emails:
                send_email.delay(
                    recipient=email,
                    subject="New Procurement Request",
                    requestor=request_by,
                    supplier_name=supplier_name,
                    template_dir="new_procurement.html",
                    order_no=indent_id,
                    amount=float(total_cost),
                    delivery_address=deliver_to,
                )

        if instant:
            # This block caters for APPROVE_WITH_OTP
            if not otp_phone:
                raise InvalidRequestException(
                    {"message": "Please add a phone number to receive SMS"}
                )
            otp_phone_no = format_phone_number(otp_phone)
            # Update indent, create accepted po, and create invoice
            indent.approval_status = "APPROVE_WITH_OTP"
            indent.instant_request = True
            indent.save()
            amt = indent.estimated_cost
            product_count = indent.products.count()
            po_id = (
                str(team.company.company_name).upper()[:4]
                + "-PO"
                + str(uuid.uuid4()).replace("-", "")[:20]
            )
            purchase_order = ProcurementPurchaseOrder.objects.create(
                indent=indent,
                order_no=po_id,
                initial_cost=amt,
                accepted_cost=amt,
                status="ACCEPTED",
                otp_phone_no=otp_phone_no,
            )
            ProcurementPurchaseInvoice.objects.create(
                purchase_order=purchase_order,
                pi_requester=user,
                procured_for=team,
                vendor=supplier,
                no_of_items=product_count,
                allocation_balance=team.procurementescrow,
            )

        return PurchaseIndentSerializerOut(
            indent, context={"request": self.context.get("request")}
        ).data

    def update(self, instance, validated_data):
        user = validated_data.get("user")
        products = validated_data.get("products")
        vendor_id = validated_data.get("vendor_id")

        if instance.approval_status != "PENDING_APPROVAL":
            raise InvalidRequestException(
                {
                    "message": "Cannot edit this procurement requisition, as it has passed it review stage"
                }
            )

        instance.payment_term = validated_data.get(
            "payment_condition", instance.payment_term
        )
        instance.expected_date_of_delivery = validated_data.get(
            "delivery_date", instance.expected_date_of_delivery
        )
        instance.indent_no = validated_data.get("indent_id", instance.indent_no)
        instance.delivery_address = validated_data.get(
            "deliver_to", instance.delivery_address
        )

        company = instance.team.company
        total_cost = 0

        if vendor_id:
            try:
                supplier = Supplier.objects.get(id=vendor_id, company=company)
                instance.supplier = supplier
            except Supplier.DoesNotExist:
                raise InvalidRequestException({"message": "Supplier not found"})

        if products:
            indent_products, total_cost = create_ident_products(
                products, instance.team, supplier, instance
            )
            instance.estimated_cost = total_cost
            for prd in indent_products:
                instance.products.add(prd)

        # Check remaining allocation against total cost
        success, detail = purchase_ident_with_budget_check(instance.team, total_cost)
        if success is False:
            raise InvalidRequestException({"message": detail})

        # new_history = {"created_on": str(timezone.now()), "detail": f"Procurement was edited by - {user.full_name}"}
        # history_data = add_retrieve_procurement_history(str(instance.history), str(new_history))
        # instance.history = history_data
        instance.save()
        PurchaseIndentHistory.objects.create(
            detail=f"Procurement was edited by - {user.full_name}", indent=instance
        )

        return PurchaseIndentSerializerOut(
            instance, context={"request": self.context.get("request")}
        ).data


class PurchaseIndentApproveSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    status = serializers.ChoiceField(choices=PURCHASE_INDENT_APPROVAL_TYPE_CHOICES)
    indent = serializers.ListField(child=serializers.UUIDField())
    decline_reason = serializers.CharField(required=False, allow_blank=False)
    transaction_pin = serializers.CharField()
    team_id = serializers.UUIDField()
    company_id = serializers.UUIDField()

    def create(self, validated_data):
        user = validated_data.get("user")
        indent_status = validated_data.get("status")
        indent_ids = validated_data.get("indent")
        team_id = validated_data.get("team_id")
        company_id = validated_data.get("company_id")
        transaction_pin = validated_data.get("transaction_pin")
        decline_reason = validated_data.get("decline_reason")

        if indent_status == "PENDING_APPROVAL":
            raise InvalidRequestException({"message": "Selected status is not allowed"})

        try:
            team = Team.objects.get(company_id=company_id, id=team_id)
        except Team.DoesNotExist:
            raise InvalidRequestException(
                {
                    "message": "Please confirm that team exists and you're an active member"
                }
            )

        # Get allocated budget and subtract estimated cost from it

        member = None
        team_member = team.members.filter(member=user)
        if team_member:
            member = team_member.first()
        allowed_roles = [
            UserRole.ADMIN,
            UserRole.DISBURSER,
            UserRole.OWNER,
            UserRole.SUB_ADMIN,
        ]
        if [(member and member.role in allowed_roles) or (team.company.user == user)][
            0
        ] is False:
            raise InvalidRequestException(
                {"message": "You do not have permission to perform this action"}
            )

        indent_list = list()
        suppliers_without_account_details = list()
        total_amount = 0

        if not user.requisition_transaction_pin:
            raise InvalidRequestException(
                {"message": "Please set your transaction PIN to perform transactions"}
            )

        password_match = check_password(
            transaction_pin, user.requisition_transaction_pin
        )
        if not password_match:
            raise InvalidRequestException({"message": "Invalid transaction pin"})

        for pk in indent_ids:
            procurement = PurchaseIndent.objects.filter(
                approval_status="PENDING_APPROVAL", team=team, id=pk
            )
            if procurement.exists():
                pi = procurement.last()
                indent_list.append(pi)
                total_amount += pi.estimated_cost
                # Check if supplier has account information
                supplier = pi.supplier
                if not any(
                    [
                        supplier.bank_code,
                        supplier.bank_name,
                        supplier.bank_account_name,
                        supplier.bank_account_number,
                    ]
                ):
                    suppliers_without_account_details.append(supplier.name)

        success, message = purchase_ident_with_budget_check(team, total_amount)
        if success is False:
            raise InvalidRequestException({"message": message})

        if indent_status == "DECLINED" and not decline_reason:
            raise InvalidRequestException(
                {"message": "Please add a reason for declining this request"}
            )

        wallet_type = team.company.company_wallet_type
        account_instance = AccountSystem.objects.filter(
            company=team.company,
            account_type=(
                "SPEND_MGMT" if wallet_type == "CORPORATE" else "NON_CORP_SPEND_MGMT"
            ),
        ).first()

        if indent_status == "APPROVE_ONLY":
            # Check if any supplier is missing account information
            if suppliers_without_account_details:
                raise InvalidRequestException(
                    {
                        "message": "Cannot find bank information for supplier(s)",
                        "suppliers_without_account_details": suppliers_without_account_details,
                    }
                )
            available_balance = 0
            wallet_obj = Wallet.objects.filter(account=account_instance)
            if wallet_obj.exists():
                wallet = wallet_obj.last()
                available_balance = wallet.balance

            if float(total_amount) > float(available_balance):
                raise InvalidRequestException(
                    {"message": "Insufficient fund in company's wallet"}
                )

        # Add balance to team procurement escrow balance
        escrow, _ = ProcurementEscrow.objects.get_or_create(team=team)
        escrow.balance += total_amount
        escrow.save()

        if indent_list:
            for indent in indent_list:
                # Create PO
                po_id = (
                    str(team.company.company_name).upper()[:4]
                    + "-PO"
                    + str(uuid.uuid4()).replace("-", "")[:20]
                )
                po = ProcurementPurchaseOrder.objects.create(
                    indent=indent,
                    order_no=po_id,
                    initial_cost=float(indent.estimated_cost),
                )
                if indent_status in [
                    "APPROVE_WITH_PO",
                    "APPROVE_WITH_INVOICE",
                    "APPROVE_WITH_OTP",
                ]:
                    # Create invoice
                    goods_count = po.indent.products.count()
                    ProcurementPurchaseInvoice.objects.create(
                        purchase_order=po,
                        pi_requester=po.indent.requested_by,
                        pi_approver=po.indent.approved_by,
                        procured_for=po.indent.team,
                        vendor=po.indent.supplier,
                        no_of_items=goods_count,
                        no_shipped=goods_count,
                        allocation_balance=escrow,
                    )
                    indent.approval_status = indent_status
                    indent.approved_by = user
                    indent.save()
                if indent_status == "APPROVE_ONLY":
                    po.accepted_cost = float(indent.estimated_cost)
                    po.save()
                    # Send fund to supplier's bank account and create transaction
                    send_fund_to_vendor_bank(po, account_instance, user, escrow)
                    ...

                if indent_status == "DECLINED":
                    escrow.balance -= indent.estimated_cost
                    escrow.save()
                    indent.approval_status = indent_status
                    indent.declined_by = user
                    indent.decline_reason = decline_reason
                    indent.save()
                    return "Procurement request(s) declined successfully"

                PurchaseIndentHistory.objects.create(
                    detail=f"Procurement request {indent_status} by: {user.full_name}",
                    indent=indent,
                )

            return "Procurement request(s) approved successfully"
        else:
            raise InvalidRequestException(
                {"message": "Unable to approved selected procurement(s)"}
            )


class PurchaseIndentBulkUploadSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    file = serializers.FileField()
    team_id = serializers.UUIDField()

    def create(self, validated_data):
        user = validated_data.get("user")
        file = validated_data.get("file")
        team_id = validated_data.get("team_id")
        creator = None

        try:
            team = Team.objects.get(id=team_id)
        except Team.DoesNotExist:
            raise InvalidRequestException({"message": "Invalid TeamID"})

        company = team.company

        team_member = TeamMember.objects.filter(member=user, team__company=company)
        if team_member.exists():
            creator = team_member.first().member

        if str(company.user_id).lower() == str(user.id).lower():
            creator = user

        if not creator:
            raise InvalidRequestException(
                {"message": "You do not have permission to perform this action"}
            )

        correct_data = list()
        incorrect_data = list()
        correct_response = list()

        response = extract_from_excel_or_csv(file)

        from stock_inventory.models import Category as ProductCategory

        for item in response:
            supplier_name = str(item.get("supplier_name")).lower()
            product_name = str(item.get("product_name"))

            supplier = Supplier.objects.filter(
                company=company, name__iexact=supplier_name
            )
            product = Product.objects.filter(company=company, name__iexact=product_name)

            if not supplier.exists():
                incorrect_data.append(item)
            else:
                supplier_data = supplier.last()
                data = dict()
                if not product.exists():
                    # Create product from backend and assign default category
                    default_category, _ = ProductCategory.objects.get_or_create(
                        company=company, name="default"
                    )
                    prod = Product.objects.create(
                        company=company,
                        category=default_category,
                        name=str(product_name).title(),
                        created_by=user,
                    )
                else:
                    prod = product.last()

                sup_id = supplier_data.id
                sup_name = supplier_data.name
                prod_id = prod.id
                prod_name = product_name
                price = item.get("amount")
                quantity = item.get("quantity")

                data["supplier_id"] = sup_id
                data["supplier_name"] = sup_name
                data["product_id"] = prod_id
                data["product_name"] = prod_name
                data["amount"] = price
                data["quantity"] = quantity

                correct_data.append(data)

        if correct_data:
            grouped_data = defaultdict(
                lambda: {"supplier_id": "", "supplier_name": "", "products": []}
            )

            for item in correct_data:
                supplier_name = item["supplier_name"]
                supplier_id = item["supplier_id"]
                grouped_data[supplier_name]["supplier_name"] = supplier_name
                grouped_data[supplier_name]["supplier_id"] = supplier_id

                # Append product details
                product = {
                    "product_id": f"{item['product_id']}",
                    "product_name": item["product_name"],
                    "quantity": item["quantity"],
                    "amount": item["amount"],
                }
                grouped_data[supplier_name]["products"].append(product)

            correct_response = grouped_data.values()

        return {
            "message": "Upload successful",
            "data": {
                "incorrect_data": incorrect_data,
                "correct_data": correct_response,
            },
        }


class BulkProcurementDataSerializerIn(serializers.Serializer):
    supplier_id = serializers.UUIDField()
    products = serializers.ListField(child=IndentProductSerializerIn())


class BulkProcurementSerailizerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    team_id = serializers.UUIDField()
    bulk_data = serializers.ListField(child=BulkProcurementDataSerializerIn())
    payment_condition = serializers.ChoiceField(choices=PURCHASE_PAYMENT_TERMS_CHOICES)
    deliver_to = serializers.CharField(
        required=False, max_length=200, allow_blank=True, allow_null=True
    )
    delivery_date = serializers.CharField(
        required=False, validators=[DeliveryDateValidator()]
    )

    def create(self, validated_data):
        user = validated_data.get("user")
        team_id = validated_data.get("team_id")
        bulk_data = validated_data.get("bulk_data")
        payment_condition = validated_data.get("payment_condition")
        dt = validated_data.get("delivery_date")
        deliver_to = validated_data.get("deliver_to")

        try:
            member = TeamMember.objects.get(member=user, team__id=team_id)
        except TeamMember.DoesNotExist:
            raise InvalidRequestException(
                {
                    "message": "Please confirm that team exists and you're an active member"
                }
            )

        team = member.team

        for item in bulk_data:
            vendor_id = item.get("supplier_id")

            try:
                supplier = Supplier.objects.get(id=vendor_id, company=team.company)
            except Supplier.DoesNotExist:
                raise InvalidRequestException({"message": "Supplier not found"})

            products = item.get("products")
            indent_products, total_cost = create_ident_products(
                products, team, supplier, None
            )
            total_cost += total_cost
            # product_indents.append(ind_prod for ind_prod in indent_products)

            indent_id = (
                str(team.company.company_name).upper()[:4]
                + "-PI"
                + str(uuid.uuid4()).replace("-", "")[:20]
            )
            # Create Purchase indent

            delivery_date = datetime.strptime(dt, "%Y-%m-%d")
            indent = PurchaseIndent.objects.create(
                team=member.team,
                indent_no=indent_id,
                requested_by=user,
                supplier=supplier,
                payment_term=payment_condition,
                estimated_cost=total_cost,
                expected_date_of_delivery=delivery_date,
                delivery_address=deliver_to,
            )
            PurchaseIndentHistory.objects.create(
                detail="Procurement request created", indent=indent
            )

            for prd in indent_products:
                indent.products.add(prd)

        return "Bulk procurements created successfully"


class ProcurementPurchaseOrderSerializerOut(serializers.ModelSerializer):
    indent = serializers.SerializerMethodField()
    purchase_order_status = serializers.CharField(source="status")
    invoice = serializers.SerializerMethodField()
    return_items = serializers.SerializerMethodField()

    def get_return_items(self, obj):
        rtn = ProcurementReturn.objects.filter(purchase_order=obj)
        if rtn.exists():
            return ProcurementReturnSerializerOut(rtn, many=True).data
        return None

    def get_invoice(self, obj):
        invoices = ProcurementPurchaseInvoice.objects.filter(purchase_order=obj)
        if invoices.exists():
            invoice = invoices.last()
            return invoice.id
        return None

    def get_indent(self, obj):
        if obj.indent:
            return PurchaseIndentSerializerOut(
                obj.indent, context={"request": self.context.get("request")}
            ).data

    class Meta:
        model = ProcurementPurchaseOrder
        exclude = ["status", "code_expiry", "otp_text"]


class SupplierSerializerOut(serializers.ModelSerializer):
    company_name = serializers.CharField(source="company.company_name")
    company_id = serializers.CharField(source="company.id")
    categories = serializers.SerializerMethodField()
    last_purchase_order = serializers.SerializerMethodField()
    total_amount_made = serializers.SerializerMethodField()
    pending_payment = serializers.SerializerMethodField()
    expected_goods = serializers.SerializerMethodField()

    def get_expected_goods(self, obj):
        expected = dict()
        po = ProcurementPurchaseOrder.objects.filter(
            indent__supplier=obj, delivery_status="NOT_DELIVERED", status="ACCEPTED"
        )
        if po.exists():
            expected["count"] = po.count()
            expected["amount"] = (
                po.aggregate(Sum("accepted_cost"))["accepted_cost__sum"] or 0
            )
        return expected

    def get_total_amount_made(self, obj):
        # Returning 0 for now, this will be calculated based on paid invoices
        return 0

    def get_pending_payment(self, obj):
        # Returning 0 for now, this will be calculated based on unpaid invoices
        pending_payment = {"count": 0, "amount": 0}
        return pending_payment

    def get_last_purchase_order(self, obj):
        data = dict()
        purchases = ProcurementPurchaseOrder.objects.filter(
            indent__supplier=obj
        ).order_by("-created_at")
        if purchases.exists():
            po = purchases.first()
            data["accepted_cost"] = po.accepted_cost
            data["initial_cost"] = po.initial_cost
            data["status"] = po.status
            data["expected_date_of_delivery"] = po.indent.expected_date_of_delivery
        return data

    def get_categories(self, obj):
        result = list()
        if obj.category:
            data = dict()
            for cat in obj.category.all():
                data["id"] = cat.id
                data["name"] = cat.name
                result.append(data)
        return result

    class Meta:
        model = Supplier
        exclude = ["company", "category"]


class SupplierDataSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    business_name = serializers.CharField()
    business_email = serializers.EmailField()
    address = serializers.CharField()
    state = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    bank_code = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    bank_name = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    account_no = serializers.IntegerField(required=False, allow_null=True)
    account_name = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    company_registration_no = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    lga = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    nearest_landmark = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    house_no = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    city = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    country = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    personal_email = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    personal_phone_no = serializers.CharField(required=False, allow_null=True)
    job_title = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    certificate_of_reg = serializers.FileField(
        required=False, allow_null=True, allow_empty_file=True
    )


class SupplierInviteSerializerIn(serializers.Serializer):
    business_email = serializers.EmailField()
    business_name = serializers.CharField()


class AddSupplierSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    company_id = serializers.UUIDField()
    addition_type = serializers.ChoiceField(choices=SUPPLIER_ADDITION_CHOICES)
    suppliers = serializers.ListField(child=SupplierDataSerializer(), required=False)
    suppliers_invite = serializers.ListField(
        child=SupplierInviteSerializerIn(), required=False
    )

    def create(self, validated_data):
        user = validated_data.get("user")
        addition_type = validated_data.get("addition_type")
        company_id = validated_data.get("company_id")
        suppliers_prefill = validated_data.get("suppliers")
        suppliers_invite = validated_data.get("suppliers_invite")
        creator = None
        suppliers = list()

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise InvalidRequestException({"message": "Invalid Company ID"})

        team_member = TeamMember.objects.filter(member=user, team__company=company)
        if team_member.exists():
            creator = team_member.first().member

        if str(company.user_id).lower() == str(user.id).lower():
            creator = user

        if not creator:
            raise InvalidRequestException(
                {"message": "You do not have permission to perform this action"}
            )

        suppliers_not_created = list()
        if addition_type == "PREFILL":
            suppliers = suppliers_prefill
        if addition_type == "INVITE":
            if not suppliers_invite:
                raise InvalidRequestException(
                    {"message": "List of supplier(s) email/business name is required"}
                )
            suppliers = suppliers_invite

        for vendor in suppliers:
            business_name = str(vendor.get("business_name")).lower()
            business_email = str(vendor.get("business_email"))
            name_exists = Supplier.objects.filter(name__iexact=business_name).exists()
            email_exists = User.objects.filter(email__iexact=business_email).exists()
            if name_exists or email_exists:
                suppliers_not_created.append(
                    {
                        "name": business_name,
                        "email": business_email,
                        "name_exist": name_exists,
                        "email_exist": email_exists,
                    }
                )

        if suppliers_not_created:
            raise InvalidRequestException(
                {
                    "message": "Some business name or email already exist on the platform, please review and try again",
                    "already_exist": suppliers_not_created,
                }
            )

        if addition_type == "PREFILL":
            if not suppliers:
                raise InvalidRequestException(
                    {"message": "List of supplier(s) to create is required"}
                )
            # Create Supplier(s)
            for vendor in suppliers:
                created_supplier = onboard_supplier(
                    vendor, company, approve_status=False, user=user
                )
            return {"message": "Supplier(s) created successfully"}

        if addition_type == "INVITE":
            suppliers_to_onboard = list()
            supplier_fe = settings.SUPPLIER_INVITE_FRONTEND_URL
            invitation_id = f"{supplier_fe}/onboard-vendor/{company.supplier_invite_id}?companyName={company.company_name}"
            company_name = str(company.company_name).title()
            for vendor in suppliers_invite:
                business_email = vendor.get("business_email")
                business_name = str(vendor.get("business_name")).title()
                if Supplier.objects.filter(name__iexact=business_name).exists():
                    suppliers_not_created.append(business_name)
                else:
                    # Send company invite link to vendors
                    suppliers_to_onboard.append(business_email)
                    send_email.delay(
                        recipient=business_email,
                        subject="Vendor Invite",
                        invite_url=invitation_id,
                        supplier_name=business_name,
                        template_dir="supplier_invite.html",
                        company_name=company_name,
                    )

            return {
                "message": "Supplier(s) created successful",
                "invite_link": str(invitation_id),
                "already_exist": suppliers_not_created,
            }


class SupplierCompleteOnboardingSerializerIn(SupplierDataSerializer):
    invite_id = serializers.CharField()
    categories = serializers.CharField(required=False, allow_blank=True)

    def create(self, validated_data):
        invitation_id = validated_data.get("invite_id")
        categories = validated_data.get("categories")  # Comma separated strings
        # Get company with the invite ID
        try:
            company = Company.objects.get(supplier_invite_id=invitation_id)
        except Company.DoesNotExist:
            raise InvalidRequestException({"message": "Company not found"})

        phone_no = validated_data.get("phone_number")
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        business_name = validated_data.get("business_name")
        business_email = validated_data.get("business_email")
        address = validated_data.get("address")
        state = validated_data.get("state")
        country = validated_data.get("country")
        bank_code = validated_data.get("bank_code")
        bank_name = validated_data.get("bank_name")
        account_no = validated_data.get("account_no")
        account_name = validated_data.get("account_name")
        company_registration_no = validated_data.get("company_registration_no")
        lga = validated_data.get("lga")
        nearest_landmark = validated_data.get("nearest_landmark")
        house_no = validated_data.get("house_no")
        city = validated_data.get("city")
        personal_email = validated_data.get("personal_email")
        personal_phone_no = validated_data.get("personal_phone_no")
        job_title = validated_data.get("job_title")
        certificate_of_reg = validated_data.get("certificate_of_reg")

        if Supplier.objects.filter(
            name__iexact=business_name, company=company
        ).exists():
            raise InvalidRequestException(
                {"message": "Business with this name already registered"}
            )

        if User.objects.filter(email__iexact=business_email).exists():
            raise InvalidRequestException({"message": "Email already registered"})

        phone_number = format_phone_number(phone_no)
        personal_phone_number = format_phone_number(personal_phone_no)
        personal_full_name = str(first_name) + " " + str(last_name)

        if not all(
            [
                lga,
                nearest_landmark,
                house_no,
                city,
                personal_email,
                personal_phone_no,
                job_title,
            ]
        ):
            raise InvalidRequestException(
                {"message": "Please ensure to fill all required fields"}
            )

        # Create Supplier
        supplier = Supplier.objects.create(
            company=company,
            created_by=company.user,
            name=business_name,
            email=business_email,
            phone_number=phone_number,
            address=address,
            bank_name=bank_name,
            bank_account_name=account_name,
            bank_account_number=account_no,
            landmark=nearest_landmark,
            lga=lga,
            city=city,
            state=state,
            contact_person_name=personal_full_name,
            contact_person_email=personal_email,
            contact_person_phone_no=personal_phone_number,
            contact_person_position=job_title,
            registration_no=company_registration_no,
            registration_doc=certificate_of_reg,
            bank_code=bank_code,
            country=country,
        )
        try:
            category_list = str(categories).replace(" ", "").split(",")
            for cat in category_list:
                supplier.category.add(cat)
        except Exception as err:
            print(str(err))
            pass

        if company.auto_approve_suppliers:
            supplier.is_active = True
            supplier.is_approved = True
            supplier.approved_at = datetime.now()
            supplier.save()

            supplier_email = business_email
            # Generate Authentication and send to supplier's email
            random_password = User.objects.make_random_password()
            supplier_user = User.objects.create(
                email=supplier_email, is_supplier=True, first_name=business_name
            )
            supplier_user.set_password(random_password)
            supplier_user.save()

            send_email.delay(
                recipient=business_email,
                subject="Vendor Account Approved",
                login_url=login_url,
                supplier_name=business_name,
                template_dir="supplier_approval.html",
                email=business_email,
                password=random_password,
            )

            return {
                "message": "Registration completed, and login information sent to your email address"
            }
        company_name = str(company.company_name).title()

        return {
            "message": f"Request submitted, you will receive login information as soon as {company_name} approves this request"
        }


class ApproveSupplierSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    supplier_id = serializers.UUIDField()

    def create(self, validated_data):
        user = validated_data.get("user")
        supplier_id = validated_data.get("supplier_id")

        allowed_roles = [
            UserRole.ADMIN,
            UserRole.DISBURSER,
            UserRole.OWNER,
            UserRole.SUB_ADMIN,
        ]

        try:
            supplier = Supplier.objects.get(id=supplier_id, is_approved=False)
        except Supplier.DoesNotExist:
            raise InvalidRequestException({"message": "Supplier not found"})

        supplier_email = str(supplier.email)
        supplier_name = str(supplier.name)

        if User.objects.filter(email__iexact=supplier_email).exists():
            raise InvalidRequestException(
                {
                    "message": "User with supplier's email already registered, please contact admin"
                }
            )

        member = TeamMember.objects.filter(
            member=user, team__company=supplier.company, role__in=allowed_roles
        )
        if not member.exists():
            raise InvalidRequestException(
                {"message": "You are not permitted to perform this action"}
            )

        # Update supplier's state
        supplier.is_active = True
        supplier.is_approved = True
        supplier.approved_at = datetime.now()
        supplier.updated_by = user
        supplier.save()

        # Generate Authentication and send to supplier's email
        random_password = User.objects.make_random_password()
        supplier_user = User.objects.create(
            email=supplier_email, is_supplier=True, first_name=supplier.name
        )
        supplier_user.set_password(random_password)
        supplier_user.save()

        send_email.delay(
            recipient=supplier_email,
            subject="Vendor Account Approved",
            login_url=login_url,
            supplier_name=supplier_name,
            template_dir="supplier_approval.html",
            email=supplier_email,
            password=random_password,
        )

        return SupplierSerializerOut(
            supplier, context={"request": self.context.get("request")}
        ).data


class SupplierLoginSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField()

    def create(self, validated_data):
        email = validated_data.get("email")
        password = validated_data.get("password")

        try:
            user = User.objects.get(email__iexact=email, is_supplier=True)
            if user.check_password(password):
                return user
            raise InvalidRequestException({"message": "Incorrect email or password"})
        except User.DoesNotExist:
            raise InvalidRequestException({"message": "Incorrect email or password"})


class SupplierApproveDeclinePurchaseOrderSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    purchase_order_id = serializers.UUIDField()
    approval_status = serializers.CharField()
    decline_reason = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )

    def create(self, validated_data):
        user = validated_data.get("user")
        po_id = validated_data.get("purchase_order_id")
        approval = validated_data.get("approval_status")
        reason = validated_data.get("decline_reason")

        allowed_status = ["ACCEPTED", "DENIED"]

        if approval not in allowed_status:
            raise InvalidRequestException(
                {"message": "Approval status can only be ACCEPTED or DENIED"}
            )

        try:
            po = ProcurementPurchaseOrder.objects.get(
                id=po_id,
                status="UNDER_REVIEW",
                indent__supplier__email__iexact=user.email,
            )
        except ProcurementPurchaseOrder.DoesNotExist:
            raise InvalidRequestException({"message": "Invalid purchase order"})

        if approval == "DENIED" and not reason:
            raise InvalidRequestException(
                {"message": "Please add a reason for declining this Purchase Order"}
            )

        po.status = approval
        po.decline_reason = reason
        po.save()
        ap_status = str(approval).lower()

        PurchaseIndentHistory.objects.create(
            detail=f"Supplier {ap_status} purchase order", indent=po.indent
        )

        return {
            "message": f"Purchase order {ap_status} successfully",
            "data": ProcurementPurchaseOrderSerializerOut(po).data,
        }


class PurchaseOrderCommentSerializerOut(serializers.ModelSerializer):
    sender = serializers.SerializerMethodField()
    replies = serializers.SerializerMethodField()

    def get_sender(self, obj):
        return {
            "id": obj.sender.id,
            "first_name": obj.sender.first_name,
            "last_name": obj.sender.last_name,
            "email": obj.sender.email,
        }

    def get_replies(self, obj):
        replies = list()
        comment_replies = PurchaseOrderComment.objects.filter(parent_comment=obj)
        if comment_replies.exists():
            for comment in comment_replies:
                replies.append(
                    {
                        "id": comment.id,
                        "message": comment.message,
                        "sender_first_name": comment.sender.first_name,
                        "sender_last_name": comment.sender.last_name,
                        "sender_email": comment.sender.email,
                        "created_at": comment.created_at,
                        "edited_at": comment.edited_at,
                    }
                )
        return replies

    class Meta:
        model = PurchaseOrderComment
        exclude = ["parent_comment"]


class PurchaseOrderCommentSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    purchase_order_id = serializers.UUIDField(required=False)
    parent_comment_id = serializers.UUIDField(required=False)
    message = serializers.CharField()

    def create(self, validated_data):
        user = validated_data.get("user")
        purchase_order_id = validated_data.get("purchase_order_id")
        parent_comment_id = validated_data.get("parent_comment_id")
        message = validated_data.get("message")

        if not purchase_order_id:
            raise InvalidRequestException({"message": "Purchase Order ID is required"})

        query = (
            Q(indent__team__members__member__in=[user])
            | Q(indent__team__company__user=user)
            | Q(indent__supplier__email__iexact=user.email)
        )

        purchase_order = ProcurementPurchaseOrder.objects.filter(
            query, id=purchase_order_id
        )
        if not purchase_order:
            raise InvalidRequestException({"message": "Purchase Order not found"})

        po = purchase_order.last()

        # Create Comment
        comment = PurchaseOrderComment.objects.create(
            sender=user, purchase_order=po, message=message
        )
        if parent_comment_id:
            # Get parent comment
            try:
                p_comment = PurchaseOrderComment.objects.get(
                    id=parent_comment_id, purchase_order=po
                )
            except PurchaseOrderComment.DoesNotExist:
                raise InvalidRequestException({"message": "Parent comment not found"})
            comment.parent_comment = p_comment
            comment.save()

        return PurchaseOrderCommentSerializerOut(comment).data

    def update(self, instance, validated_data):
        instance.message = validated_data.get("message", instance.message)
        instance.edited_at = timezone.now()
        instance.save()

        return PurchaseOrderCommentSerializerOut(instance).data


class ProcurementPurchaseInvoiceSerializerIn(serializers.Serializer):
    po_invoice_number = serializers.CharField(max_length=11, read_only=True)
    document_date = serializers.DateTimeField(default=timezone.now)
    Due_date = serializers.DateTimeField(required=False, allow_null=True)
    # status = serializers.ChoiceField(choices=ProcurementPurchaseInvoice.ApprovalStatus.choices)
    purchase_order = serializers.PrimaryKeyRelatedField(
        queryset=ProcurementPurchaseOrder.objects.all()
    )
    pi_requester = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(), required=False, allow_null=True
    )
    pi_approver = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(), required=False, allow_null=True
    )
    procured_for = serializers.PrimaryKeyRelatedField(
        queryset=Team.objects.all(), required=False, allow_null=True
    )
    vendor = serializers.PrimaryKeyRelatedField(
        queryset=Supplier.objects.all(), required=False, allow_null=False
    )
    procurement_type = serializers.ChoiceField(
        choices=ProcurementPurchaseInvoice.ProcurementType.choices,
        required=False,
        allow_null=True,
    )
    no_of_items = serializers.IntegerField(default=1)
    no_shipped = serializers.IntegerField(default=1)
    shipping_fee = serializers.FloatField(default=0.0)
    vat = serializers.FloatField(
        default=7.5,
        validators=[MaxValueValidator(100)],
        required=False,
        allow_null=True,
    )
    # hard_copy_upload = serializers.FileField(required=False, allow_null=True)
    allocation_balance = serializers.PrimaryKeyRelatedField(
        queryset=ProcurementEscrow.objects.all(), required=False, allow_null=True
    )
    accepted = serializers.BooleanField(default=False)
    paid = serializers.ChoiceField(
        choices=ProcurementPurchaseInvoice.PaidOption.choices,
        required=False,
        allow_null=True,
    )
    # notes = serializers.PrimaryKeyRelatedField(queryset=Notes.objects.all(), required=False, allow_null=True)

    amount_paid = serializers.FloatField(default=0.0)
    received_date = serializers.DateField(required=False, allow_null=True)
    payment_terms = serializers.CharField(max_length=100)

    def create(self, validated_data):
        return ProcurementPurchaseInvoice.objects.create(**validated_data)

    def update(self, instance, validated_data):
        for current_field, new_value in validated_data.items():
            setattr(instance, current_field, new_value)
        instance.save()
        return instance

    def delete(self, instance):
        instance.delete()
        return {"message": "Invoice deleted successfully"}


class NotesSerializer(serializers.ModelSerializer):
    created_by_username = serializers.CharField(
        source="created_by.username", read_only=True
    )

    class Meta:
        model = Notes
        fields = [
            "id",
            "invoice_no",
            "notes",
            "created_by",
            "created_at",
            "created_by_username",
        ]
        read_only_fields = ["created_by_username"]


class ProcurementPurchaseInvoiceUploadedFilesSerializer(serializers.ModelSerializer):
    hard_copy_upload = serializers.FileField(required=False)

    class Meta:
        model = ProcurementPurchaseInvoiceUploadedFIles
        fields = "__all__"


class ProcurementPurchaseInvoiceSerializerModify(serializers.Serializer):
    po_invoice_number = serializers.CharField(max_length=11, read_only=True)
    document_date = serializers.DateTimeField(default=timezone.now)
    Due_date = serializers.DateTimeField(required=False, allow_null=True)
    purchase_order = serializers.PrimaryKeyRelatedField(
        queryset=ProcurementPurchaseOrder.objects.all(), required=False, allow_null=True
    )
    pi_requester = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(), required=False, allow_null=True
    )
    pi_approver = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(), required=False, allow_null=True
    )
    procured_for = serializers.PrimaryKeyRelatedField(
        queryset=Team.objects.all(), required=False, allow_null=True
    )
    vendor = serializers.PrimaryKeyRelatedField(
        queryset=Supplier.objects.all(), required=False, allow_null=False
    )
    procurement_type = serializers.ChoiceField(
        choices=ProcurementPurchaseInvoice.ProcurementType.choices,
        required=False,
        allow_null=True,
    )
    no_of_items = serializers.IntegerField(required=False, allow_null=True)
    no_shipped = serializers.IntegerField(required=False, allow_null=True)
    shipping_fee = serializers.FloatField(required=False, allow_null=True)
    vat = serializers.FloatField(
        default=7.5,
        validators=[MaxValueValidator(100)],
        required=False,
        allow_null=True,
    )
    hard_copy_upload = serializers.FileField(required=False, allow_null=True)
    accepted = serializers.BooleanField(required=False, allow_null=True)
    paid = serializers.BooleanField(required=False, allow_null=True)
    note = serializers.CharField(required=False, allow_null=True)

    def validate_hard_copy_upload(self, file):
        if not file.name.lower().endswith((".png", ".jpg", ".jpeg", ".pdf")):
            raise serializers.ValidationError(
                "Unsupported file format. Only PNG, JPG, JPEG, and PDF are allowed."
            )
        return file

    def extract_text_and_structure(self, file):
        # pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        pytesseract.tesseract_cmd = r"/usr/bin/tesseract"

        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            for chunk in file.chunks():
                temp_file.write(chunk)
            temp_file_path = temp_file.name

        try:
            with Image.open(temp_file_path) as img:
                raw_text = pytesseract.image_to_string(img)
                print(raw_text)

                prompt = f"""
                    I have the following OCR raw text: "{raw_text}". 
                    The text contains details of an invoice, such as the invoice number, list of items, number of items, total, subtotal, company name, company address, and date.

                    Please parse and return a **single JSON object** with the key 'invoice_details' in this format:
                    {{
                        "invoice_details": {{
                            "invoice_number": "",
                            "items": [],
                            "total": "",
                            "subtotal": "",
                            "company_name": "",
                            "address": "",
                            "Date": ""
                        }}
                    }}

                    Ensure your output is strictly valid JSON with no additional text or formatting. If the input text does not resemble an invoice, return an empty JSON object.
                    """

            SYSTEM_MSG = (
                "You are a data extraction assistant tasked with processing raw text generated from OCR. "
                "Your goal is to analyze the provided text, extract relevant invoice details (e.g., invoice number, list of items, number of items, total, subtotal, company name, company address, and date), "
                "and structure this data into a JSON object. Correct any errors in the OCR results using contextual reasoning where necessary. "
                "If the text does not contain invoice-related details, return an empty JSON object without additional explanations. "
                "Ensure the JSON format strictly adheres to the specified structure and maintain a high level of accuracy."
            )

            payload = json.dumps(
                {"prompt": prompt, "system_msg": SYSTEM_MSG, "model": "gpt-3.5-turbo"}
            )

            headers = {
                "Authorization": "9745-26ed188f48f0-4b98b89f-626bb781",
                "Content-Type": "application/json",
            }

            url = "https://backend-dev.getlinked.ai/services/chatgpt-prompt/"

            response = requests.post(url, headers=headers, data=payload)

            try:
                structured_data = response.json()
                raw_json_string = structured_data.get("response")

                parsed_data = json.loads(raw_json_string)
                # print("Returned:", parsed_data)

                return parsed_data

            except (ValueError, json.JSONDecodeError):
                raise serializers.ValidationError(
                    "Invalid response from OCR processing API."
                )
        except Exception as e:
            raise serializers.ValidationError(f"Error during OCR processing: {str(e)}")
        finally:
            import os

            os.unlink(temp_file_path)

    def compare_data(self, extracted_data, expected_data):
        discrepancies = []
        for key, value in expected_data.items():
            if extracted_data.get(key) != value:
                discrepancies.append(
                    {key: {"expected": value, "found": extracted_data.get(key)}}
                )
        return discrepancies

    def process_file(self, file, expected_data=None):
        extracted_data = self.extract_text_and_structure(file)
        discrepancies = []
        if expected_data:
            discrepancies = self.compare_data(extracted_data, expected_data)
        return {"extracted_data": extracted_data, "discrepancies": discrepancies}

    def save_extracted_data(self, instance, structured_data):
        # instance.no_of_items = len(structured_data["items"])
        # instance.total = structured_data["footer"].get("total", 0)
        instance.save()

    def update(self, instance, validated_data):
        if "hard_copy_upload" in validated_data:
            file = validated_data.get("hard_copy_upload")
            structured_data = self.extract_text_and_structure(file)
            self.save_extracted_data(instance, structured_data)

        for current_field, new_value in validated_data.items():
            setattr(instance, current_field, new_value)
        instance.save()
        return instance


class ProcurementPurchaseInvoiceSerializerOut(serializers.ModelSerializer):
    subtotal = serializers.SerializerMethodField()
    calculate_total = serializers.SerializerMethodField()
    purchase_order = serializers.SerializerMethodField()
    order_no = serializers.CharField(source="purchase_order.order_no")
    pi_requester = serializers.SerializerMethodField()
    pi_approver = serializers.SerializerMethodField()
    vendor = serializers.SerializerMethodField()
    allocation_balance = serializers.SerializerMethodField()
    procured_for = serializers.SerializerMethodField()
    items = serializers.SerializerMethodField()
    activity_log = serializers.SerializerMethodField()

    class Meta:
        model = ProcurementPurchaseInvoice
        exclude = []

    def get_allocation_balance(self, instance):
        if instance.allocation_balance:
            return {
                "id": instance.allocation_balance.id or None,
                "team": instance.allocation_balance.team.team_name or None,
                "balance": instance.allocation_balance.balance or None,
            }
        return None

    def get_purchase_order(self, instance):
        if instance.purchase_order:
            return {
                "id": instance.purchase_order.id,
                "order_no": instance.purchase_order.order_no,
            }

    def get_pi_requester(self, instance):
        if instance.pi_requester:
            default_company = instance.pi_requester.default_company
            return {
                "id": instance.pi_requester.id or None,
                "first_name": instance.pi_requester.first_name or None,
                "last_name": instance.pi_requester.last_name or None,
                "email": instance.pi_requester.email or None,
                "company": default_company.company_name if default_company else None,
                "company_id": default_company.id if default_company else None,
                "phone": instance.pi_requester.phone_no or None,
                "account_no": instance.pi_requester.account_no or None,
                "account_name": instance.pi_requester.account_name or None,
                "bank": instance.pi_requester.bank or None,
            }
        return None

    def get_pi_approver(self, instance):
        if instance.pi_approver:
            return {
                "id": instance.pi_approver.id if instance.pi_approver else None,
                "first_name": (
                    instance.pi_approver.first_name if instance.pi_approver else None
                ),
                "last_name": (
                    instance.pi_approver.last_name if instance.pi_approver else None
                ),
                "email": instance.pi_approver.email if instance.pi_approver else None,
            }
        return None

    def get_procured_for(self, instance):
        if instance.procured_for:
            branch = instance.procured_for.branch
            return {
                "team_id": instance.procured_for.id,
                "team_name": instance.procured_for.team_name or None,
                "team_type": instance.procured_for.team_type or None,
                "branch_name": branch.name if branch else None,
                "address": branch.address if branch else None,
                "phone": branch.phone if branch else None,
            }
        return None

    def get_vendor(self, instance):
        if instance.vendor:
            return {
                "id": instance.vendor.id if instance.vendor else None,
                "name": instance.vendor.name if instance.vendor else None,
                "company": (
                    instance.vendor.company.company_name if instance.vendor else None
                ),
                "registration_no": (
                    instance.vendor.registration_no if instance.vendor else None
                ),
                "bank_account_name": (
                    instance.vendor.bank_account_name if instance.vendor else None
                ),
                "bank_account_number": (
                    instance.vendor.bank_account_number if instance.vendor else None
                ),
                "address": instance.vendor.address if instance.vendor else None,
                "phone_no": instance.vendor.phone_number if instance.vendor else None,
            }
        return None

    def get_subtotal(self, instance):
        return instance.subtotal()

    def get_calculate_total(self, instance):
        return instance.calculate_total()

    def get_items(self, instance):
        products = instance.items()
        return [
            {
                "name": product.product.name if products else None,
                "unit_price": product.product.product_price if products else None,
                "quantity": product.quantity if products else None,
                "estimated_amount": product.estimated_amount if products else None,
                "Amount": (
                    product.product.product_price * product.quantity
                    if products
                    else None
                ),
            }
            for product in products
        ]

    def get_activity_log(self, instance):

        vendor = instance.vendor
        reviewer = instance.pi_approver
        approver = instance.pi_approver

        return {
            "received_from": f"Invoice was received from {vendor.name if vendor else None}",
            "reviewer": f"Invoice was reviewed by {reviewer.email if reviewer else None}",
            "approver": f"Invoice was approved by {approver.email if approver else None}",
        }


class UploadedFilePreviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProcurementPurchaseInvoice
        fields = ["hard_copy_upload", "hard_copy_upload_url"]

    def get_hard_copy_upload_url(self, instance):
        request = self.context.get("request")
        if instance.hard_copy_upload and request:
            return request.build_absolute_uri(instance.hard_copy_upload.url)
        return None


class ConfirmProcurementPurchaseAmountPaidSerializer(serializers.Serializer):
    amount_paid = serializers.FloatField(min_value=0.1)


class FlagsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Flags
        exclude = ["invoice_no", "id", "updated_at", "created_at"]


class CompanyPaymentWalletSeriaizer(serializers.ModelSerializer):
    company = serializers.SerializerMethodField()

    class Meta:
        model = AccountSystem
        fields = ["id", "company", "account_type", "available_balance"]

    def get_company(self, instance):
        if instance.company:
            return {
                "company_id": instance.id,
                "company_name": (
                    instance.company.company_name if instance.company else None
                ),
                "wallet_type": (
                    instance.company.company_wallet_type
                    if instance.company.company_wallet_type
                    else None
                ),
            }
        return None


class EnterAmountToPaySerialzer(serializers.Serializer):
    enter_amount = serializers.FloatField()


class DisbursementPinSerializer(serializers.Serializer):
    enter_amount = serializers.FloatField()
    pin = serializers.IntegerField(required=True)


class ReturnProductsSerializerIn(serializers.Serializer):
    indent_product_id = serializers.UUIDField()
    quantity = serializers.IntegerField()
    image = serializers.ImageField()
    return_reason = serializers.ChoiceField(choices=RETURN_REASON_CHOICES)

    def validate(self, attrs):
        allowed_types = ["image/jpeg", "image/png", "image/gif"]
        max_file_size = 5 * 1024 * 1024
        allowed_reasons = [
            "DAMAGED",
            "MISSING",
            "WRONG_SPECIFICATION",
            "LATE_DELIVERY",
            "DEFECTED",
            "OTHERS",
        ]

        image = attrs["image"]
        if image.content_type not in allowed_types:
            raise serializers.ValidationError({"image": "Invalid image format"})
        if image.size > max_file_size:
            raise serializers.ValidationError({"image": "Image too large"})
        if attrs["quantity"] < 1:
            raise serializers.ValidationError(
                {"quantity": "Quantity must be at least 1"}
            )
        if not attrs["return_reason"] in allowed_reasons:
            raise serializers.ValidationError(
                {"return_reason": "Invalid return reason selected"}
            )

        return attrs


class ReceivedProductSerializerIn(serializers.Serializer):
    indent_product_id = serializers.UUIDField()
    quantity = serializers.IntegerField()

    def validate(self, attrs):
        if attrs["quantity"] < 1:
            raise serializers.ValidationError(
                {"quantity": "Quantity must be at least 1"}
            )

        return attrs


class ConfirmDeliverySerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    purchase_order_id = serializers.UUIDField()
    delivery_status = serializers.ChoiceField(choices=PURCHASE_DELIVERY_MODE_CHOICES)
    return_status = serializers.ChoiceField(choices=DELIVERY_WITH_RETURNS_CHOICES)
    return_products = serializers.ListField(
        child=ReturnProductsSerializerIn(), required=False
    )
    comment = serializers.CharField(required=False)
    return_no = serializers.CharField(required=False)
    delivered_products = serializers.ListField(
        child=ReceivedProductSerializerIn()
    )  # This is list of indent products for delivered items

    def create(self, validated_data):
        user = validated_data.get("user")
        purchase_order_id = validated_data.get("purchase_order_id")
        delivery_status = validated_data.get("delivery_status")
        return_status = validated_data.get("return_status")
        return_products = validated_data.get("return_products")
        return_no = validated_data.get("return_no")
        comment = validated_data.get("comment")
        delivered_products = validated_data.get("delivered_products")

        if delivery_status == "NOT_DELIVERED":
            raise InvalidRequestException({"message": "Invalid delivery status"})

        query = Q(indent__team__members__member__in=[user]) | Q(
            indent__team__company__user=user
        )
        purchase_order = ProcurementPurchaseOrder.objects.filter(
            query, id=purchase_order_id, status="ACCEPTED"
        )
        if not purchase_order:
            raise InvalidRequestException({"message": "Purchase Order not found"})

        po = purchase_order.last()

        if po.delivery_status == "FULLY_DELIVERED":
            raise InvalidRequestException(
                {"message": "Purchase Order already fully delivered"}
            )

        invalid_indent_product = list()
        un_allowed_quantity = list()
        un_allowed_total_received = list()

        if not return_no:
            return_no = (
                str(po.indent.team.company.company_name).upper()[:4]
                + "-PR"
                + str(uuid.uuid4()).replace("-", "")[:20]
            )

        for item in delivered_products:
            indent_product_id = item.get("indent_product_id")
            quantity = item.get("quantity")
            try:
                indent_product = IndentProduct.objects.get(id=indent_product_id)
                received_total = indent_product.received + quantity
                prod_name = indent_product.product.name
                if received_total > indent_product.quantity:
                    un_allowed_total_received.append(prod_name)
            except IndentProduct.DoesNotExist:
                invalid_indent_product.append("Invalid Product")

        if un_allowed_total_received:
            raise InvalidRequestException(
                {
                    "message": "Received products cannot be greater than expected delivery",
                    "invalid_products": invalid_indent_product,
                    "unallowed_quantity": un_allowed_quantity,
                    "unallowed_total": un_allowed_total_received,
                }
            )

        if return_products:
            for prod in return_products:
                # Check if indent product belongs to the purchase order and quantity returning is not greater than initial ordered
                indent_id = prod.get("indent_product_id")
                return_qty = prod.get("quantity")
                indent_product = None

                try:
                    indent_product = IndentProduct.objects.get(id=indent_id)
                    indent_product_name = indent_product.product.name
                    if return_qty > indent_product.quantity:
                        un_allowed_quantity.append(indent_product_name)
                except IndentProduct.DoesNotExist:
                    indent_product_name = "Invalid Product"

                if indent_product not in po.indent.products.all():
                    invalid_indent_product.append(indent_product_name)

        if invalid_indent_product or un_allowed_quantity:
            raise InvalidRequestException(
                {
                    "message": "Invalid product or quantity for selected purchase order",
                    "invalid_products": invalid_indent_product,
                    "unallowed_quantity": un_allowed_quantity,
                }
            )

        if return_status == "WITH_RETURNS":
            if not return_products:
                raise InvalidRequestException({"message": "Please add items to return"})
            # Create Procurement Return, with Return Products
            po_return = ProcurementReturn.objects.create(
                purchase_order=po, return_no=return_no, description=comment
            )
            for prod in return_products:
                indent_product_id = prod.get("indent_product_id")
                quantity = prod.get("quantity")
                image = prod.get("image")
                return_reason = prod.get("return_reason")
                prd = ProcurementReturnProduct.objects.create(
                    indent_product_id=indent_product_id,
                    quantity=quantity,
                    image=image,
                    return_reason=return_reason,
                )
                po_return.return_products.add(prd)
        # Add product to stock if status is fully or partially delivered
        for item in delivered_products:
            indent_product_id = item.get("indent_product_id")
            qty = item.get("quantity")
            int_product = IndentProduct.objects.get(id=indent_product_id)
            received_total = int_product.received + qty
            int_product.received = (
                int_product.quantity
                if int_product.quantity > int_product.received
                else received_total
            )
            int_product.save()
            if int_product.quantity == int_product.received:
                int_product.delivered = True
                int_product.save()

            # Add quantity to stock
            try:
                stock = StockDetail.objects.get(item=int_product.product)
                stock.quantity += qty
                stock.save()
            except (StockDetail.DoesNotExist, StockDetail.MultipleObjectsReturned):
                pass

        # Update PurchaseOrder
        po.delivery_status = delivery_status
        po.confirm_delivery_date = timezone.now()
        po.indent.actual_date_of_delivery = timezone.now()
        po.indent.save()
        po.save()
        user_full_name = str(user.full_name)
        PurchaseIndentHistory.objects.create(
            detail=f"{user_full_name} confirmed delivery", indent=po.indent
        )
        return {"message": "Delivery confirmed successfully"}


class NameEnquirySerializerIn(serializers.Serializer):
    bank_code = serializers.CharField()
    account_no = serializers.CharField()

    def create(self, validated_data):
        bank_code = validated_data.get("bank_code")
        account_no = validated_data.get("account_no")

        from core.services import Paystack

        paystack_manager = Paystack()
        response = paystack_manager.name_enquiry(bank_code, account_no)
        return response


class ProcurementReturnSerializerOut(serializers.ModelSerializer):
    return_products = serializers.SerializerMethodField()

    def get_return_products(self, obj):
        result = list()
        for prod in obj.return_products.all():
            data = dict()
            data["id"] = prod.id
            data["name"] = prod.indent_product.product.name
            data["quantity"] = prod.quantity
            data["image"] = prod.image.url
            data["reason"] = prod.return_reason
            result.append(data)
        return result

    class Meta:
        model = ProcurementReturn
        exclude = []


class AcceptDeclineReturnSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    return_id = serializers.UUIDField()
    status = serializers.ChoiceField(choices=PURCHASE_ORDER_STATUS_CHOICES)

    def create(self, validated_data):
        user = validated_data.get("user")
        return_id = validated_data.get("return_id")
        accept_status = validated_data.get("status")

        if accept_status in ["UNDER_REVIEW", "CANCELLED"]:
            raise InvalidRequestException({"message": "Invalid status"})

        returns = ProcurementReturn.objects.filter(
            id=return_id,
            purchase_order__indent__supplier__email__iexact=user.email,
            status="UNDER_REVIEW",
        )
        if not returns.exists():
            raise InvalidRequestException({"message": "Procurement Return not found"})

        procurement_return = returns.first()
        if accept_status == "ACCEPTED":
            total_sum = 0
            for prod in procurement_return.return_products.all():
                total_sum += prod.indent_product.estimated_amount
            # Create Credit note
            ProcurementCreditNote.objects.create(
                procurement_return=procurement_return, total=total_sum
            )
        procurement_return.status = accept_status
        procurement_return.save()
        lower_status = str(accept_status).lower()
        return {
            "message": f"Procurement return {lower_status}",
            "data": ProcurementReturnSerializerOut(
                procurement_return, context={"request": self.context.get("request")}
            ).data,
        }


class ProcurementCreditNoteSerializerOut(serializers.ModelSerializer):
    procurement_return = serializers.SerializerMethodField()

    def get_procurement_return(self, obj):
        return ProcurementReturnSerializerOut(obj.procurement_return).data

    class Meta:
        model = ProcurementCreditNote
        exclude = []


class UpdateSupplierProfileSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    update_type = serializers.ChoiceField(choices=SUPPLIER_UPDATE_CHOICES)
    current_password = serializers.CharField(required=False)
    new_password = serializers.CharField(required=False)
    confirm_password = serializers.CharField(required=False)
    phone_number = serializers.CharField(required=False)
    bank_name = serializers.CharField(required=False)
    bank_code = serializers.CharField(required=False)
    bank_account_name = serializers.CharField(required=False)
    bank_account_number = serializers.IntegerField(required=False)
    contact_person_name = serializers.CharField(required=False)
    contact_person_email = serializers.CharField(required=False)
    contact_person_phone_no = serializers.CharField(required=False)
    contact_person_position = serializers.CharField(required=False)
    categories = serializers.CharField(required=False)
    logo = serializers.ImageField(required=False)
    registration_doc = serializers.FileField(required=False)

    def update(self, instance, validated_data):
        user = validated_data.get("user")
        update_type = validated_data.get("update_type")
        current_password = validated_data.get("current_password")
        new_password = validated_data.get("new_password")
        confirm_password = validated_data.get("confirm_password")
        phone_number = validated_data.get("phone_number")
        contact_person_phone_no = validated_data.get("contact_person_phone_no")
        category = validated_data.get("categories")

        if update_type == "CHANGE_PASSWORD":
            if not all([current_password, new_password, confirm_password]):
                raise InvalidRequestException(
                    {"message": "All password fields are required"}
                )

            if not user.check_password(current_password):
                raise InvalidRequestException({"message": "Incorrect current password"})

            if new_password != confirm_password:
                raise InvalidRequestException({"message": "Passwords mismatch"})

            # Update user password
            user.password = make_password(password=new_password)
            user.save()
            return {"message": "Password changed successfully"}

        if phone_number:
            phone_no = format_phone_number(phone_number)
            instance.phone_number = phone_no
        if contact_person_phone_no:
            personal_phone_no = format_phone_number(contact_person_phone_no)
            instance.contact_person_phone_no = personal_phone_no

        instance.bank_name = validated_data.get("bank_name", instance.bank_name)
        instance.bank_code = validated_data.get("bank_code", instance.bank_code)
        instance.bank_account_name = validated_data.get(
            "bank_account_name", instance.bank_account_name
        )
        instance.bank_account_number = validated_data.get(
            "bank_account_number", instance.bank_account_number
        )
        instance.contact_person_name = validated_data.get(
            "contact_person_name", instance.contact_person_name
        )
        instance.contact_person_email = validated_data.get(
            "contact_person_email", instance.contact_person_email
        )
        instance.contact_person_position = validated_data.get(
            "contact_person_position", instance.contact_person_position
        )
        instance.logo = validated_data.get("logo", instance.logo)
        instance.registration_doc = validated_data.get(
            "registration_doc", instance.registration_doc
        )

        if category:
            from stock_inventory.models import Category as StockCategory

            try:
                category_list = str(category).replace(" ", "").split(",")
                instance.category.clear()
                for cat_id in category_list:
                    instance.category.add(StockCategory.objects.get(id=cat_id))
            except Exception as err:
                print(str(err))
                pass

        instance.save()

        return {
            "message": "Profile updated successfully",
            "data": SupplierSerializerOut(instance).data,
        }


class OrderFulfilmentSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    purchase_order_id = serializers.UUIDField()
    otp = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        purchase_order_id = validated_data.get("purchase_order_id")
        otp = validated_data.get("otp")

        po = ProcurementPurchaseOrder.objects.filter(
            id=purchase_order_id,
            status="ACCEPTED",
            supplier_fulfilled=False,
            indent__supplier__email__iexact=user.email,
        )
        if not po:
            raise InvalidRequestException({"message": "Purchase order not found"})

        purchase_order = po.last()

        indent_approval_status = purchase_order.indent.approval_status
        if indent_approval_status == "APPROVE_WITH_OTP":
            if not otp:
                raise InvalidRequestException({"message": "OTP not provided"})
            # Check OTP Expiry
            if timezone.now() > purchase_order.code_expiry:
                raise InvalidRequestException({"message": "OTP Expired"})

            # Check OTP Validity
            if not check_password(otp, purchase_order.otp_text):
                raise InvalidRequestException({"message": "Invalid OTP"})

        purchase_order.supplier_fulfilled = True
        purchase_order.save()
        return ProcurementPurchaseOrderSerializerOut(purchase_order).data


class RequestProcurementOTPSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    purchase_order_id = serializers.UUIDField()

    def create(self, validated_data):
        user = validated_data.get("user")
        purchase_order_id = validated_data.get("purchase_order_id")

        # Generate random OTP
        otp = get_random_string(length=6, allowed_chars="0123456789")
        hashed_token = make_password(otp)
        data = dict()

        po = ProcurementPurchaseOrder.objects.filter(
            id=purchase_order_id,
            status="ACCEPTED",
            supplier_fulfilled=False,
            indent__approval_status="APPROVE_WITH_OTP",
            indent__supplier__email__iexact=user.email,
        )
        if not po:
            raise InvalidRequestException({"message": "Purchase order not found"})

        purchase_order = po.last()

        if not purchase_order.otp_phone_no:
            raise InvalidRequestException(
                {"message": "Phone number to receive OTP is required"}
            )

        purchase_order.otp_text = hashed_token
        purchase_order.code_expiry = timezone.now() + timezone.timedelta(minutes=15)
        purchase_order.save()

        # Send OTP to phone number
        data["message"] = "OTP sent to the phone number registered for this Procurement"
        if str(settings.ENVIRONMENT) in ["development", "dev", "staging"]:
            data["otp"] = otp

        return data


class AssetImageSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = AssetImage
        exclude = []


class AssetExpenseSerializerOut(serializers.ModelSerializer):
    added_by = serializers.SerializerMethodField()
    last_edited_by = serializers.SerializerMethodField()

    def get_added_by(self, obj):
        if obj.added_by:
            return {
                "id": obj.added_by.id,
                "first_name": obj.added_by.first_name,
                "last_name": obj.added_by.last_name,
                "email": obj.added_by.email,
            }
        return None

    def get_last_edited_by(self, obj):
        if obj.edited_by:
            return {
                "id": obj.edited_by.id,
                "first_name": obj.edited_by.first_name,
                "last_name": obj.edited_by.last_name,
                "email": obj.edited_by.email,
            }
        return None

    class Meta:
        model = AssetExpense
        exclude = ["edited_by"]


class AssetSerializerOut(serializers.ModelSerializer):
    monthly_depreciation = serializers.SerializerMethodField()
    yearly_depreciation = serializers.SerializerMethodField()
    product = serializers.SerializerMethodField()
    team = serializers.SerializerMethodField()
    supplier = serializers.SerializerMethodField()
    images = serializers.SerializerMethodField()
    expenses = serializers.SerializerMethodField()

    def get_expenses(self, obj):
        return AssetExpenseSerializerOut(
            AssetExpense.objects.filter(asset=obj), many=True
        ).data

    def get_images(self, obj):
        if obj.images:
            return AssetImageSerializerOut(obj.images.all(), many=True).data
        return None

    def get_product(self, obj):
        return {
            "id": obj.product.id,
            "name": obj.product.name,
            "category_id": obj.product.category.id,
            "category_name": obj.product.category.name,
        }

    def get_team(self, obj):
        if obj.team:
            return {
                "id": obj.team.id,
                "name": obj.team.team_name,
                "company_id": obj.team.company_id,
                "company_name": obj.team.company.company_name,
            }
        return None

    def get_supplier(self, obj):
        if obj.supplier:
            return {
                "id": obj.supplier_id,
                "name": obj.supplier.name,
                "email": obj.supplier.email,
                "phone": obj.supplier.phone_number,
                "address": obj.supplier.get_full_address(),
            }
        return None

    def get_monthly_depreciation(self, obj):
        if not self.context.get("monthly"):
            return []
        method = obj.depreciation_method
        purchase_date = obj.purchase_date
        purchase_cost = obj.purchase_cost
        residual_value = obj.residual_value
        useful_life = obj.useful_life
        total_estimated_units = obj.total_estimated_units
        rate = obj.depreciation_rate
        produced = obj.units_produced
        capex = obj.capex_amount

        # Depreciable amount (purchase price - residual value)
        depreciable_amount = purchase_cost - residual_value + capex

        # Calculate total months for depreciation
        total_months = useful_life * 12

        # Generate the list of monthly depreciation
        monthly_depreciation = []
        current_date = purchase_date

        if method == "STRAIGHT_LINE":  # Straight Line Depreciation
            new_item_value = purchase_cost
            monthly_value = depreciable_amount / total_months
            for month_index in range(total_months):
                month_name_str = month_name[current_date.month]
                year_str = current_date.year
                new_item_value -= monthly_value
                monthly_depreciation.append(
                    {
                        "month": month_name_str,
                        "year": year_str,
                        "depreciation": round(monthly_value, 2),
                        "item_value": round(new_item_value, 2),
                    }
                )
                # Move to next month
                current_date = (
                    datetime(current_date.year, current_date.month + 1, 1)
                    if current_date.month < 12
                    else datetime(current_date.year + 1, 1, 1)
                )

        elif method == "DOUBLE_DECLINING":  # Declining Balance Depreciation
            balance = purchase_cost
            for month_index in range(total_months):
                year_str = current_date.year
                month_name_str = month_name[current_date.month]
                depreciation = round(balance * float(rate / 12), 2)
                # Reduce balance after each month
                balance -= depreciation
                monthly_depreciation.append(
                    {
                        "month": month_name_str,
                        "year": year_str,
                        "depreciation": depreciation,
                        "item_value": round(balance, 2),
                    }
                )
                # Move to next month
                current_date = (
                    datetime(current_date.year, current_date.month + 1, 1)
                    if current_date.month < 12
                    else datetime(current_date.year + 1, 1, 1)
                )

        elif method == "UNIT_OF_PRODUCTION":  # Unit of Production Depreciation
            # Calculate depreciation per unit
            depreciable_amount_per_unit = depreciable_amount / total_estimated_units
            units_produced_monthly = produced / 12
            new_item_value = purchase_cost

            for month_index in range(total_months):
                month_name_str = month_name[current_date.month]
                year_str = current_date.year
                depreciation = round(
                    depreciable_amount_per_unit * units_produced_monthly, 2
                )
                new_item_value -= depreciation
                monthly_depreciation.append(
                    {
                        "month": month_name_str,
                        "year": year_str,
                        "depreciation": depreciation,
                        "item_value": round(new_item_value, 2),
                    }
                )
                # Move to next month
                current_date = (
                    datetime(current_date.year, current_date.month + 1, 1)
                    if current_date.month < 12
                    else datetime(current_date.year + 1, 1, 1)
                )

        return monthly_depreciation

    def get_yearly_depreciation(self, obj):
        method = obj.depreciation_method
        purchase_date = obj.purchase_date
        purchase_cost = obj.purchase_cost
        residual_value = obj.residual_value
        useful_life = obj.useful_life
        total_estimated_units = obj.total_estimated_units
        rate = obj.depreciation_rate
        produced = obj.units_produced
        capex = obj.capex_amount

        depreciable_amount = purchase_cost - residual_value + capex
        yearly_depreciation = []
        current_year = purchase_date.year
        total_years = useful_life
        end_year = purchase_date + timezone.timedelta(days=365 * (total_years - 1))
        if timezone.now() > end_year:
            obj.depreciated = True
            obj.save()

        if not self.context.get("yearly"):
            return yearly_depreciation

        if method == "STRAIGHT_LINE":  # Straight Line Depreciation
            yearly_value = depreciable_amount / total_years
            new_item_value = purchase_cost
            for year in range(total_years):
                new_item_value -= yearly_value
                yearly_depreciation.append(
                    {
                        "year": current_year + year,
                        "depreciation": round(yearly_value, 2),
                        "item_value": round(new_item_value, 2),
                    }
                )

        elif method == "DOUBLE_DECLINING":  # Declining Balance Depreciation
            balance = purchase_cost
            for year in range(total_years):
                depreciation = round(balance * float(rate), 2)
                balance -= depreciation
                yearly_depreciation.append(
                    {
                        "year": current_year + year,
                        "depreciation": depreciation,
                        "item_value": round(balance, 2),
                    }
                )

        elif method == "UNIT_OF_PRODUCTION":  # Unit of Production Depreciation
            depreciable_amount_per_unit = depreciable_amount / total_estimated_units
            new_item_value = purchase_cost

            for year in range(total_years):
                depreciation = round(depreciable_amount_per_unit * produced, 2)
                new_item_value -= depreciation
                yearly_depreciation.append(
                    {
                        "year": current_year + year,
                        "depreciation": depreciation,
                        "item_value": round(new_item_value, 2),
                    }
                )

        return yearly_depreciation

    class Meta:
        model = Asset
        exclude = ["indent_product"]


class AssetImageUploadSerializerIn(serializers.Serializer):
    image = serializers.ImageField()

    def validate(self, attrs):
        allowed_types = ["image/jpeg", "image/png", "image/gif"]
        max_file_size = 5 * 1024 * 1024

        image = attrs["image"]
        if image.content_type not in allowed_types:
            raise serializers.ValidationError({"image": "Invalid image format"})
        if image.size > max_file_size:
            raise serializers.ValidationError({"image": "Image too large"})
        return attrs


class AssetImageSerializerIn(serializers.Serializer):
    images = serializers.ListField(child=AssetImageUploadSerializerIn())
    team_id = serializers.UUIDField()

    def create(self, validated_data):
        images = validated_data.get("images")
        team_id = validated_data.get("team_id")

        try:
            Team.objects.get(id=team_id)
        except Team.DoesNotExist:
            raise InvalidRequestException({"message": "Selected team not found"})

        image_list = list()

        for img in images:
            image = img.get("image")

            # Create AssetImage
            asset_image = AssetImage.objects.create(team_id=team_id)

            url = upload_file_aws_s3_bucket(
                model_instance_id=asset_image.id, file=image, model_name="AssetImage"
            )

            if url:
                asset_image.image = url
                asset_image.save()
                image_list.append(str(asset_image.id))

        return AssetImageSerializerOut(
            AssetImage.objects.filter(id__in=image_list), many=True
        ).data


class AssetSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    product_id = serializers.UUIDField(required=False)
    team_id = serializers.UUIDField(required=False)
    member_id = serializers.UUIDField(required=False)
    supplier_id = serializers.UUIDField(required=False, allow_null=True)
    description = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )
    asset_no = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    purchase_cost = serializers.FloatField(required=False)
    purchase_date = serializers.DateTimeField(required=False)
    depreciation_method = serializers.ChoiceField(
        choices=ASSET_DEPRECIATION_METHOD_CHOICES, required=False
    )
    asset_category = serializers.ChoiceField(
        choices=ASSET_CATEGORY_CHOICES, required=False
    )
    useful_life = serializers.IntegerField(min_value=1, required=False)
    residual_value = serializers.FloatField(required=False)
    capex_amount = serializers.FloatField(required=False)
    net_income = serializers.FloatField(required=False)
    depreciation_rate = serializers.DecimalField(
        required=False, decimal_places=2, max_digits=10
    )
    total_estimated_units = serializers.IntegerField(required=False)
    units_produced = serializers.IntegerField(required=False)
    warranty_expiry = serializers.DateTimeField(required=False)
    images = serializers.ListField(child=serializers.UUIDField(), required=False)

    def validate(self, attrs):
        useful_life = attrs["useful_life"]
        if useful_life > ConstantTable.get_constant_instance().asset_useful_life:
            raise InvalidRequestException({"message": "Product not found"})

        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        product_id = validated_data.get("product_id")
        team_id = validated_data.get("team_id")
        team_member_id = validated_data.get("member_id")
        asset_category = validated_data.get("asset_category")
        warranty_expiry = validated_data.get("warranty_expiry")
        supplier_id = validated_data.get("supplier_id")
        description = validated_data.get("description")
        asset_no = validated_data.get("asset_no")
        purchase_cost = validated_data.get("purchase_cost")
        purchase_date = validated_data.get("purchase_date")
        depreciation_method = validated_data.get("depreciation_method")
        useful_life = validated_data.get("useful_life")
        residual_value = validated_data.get("residual_value")
        capex_amount = validated_data.get("capex_amount", 0)
        net_income = validated_data.get("net_income", 0)
        depreciation_rate = validated_data.get("depreciation_rate")
        total_estimated_units = validated_data.get("total_estimated_units")
        units_produced = validated_data.get("units_produced")
        images = validated_data.get("images")

        if not all(
            [
                product_id,
                team_id,
                purchase_cost,
                purchase_date,
                depreciation_method,
                useful_life,
                residual_value,
            ]
        ):
            raise InvalidRequestException(
                {
                    "message": "Ensure these fields are selected: Product, Team, Purchase Cost, "
                    "Depreciation Method, Purchase Date, Useful Life and Residual Value"
                }
            )

        try:
            member = TeamMember.objects.get(
                member=user, team__id=team_id, is_active=True
            )
        except TeamMember.DoesNotExist:
            raise InvalidRequestException(
                {
                    "message": "Please confirm that team exists and you're an active member"
                }
            )

        team = member.team

        try:
            supplier = Supplier.objects.get(id=supplier_id, company=team.company)
        except Supplier.DoesNotExist:
            raise InvalidRequestException({"message": "Supplier not found"})

        if not asset_no:
            asset_no = (
                str(team.company.company_name).upper()[:4]
                + "/ASSET/"
                + str(uuid.uuid4()).replace("-", "")[:10]
            )

        products = Product.objects.filter(id=product_id, company_id=team.company_id)
        if not products.exists():
            raise InvalidRequestException({"message": "Product not found"})

        product = products.last()
        team_member = None
        if team_member_id:
            try:
                team_member = TeamMember.objects.get(
                    id=team_member_id, team__id=team_id, is_active=True
                )
            except TeamMember.DoesNotExist:
                raise InvalidRequestException(
                    {"message": "Member not found or inactive"}
                )

        # Create Asset
        asset = Asset.objects.create(
            product=product,
            team=team,
            supplier=supplier,
            description=description,
            asset_no=asset_no,
            purchase_cost=purchase_cost,
            purchase_date=purchase_date,
            depreciation_method=depreciation_method,
            useful_life=useful_life,
            residual_value=residual_value,
            capex_amount=capex_amount,
            depreciation_rate=depreciation_rate,
            total_estimated_units=total_estimated_units,
            net_income=net_income,
            units_produced=units_produced,
            assigned_to=team_member,
            asset_category=asset_category,
            warranty_expiry_date=warranty_expiry,
        )
        if images:
            try:
                for image_id in images:
                    asset.images.add(AssetImage.objects.get(id=image_id, team=team))
            except Exception:
                pass

        return AssetSerializerOut(
            asset,
            context={
                "request": self.context.get("request"),
                "monthly": True,
                "yearly": True,
            },
        ).data

    def update(self, instance, validated_data):
        images = validated_data.get("images")
        instance.description = validated_data.get("description", instance.description)
        instance.asset_no = validated_data.get("asset_no", instance.asset_no)
        instance.purchase_cost = validated_data.get(
            "purchase_cost", instance.purchase_cost
        )
        instance.purchase_date = validated_data.get(
            "purchase_date", instance.purchase_date
        )
        instance.depreciation_method = validated_data.get(
            "depreciation_method", instance.depreciation_method
        )
        instance.useful_life = validated_data.get("useful_life", instance.useful_life)
        instance.residual_value = validated_data.get(
            "residual_value", instance.residual_value
        )
        instance.capex_amount = validated_data.get(
            "capex_amount", instance.capex_amount
        )
        instance.depreciation_rate = validated_data.get(
            "depreciation_rate", instance.depreciation_rate
        )
        instance.total_estimated_units = validated_data.get(
            "total_estimated_units", instance.total_estimated_units
        )
        instance.units_produced = validated_data.get(
            "units_produced", instance.units_produced
        )
        instance.net_income = validated_data.get("net_income", instance.net_income)
        instance.save()

        if images:
            instance.images.clear()
            try:
                for image_id in images:
                    instance.images.add(
                        AssetImage.objects.get(id=image_id, team=instance.team)
                    )
            except Exception:
                pass

        return AssetSerializerOut(
            instance,
            context={
                "request": self.context.get("request"),
                "monthly": True,
                "yearly": True,
            },
        ).data


class SupplierChangePasswordSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    old_password = serializers.CharField()
    new_password = serializers.CharField()

    def create(self, validated_data):
        user = validated_data.get("user")
        old_password = validated_data.get("old_password")
        new_password = validated_data.get("new_password")

        if not user.is_supplier:
            raise InvalidRequestException(
                {"message": "Change password only applicable to suppliers"}
            )

        # Check if new and old passwords are the same
        if old_password == new_password:
            raise InvalidRequestException({"message": "Same passwords cannot be used"})

        if not check_password(password=old_password, encoded=user.password):
            raise InvalidRequestException({"message": "Old password is incorrect"})

        user.password = make_password(password=new_password)
        user.save()

        return "Password changed successfully"


class SupplierResetPasswordSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    otp = serializers.CharField()
    new_password = serializers.CharField()
    confirm_password = serializers.CharField()

    def create(self, validated_data):
        email = validated_data.get("email")
        otp = validated_data.get("otp")
        password = validated_data.get("new_password")
        confirm_password = validated_data.get("confirm_password")

        supplier = is_supplier_check(email)

        if timezone.now() > supplier.code_expiry:
            raise InvalidRequestException(
                {"message": "OTP has expired, Please request for another one"}
            )

        if not check_password(otp, supplier.otp):
            raise InvalidRequestException({"message": "Invalid OTP"})

        try:
            validate_password(password=password)
        except Exception as err:
            raise InvalidRequestException({"message": ", ".join(list(err))})

        if password != confirm_password:
            raise InvalidRequestException({"message": "Passwords does not match"})

        try:
            user = User.objects.get(email__iexact=email, is_supplier=True)
        except User.DoesNotExist:
            raise InvalidRequestException({"message": "User with this email not found"})

        user.password = make_password(password)
        user.save()

        return {"message": "Password reset successful"}


class ResetPasswordOTPSerializerIn(serializers.Serializer):
    email = serializers.EmailField()

    def create(self, validated_data):
        email = validated_data.get("email")

        # Generate random OTP
        otp = get_random_string(length=6, allowed_chars="0123456789")
        hashed_token = make_password(otp)
        data = dict()

        try:
            User.objects.get(user__email=email)
        except User.DoesNotExist:
            raise InvalidRequestException(
                {"message": "User with this email is not found"}
            )

        supplier = is_supplier_check(email)
        supplier.otp = hashed_token
        supplier.code_expiry = timezone.now() + timezone.timedelta(minutes=5)
        supplier.save()

        # Send OTP to phone email
        data["message"] = "OTP sent to provided email"
        if str(settings.ENVIRONMENT) in ["development", "dev", "staging"]:
            data["otp"] = otp

        return data


class AssetExpenseSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    amount = serializers.FloatField()
    asset_id = serializers.UUIDField()
    additional_note = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )
    expense_type = serializers.ChoiceField(choices=ASSET_EXPENSES_CATEGORY)

    def create(self, validated_data):
        user = validated_data.get("user")
        amount = validated_data.get("amount")
        asset_id = validated_data.get("asset_id")
        exp_type = validated_data.get("expense_type")
        expense_note = validated_data.get("additional_note")

        try:
            asset = Asset.objects.get(id=asset_id)
        except Asset.DoesNotExist:
            raise InvalidRequestException({"message": "Asset not found"})

        if not asset.team.members.filter(member=user).exists():
            raise InvalidRequestException({"message": "Permission denied"})

        # Create AssetExpense
        asset_ex = AssetExpense.objects.create(
            asset=asset,
            amount=amount,
            expense_type=exp_type,
            added_by=user,
            note=expense_note,
        )
        # Add amount to capex balance on asset
        asset.capex_amount += amount
        asset.save()
        return AssetExpenseSerializerOut(
            asset_ex, context={"request": self.context.get("request")}
        ).data

    def update(self, instance, validated_data):
        instance.expense_type = validated_data.get(
            "expense_type", instance.expense_type
        )
        instance.note = validated_data.get("additional_note", instance.note)
        amount = validated_data.get("amount")
        old_instance_amount = instance.amount
        asset = instance.asset
        new_balance = asset.capex_amount - old_instance_amount + amount
        instance.amount = amount
        instance.save()
        asset.capex_amount = new_balance
        asset.save()
        return AssetExpenseSerializerOut(
            instance, context={"request": self.context.get("request")}
        ).data