from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from cart_management import models, resources

class CartResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CartResource
    search_fields = [
        "buyer__first_name",
        "session_id",
    ]

    # def get_list_display(self, request):
    #     return [field.first_name for field in self.model._meta.concrete_fields]

class CartItemResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CartItemResource
    search_fields = [
        "cart__id",
        "product__name",
    ]

    # def get_list_display(self, request):
    #     return [field.name for field in self.model._meta.concrete_fields]

class BuyerResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.BuyerResource
    search_fields = [
        "first_name",
        "email",
        "phone_number",
    ]
    list_display = ("id", "first_name", "last_name", "email", "phone_number", "address", "postal_code", "status")

class OrderPipelineAdmin(admin.ModelAdmin):
    search_fields = [
        "name",
        "company__company_name",
        "branch__name",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class OrderStageResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.OrderStageResource
    search_fields = [
        "name",
        "pipeline__name",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class OrderResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.OrderResource
    search_fields = [
        "order_id",
        "buyer__name",
        "status",
        "payment_status",
    ]
    list_display = ("id", "order_id", "amount_paid", "status", "payment_status", "buyer", "order_date", "order_time")

class OrderProductResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.OrderProductResource
    search_fields = [
        "orders__order_id",
        "product_name",
    ]
    list_display = ("id", "orders", "product_name", "quantity")

class IncompleteOrderRecordResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.IncompleteOrderRecordResource
    search_fields = [
        "name", "phone"
    ]
    list_display = ("id", "products", "name", "phone")


@admin.register(models.PaymentRecord)
class PaymentRecordAdmin(ImportExportModelAdmin, admin.ModelAdmin):
    list_display = ('order', 'mode_of_transaction', 'amount', 'transaction_date')
    search_fields = ('order__order_id', 'mode_of_transaction')
    list_filter = ('mode_of_transaction', 'transaction_date')

    
admin.site.register(models.Cart, CartResourceAdmin)
admin.site.register(models.CartItem, CartItemResourceAdmin)
admin.site.register(models.Buyer, BuyerResourceAdmin)
admin.site.register(models.OrderPipeline, OrderPipelineAdmin)
admin.site.register(models.OrderStage, OrderStageResourceAdmin)
admin.site.register(models.Order, OrderResourceAdmin)
admin.site.register(models.OrderProduct, OrderProductResourceAdmin)
admin.site.register(models.IncompleteOrderRecord, IncompleteOrderRecordResourceAdmin)
