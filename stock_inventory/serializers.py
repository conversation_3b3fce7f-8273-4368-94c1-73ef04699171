import datetime

from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from django.db import transaction
from django.db.models import Count, Sum, Avg
from django.db.utils import IntegrityError
from rest_framework import serializers

from account.enums import AccountType
from account.models import AccountSystem, AccountServiceProvider
from core.exceptions import InvalidRequestException
from core.tasks import upload_file_aws_s3_bucket
from helpers.enums import Backorders, Currency
from helpers.reusable_functions import (
    is_valid_string,
    is_valid_uuid,
    validate_phone_number,
)
from requisition.helpers import enums
from requisition.models import Company, Team, TeamMember
from requisition.serializers import (
    AssetImageUploadSerializerIn,
    SupplierDataSerializer,
    SupplierSerializerOut,
)
from requisition.utils import is_supplier_check, onboard_supplier
from sales_app.helper.enums import TransactionStatusChoices
from sales_app.models import (
    ConstantVariable,
    SalesTransaction,
    SalesTransactionItem,
)
from stock_inventory import models, utils
from stock_inventory.helper.enums import (
    PriceVariationMethods,
    RequestStatusChoices,
)
from subscription_and_invoicing.reuseable import SubscriptionService


User = get_user_model()


# Create your serializer(s) here.
class CompanyProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Product
        fields = ["id"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["category"] = instance.category.name
        representation["item"] = instance.name
        product_stock_detail = models.StockDetail.objects.filter(
            company=instance.company, item=instance
        )
        product_total_quantity = product_stock_detail.aggregate(
            total_quantity=Sum("quantity"),
            total_quantity_on_hold=Sum("stock_on_hold"),
        )
        if product_stock_detail.exists():
            product_stock_instance = product_stock_detail.last()
            supplier = product_stock_instance.supplied_by
            representation["date_time"] = product_stock_instance.updated_at or ""
            representation["supplier"] = supplier.name if supplier is not None else ""
            representation["variants"] = product_stock_instance.has_variants
            representation["quantity"] = (
                product_total_quantity.get("total_quantity") or 0
            )
            representation["stock_on_hold"] = (
                product_total_quantity.get("total_quantity_on_hold") or 0
            )
            representation["stock_price"] = product_stock_instance.stock_price
            representation["selling_price"] = product_stock_instance.selling_price
            representation["barcode"] = product_stock_instance.barcode or ""
        else:
            representation["date_time"] = ""
            representation["supplier"] = ""
            representation["variants"] = False
            representation["quantity"] = 0
            representation["stock_on_hold"] = 0
            representation["stock_price"] = 0.0
            representation["selling_price"] = 0.0
            representation["barcode"] = ""
        return representation


class BranchStockDetailSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.StockDetail
        fields = [
            "id",
            "updated_at",
            "company",
            "branch",
            "category",
            "item",
            "quantity",
            "stock_on_hold",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        stock_details = instance
        representation["company"] = stock_details.company.company_name
        representation["branch"] = stock_details.branch.name
        representation["category"] = (
            stock_details.category.name if stock_details.category is not None else ""
        )
        representation["item"] = stock_details.item.name
        representation["supervisor"] = None
        return representation


class BranchesOverviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = [
            "id",
            "company_name",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        company_branches = models.Branch.objects.filter(company=instance)
        company_branch_teams = Team.objects.filter(company=instance, is_active=True)
        company_stock_details = models.StockDetail.objects.filter(
            company=instance,
            branch__in=company_branches,
        ).aggregate(
            total_stock=Sum("quantity"),
            total_quantity_on_hold=Sum("stock_on_hold"),
            total_stock_value=Sum("stock_value"),
        )
        representation["branches"] = company_branches.count()
        representation["teams"] = company_branch_teams.count()
        representation["total_stock"] = company_stock_details.get("total_stock") or 0
        representation["stock_on_hold"] = (
            company_stock_details.get("total_quantity_on_hold") or 0
        )
        representation["stock_value"] = (
            company_stock_details.get("total_stock_value") or 0.00
        )
        subscription_service = SubscriptionService(instance)
        representation = subscription_service.get_subscription_info(representation)
        return representation


class BranchSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Branch
        fields = "__all__"
        read_only_fields = [
            "created_by",
        ]

    def validate(self, attrs):
        if attrs.get("vat") < 0:
            raise serializers.ValidationError(
                {"message": "vat value cannot be less than 0%"}
            )
        if attrs.get("vat") > 100:
            raise serializers.ValidationError(
                {"message": "vat value cannot be greater than 100%"}
            )
        return super().validate(attrs)

    def to_representation(self, instance):
        from sales_app.models import MerchantAccountDetails

        representation = super().to_representation(instance)
        user = self.context.get("user")
        representation["user"] = user.full_name
        representation["company"] = instance.company.company_name
        representation["opening_stock"] = utils.branch_opening_stock(branch=instance)
        representation["closing_stock"] = utils.branch_closing_stock(branch=instance)
        branch_teams = Team.objects.filter(branch=instance, is_active=True)
        branch_stock_details = models.StockDetail.objects.filter(branch=instance)
        branch_stock_value = branch_stock_details.aggregate(
            total_quantity=Sum("quantity"),
            total_quantity_on_hold=Sum("stock_on_hold"),
            total_stock_value=Sum("stock_value"),
        )
        expected_profit = sum([stock.expected_profit for stock in branch_stock_details])
        branch_members = (
            TeamMember.objects.filter(
                team__in=branch_teams,
                is_active=True,
            )
            .order_by("member__id")
            .distinct("member")
        )
        members = [
            {
                "name": member.member.full_name if member.member is not None else "",
                "email": member.email if member is not None else "",
                "role": member.role if member is not None else "",
            }
            for member in branch_members
        ]
        total_sales_transaction = (
            SalesTransaction.objects.filter(
                company=instance.company,
                branch=instance,
                status=TransactionStatusChoices.COMPLETED,
            ).aggregate(amount=Sum("total_sales_amount"))["amount"]
            or 0.0
        )
        representation["quantity"] = branch_stock_value.get("total_quantity") or 0
        representation["stock_on_hold"] = (
            branch_stock_value.get("total_quantity_on_hold") or 0
        )
        representation["stock_value"] = (
            branch_stock_value.get("total_stock_value") or 0.0
        )
        representation["sales_value"] = total_sales_transaction
        representation["expected_profit"] = expected_profit
        representation["members"] = members
        branch_account = AccountServiceProvider.objects.filter(branch=instance).last()
        if branch_account is not None:
            account_details = {
                "account_name": branch_account.account_name,
                "account_number": branch_account.account_number,
                "bank_name": branch_account.account_provider,
            }
        else:
            branch_account = AccountSystem.objects.filter(
                branch=instance,
                account_type=AccountType.SALES,
            ).last()
            if branch_account is not None:
                account_details = {
                    "account_name": branch_account.account_name,
                    "account_number": branch_account.account_number,
                    "bank_name": branch_account.account_provider,
                }
            else:
                account_details = {
                    "account_name": "",
                    "account_number": "",
                    "bank_name": "",
                }
        representation["account_details"] = account_details
        constant_charges = ConstantVariable.objects.first()
        if constant_charges is not None:
            representation["charges"] = {
                "sales_charge": constant_charges.sales_charge,
                "sales_charge_cap": constant_charges.sales_charge_cap,
                "sales_withdrawal_charge": constant_charges.sales_withdrawal_charge,
            }
        else:
            representation["charges"] = {
                "sales_charge": 0.0,
                "sales_charge_cap": 0.0,
                "sales_withdrawal_charge": 0.0,
            }
        return representation


class BranchUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Branch
        fields = [
            "name",
            "address",
            "vat",
            "is_super_branch",
            "updated_by",
        ]
        read_only_fields = ["company"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["updated_by"] = instance.updated_by.full_name
        return representation

    def validate(self, attrs):
        if len(attrs) <= 1:
            raise serializers.ValidationError(
                {"errors": "no field(s) was passed to be updated."}
            )
        return super().validate(attrs)

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
            try:
                instance.save()
            except IntegrityError as error:
                raise serializers.ValidationError(
                    {"message": "duplicate company branch name."}
                )
        return super().update(instance, validated_data)


class BranchDeleteSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())


class CompanyBranchesStockCountSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.Branch
        fields = ["id"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        branch_stock_details = models.StockDetail.objects.filter(
            branch=instance
        ).aggregate(
            category_count=Count("category", distinct=True),
            item_count=Count("item", distinct=True),
            total_quantity=Sum("quantity"),
            total_quantity_on_hold=Sum("stock_on_hold"),
        )
        branch_teams = Team.objects.filter(branch=instance, is_active=True)
        branch_supervisors = (
            TeamMember.objects.filter(
                team__in=branch_teams,
                role=enums.UserRole.SUPERVISOR,
                is_active=True,
            )
            .order_by("member__id")
            .distinct("member")
        )
        representation["categories_count"] = (
            branch_stock_details.get("category_count") or 0
        )
        representation["products_count"] = branch_stock_details.get("item_count") or 0
        representation["quantity"] = branch_stock_details.get("total_quantity") or 0
        representation["stock_on_hold"] = (
            branch_stock_details.get("total_quantity_on_hold") or 0
        )
        representation["supervisors"] = branch_supervisors.count()
        representation["branch"] = instance.name
        return representation


class CategorySerializer(serializers.ModelSerializer):

    class Meta:
        model = models.Category
        exclude = ["updated_at"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company_name"] = (
            instance.company.company_name if instance.company is not None else ""
        )
        representation["created_by"] = (
            instance.created_by.full_name if instance.created_by is not None else ""
        )
        representation["updated_by"] = (
            instance.updated_by.full_name if instance.updated_by is not None else ""
        )
        representation["subcategories"] = models.SubCategory.objects.filter(
            category=instance
        ).count()
        representation["products"] = models.Product.objects.filter(
            category=instance,
            is_active=True,
        ).count()
        # Category stock count is being used by Instant Web.
        category_stock_details = models.StockDetail.objects.filter(
            company=self.context.get("company"),
            category=instance,
            item__is_active=True,
        )
        representation["stock_count"] = category_stock_details.count()
        return representation


class SubcategorySerializer(serializers.ModelSerializer):

    class Meta:
        model = models.SubCategory
        exclude = ["updated_at"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["category_name"] = (
            instance.category.name if instance.category is not None else ""
        )
        representation["created_by"] = (
            instance.created_by.full_name if instance.created_by is not None else ""
        )
        return representation


class CategoryCreateSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    categories = CategorySerializer(many=True)


class CategoryDeleteSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    categories = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(
            queryset=models.Category.objects.all()
        ),
    )

    def validate(self, attrs):
        if attrs["categories"] == []:
            raise serializers.ValidationError({"errors": "categories cannot be empty."})
        return super().validate(attrs)


class SubCategoryDeleteSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    subcategories = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(
            queryset=models.SubCategory.objects.all()
        ),
    )

    def validate(self, attrs):
        if attrs["subcategories"] == []:
            raise serializers.ValidationError(
                {"errors": "subcategories cannot be empty."}
            )
        return super().validate(attrs)


class NewProductUploadSerializer(serializers.Serializer):
    image = serializers.URLField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    item = serializers.CharField(max_length=255)
    quantity = serializers.IntegerField(default=0, required=False)
    stock_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0,
        required=False,
    )
    selling_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0,
        required=False,
    )
    sku = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )

    def validate(self, attrs):
        if "sku" in attrs and not is_valid_string(attrs.get("sku")):
            attrs.pop("sku")
        if attrs.get("quantity") is not None and attrs.get("quantity") >= 1:
            if attrs.get("stock_price") is not None and attrs.get("stock_price") < 1:
                raise serializers.ValidationError({"errors": "invalid stock price."})
            if (
                attrs.get("selling_price") is not None
                and attrs.get("selling_price") < 1
            ):
                raise serializers.ValidationError({"errors": "invalid selling price."})
            attrs["has_stock"] = True
        return super().validate(attrs)


class ProductSerializer(serializers.ModelSerializer):
    category = serializers.PrimaryKeyRelatedField(
        queryset=models.Category.objects.all()
    )
    name = serializers.ListField(required=False)
    stocks = NewProductUploadSerializer(many=True, required=False)
    selected_branches = serializers.PrimaryKeyRelatedField(
        queryset=models.Branch.objects.all(),
        many=True,
        required=False,
    )

    class Meta:
        model = models.Product
        fields = "__all__"
        read_only_fields = [
            "created_by",
            "company",
            "name",
        ]


class ProductCreateSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    products = ProductSerializer(many=True)

    def validate(self, attrs):
        if attrs.get("products") == []:
            raise serializers.ValidationError({"errors": "products cannot be empty."})
        return super().validate(attrs)


class ProductDeleteSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    items = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=models.Product.objects.all())
    )

    def validate(self, attrs):
        if attrs.get("items") == []:
            raise serializers.ValidationError({"errors": "items cannot be empty."})
        return super().validate(attrs)


class ProductDetailsSerializer(serializers.ModelSerializer):
    company = serializers.CharField(source="company.company_name")
    category = serializers.CharField(source="category.name")
    category_id = serializers.CharField(source="category.id")

    class Meta:
        model = models.Product
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["subcategory_name"] = (
            instance.subcategory.name if instance.subcategory is not None else ""
        )
        representation["quantity"] = (
            models.StockDetail.objects.filter(
                company=instance.company,
                category=instance.category,
                item=instance,
            ).aggregate(total_quantity=Sum("quantity"))["total_quantity"]
            or 0
        )
        representation["branches_count"] = instance.selected_branches.count()
        representation["selected_branches"] = [
            branch.name for branch in instance.selected_branches.all()
        ]
        return representation


class SupplierSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Supplier
        fields = "__all__"
        read_only_fields = [
            "created_by",
            "updated_by",
        ]

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if attrs.get("email") and is_valid_string(attrs.get("email")):
            supplier = models.Supplier.objects.filter(email=attrs.get("email").lower())
            if supplier.exists():
                raise serializers.ValidationError(
                    {"message": "supplier exists already."}
                )
        if attrs.get("phone_number") and is_valid_string(attrs.get("phone_number")):
            validate_phone_number(attrs.get("phone_number"))
            supplier = models.Supplier.objects.filter(
                phone_number=attrs.get("phone_number")
            )
            if supplier.exists():
                raise serializers.ValidationError(
                    {"message": "supplier exists already."}
                )
        return super().validate(attrs)


class SupplierUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Supplier
        fields = [
            "name",
            "email",
            "phone_number",
            "address",
            "bank_name",
            "bank_account_name",
            "bank_account_number",
            "updated_by",
        ]
        read_only_fields = [
            "company",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["updated_by"] = instance.updated_by.full_name
        return representation

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if len(attrs) <= 1:
            raise serializers.ValidationError(
                {
                    "message": "At least one of the following fields must be passed to be updated: name, email, phone_number, address, bank_name, bank_account_name, bank_account_number."
                }
            )
        if "phone_number" in attrs and is_valid_string(attrs.get("phone_number")):
            validate_phone_number(attrs.get("phone_number"))
        return super().validate(attrs)


class ItemVariantUploadSerializer(serializers.Serializer):
    description = serializers.CharField()
    quantity = serializers.IntegerField()
    stock_alert = serializers.IntegerField(default=0, required=False)
    stock_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.00)],
        required=False,
    )
    selling_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.00)],
        required=False,
    )


class ItemUploadSerializer(serializers.Serializer):
    category = serializers.PrimaryKeyRelatedField(
        queryset=models.Category.objects.all()
    )
    supplied_by = serializers.CharField(
        allow_null=True,
        allow_blank=True,
        required=False,
    )
    item = serializers.PrimaryKeyRelatedField(queryset=models.Product.objects.all())
    quantity = serializers.IntegerField()
    expiry_date = serializers.DateField(default=None, required=False)
    stock_alert = serializers.IntegerField(default=0, required=False)
    stock_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        required=False,
    )
    selling_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        required=False,
    )
    has_variants = serializers.BooleanField(default=False, required=False)
    has_unique_ids = serializers.BooleanField(default=False, required=False)
    variants = ItemVariantUploadSerializer(many=True, required=False)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        supplier_id = attrs.get("supplied_by")
        if supplier_id and is_valid_string(supplier_id):
            if not is_valid_uuid(supplier_id):
                raise serializers.ValidationError(
                    {"message": "provide a valid supplier ID"}
                )
            supplied_by = models.Supplier.objects.filter(id=supplier_id).first()
            if supplied_by is not None:
                attrs["supplied_by"] = supplied_by
            else:
                raise serializers.ValidationError({"message": "supplier not found."})
        else:
            attrs["supplied_by"] = None
        quantity = attrs.get("quantity")
        variants = attrs.get("variants")
        if variants:
            total_variants = sum([variant.get("quantity") for variant in variants])
            if total_variants > quantity:
                raise serializers.ValidationError(
                    {
                        "message": "the total quantity of Variants cannot be greater than the total quantity of items."
                    }
                )
        return super().validate(attrs)


class StockManualUploadSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    stocks = ItemUploadSerializer(many=True)
    comment = serializers.CharField(
        max_length=2500,
        allow_null=True,
        allow_blank=True,
        required=False,
    )

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if attrs.get("stocks") == []:
            raise serializers.ValidationError({"message": "stocks cannot be empty."})
        return super().validate(attrs)


class SupplierHistorySummarySerializer(serializers.ModelSerializer):

    class Meta:
        model = models.SupplierHistory
        fields = [
            "id",
            "created_at",
            "supplier",
            "quantity",
            "returned_items",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        supplier = instance.supplier
        representation["supplier_name"] = supplier.name
        representation["supplier_email"] = supplier.email
        representation["supplier_phone_number"] = supplier.phone_number
        return representation


class VariantDetailSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.StockVariant
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = instance.branch.name
        representation["stock_item"] = instance.stock_item.item.name
        representation["uploaded_by"] = instance.uploaded_by.first_name
        return representation


class StockDetailSerializer(serializers.ModelSerializer):
    variants = VariantDetailSerializer(read_only=True, many=True)

    class Meta:
        model = models.StockDetail
        fields = "__all__"


class SupplierHistoryDetailsSerializer(serializers.ModelSerializer):
    variants = VariantDetailSerializer(read_only=True, many=True)

    class Meta:
        model = models.SupplierHistory
        fields = [
            "id",
            "created_at",
            "category",
            "subcategory",
            "item",
            "quantity",
            "variants",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["category"] = instance.category.name
        representation["item"] = instance.item.name
        return representation


class StockUniqueIdSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.StockUniqueId
        exclude = ["updated_at", "is_active", "is_deleted"]
        read_only_fields = ["uploaded_by", "unique_id"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = instance.branch.name
        representation["uploaded_by"] = instance.uploaded_by.first_name
        representation["stock_item"] = (
            instance.stock_item.item.name if instance.stock_item is not None else None
        )
        representation["stock_variant"] = (
            instance.stock_variant.description
            if instance.stock_variant is not None
            else None
        )

        return representation


class StockUniqueIdCreateSerializer(StockUniqueIdSerializer):
    file = serializers.FileField()

    def validate(self, attrs):
        file = attrs.get("file").name
        ext = file.split(".")[-1]
        if ext != "xlsx":
            raise serializers.ValidationError({"message": "file format not supported."})
        return super().validate(attrs)


class PriceListSerializer(serializers.ModelSerializer):
    selected_branches = serializers.PrimaryKeyRelatedField(
        queryset=models.Branch.objects.all(),
        many=True,
        required=False,
    )

    class Meta:
        model = models.PriceList
        fields = "__all__"
        read_only_fields = ["created_by", "company"]

    def validate(self, attrs):
        if not attrs.get("all_branches") and not attrs.get("selected_branches"):
            raise serializers.ValidationError(
                {
                    "errors": "price list creation must include either to 'all branches' or 'selected branches'."
                }
            )
        return super().validate(attrs)


class PriceListCreateSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    price_lists = PriceListSerializer(many=True)


class PriceListDetailsSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.PriceList
        fields = "__all__"
        read_only_fields = [
            "created_by",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        company = instance.company
        branch = self.context.get("branch")
        representation["company"] = company.company_name
        representation["category"] = instance.category.name
        representation["item"] = instance.item.name
        if branch is not None:
            representation["selected_branches"] = [branch.name]
        else:
            selected_branches = instance.selected_branches.all()
            representation["selected_branches"] = [
                branch.name for branch in selected_branches
            ]
        return representation


class PriceListUpdateSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    price_list_id = serializers.PrimaryKeyRelatedField(
        queryset=models.PriceList.objects.all()
    )
    size_or_volume = serializers.CharField(required=False)
    price = serializers.FloatField(required=False)
    selected_branches = serializers.PrimaryKeyRelatedField(
        queryset=models.Branch.objects.all(),
        many=True,
        required=False,
    )

    def validate(self, attrs):
        if len(attrs) < 3:
            raise serializers.ValidationError(
                {"message": "no field(s) was passed to be updated."}
            )
        if not models.PriceList.objects.filter(
            id=attrs.get("price_list_id").id,
            company=attrs.get("company"),
        ).exists():
            raise serializers.ValidationError(
                {"message": "provide a valid price list ID."}
            )
        attrs["price_list"] = attrs.get("price_list_id")
        attrs.pop("price_list_id")
        return super().validate(attrs)


class StockOutSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.StockOut
        exclude = ["updated_at", "quantity_approved"]
        read_only_fields = ["requested_by", "approved_by"]

    def to_representation(self, instance):
        # Add available quantity and cost price to all request detail(s).
        representation = super().to_representation(instance)
        company = instance.company
        branch = instance.branch
        category = instance.category
        product = instance.item

        representation["company"] = company.company_name
        representation["branch"] = branch.name
        representation["requested_by"] = instance.requested_by.full_name
        representation["category"] = category.name
        representation["item"] = product.name
        representation["approved_by"] = (
            instance.approved_by.full_name if instance.approved_by is not None else None
        )

        product_stock_detail = models.StockDetail.retrieve_branch_item_stock(
            branch=branch, item=product
        )
        if product_stock_detail is None:
            representation["available_quantity"] = 0
            representation["stock_price"] = 0.00
        else:
            representation["available_quantity"] = product_stock_detail.quantity
            representation["cost_price"] = product_stock_detail.stock_price
        return representation


class StockOutActionSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    request_id = serializers.UUIDField()
    status = serializers.ChoiceField(choices=RequestStatusChoices.choices)
    decline_reason = serializers.CharField(required=False)


class StockFileUploadSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    stock_file = serializers.FileField()

    def validate(self, attrs):
        attrs = super().validate(attrs)
        file = attrs.get("stock_file").name
        ext = file.split(".")[-1]
        if ext != "xlsx":
            raise serializers.ValidationError({"message": "file format not supported."})
        return super().validate(attrs)


class BranchProductSerializer(serializers.ModelSerializer):
    company = serializers.CharField(source="company.company_name")
    category_id = serializers.CharField(source="category.id")
    category = serializers.CharField(source="category.name")
    product = serializers.CharField(source="name")
    variants = serializers.CharField(source="product_description")

    class Meta:
        model = models.Product
        fields = [
            "company",
            "category_id",
            "category",
            "product",
            "id",
            "variants",
            "product_image_1",
            "subcategory",
            "sku",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        branch = self.context.get("branch")
        representation["subcategory_name"] = (
            instance.subcategory.name if instance.subcategory is not None else ""
        )
        representation["branch"] = branch.name
        stock_detail = models.StockDetail.objects.filter(
            company=instance.company,
            branch=branch,
            category=instance.category,
            item=instance,
        ).last()
        if stock_detail is not None:
            supplier = stock_detail.supplied_by
            representation["updated_at"] = stock_detail.updated_at
            representation["stock_item_id"] = stock_detail.id
            representation["quantity"] = stock_detail.quantity
            representation["stock_price"] = stock_detail.stock_price
            representation["selling_price"] = stock_detail.selling_price
            representation["unique_ids"] = stock_detail.has_unique_ids
            representation["barcode"] = stock_detail.barcode
            representation["supplier"] = supplier.name if supplier is not None else ""
        else:
            representation["updated_at"] = instance.updated_at
            representation["stock_item_id"] = ""
            representation["quantity"] = 0
            representation["stock_price"] = instance.product_price
            representation["selling_price"] = instance.selling_price
            representation["unique_ids"] = False
            representation["barcode"] = ""
            representation["supplier"] = ""
        return representation


class StockRequestSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.StockRequest
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        branch = self.context.get("branch")
        representation["category_id"] = instance.category.id
        representation["category"] = instance.category.name
        representation["item"] = instance.item.name

        stock_details = models.StockDetail.objects.filter(
            branch=branch, category=instance.category, item=instance.item
        ).first()
        if stock_details is not None:
            representation["available_stock"] = stock_details.quantity
            representation["stock_item"] = stock_details.id
            representation["stock_item_has_ids"] = stock_details.has_unique_ids
        else:
            representation["available_stock"] = 0
            representation["stock_item"] = None
            representation["stock_item_has_ids"] = None
        return representation


class StockRequestItemSerializer(serializers.Serializer):
    item = serializers.PrimaryKeyRelatedField(queryset=models.Product.objects.all())
    quantity = serializers.IntegerField()

    def validate(self, attrs):
        if attrs.get("quantity") < 0:
            raise serializers.ValidationError({"error: negative value not accepted."})
        return super().validate(attrs)


class StockRequestCategorySerializer(serializers.Serializer):
    category = serializers.PrimaryKeyRelatedField(
        queryset=models.Category.objects.all()
    )
    items = StockRequestItemSerializer(many=True)

    def validate(self, attrs):
        if attrs.get("items") == []:
            raise serializers.ValidationError({"errors": "items can not be empty."})
        return super().validate(attrs)


class StockRequestCreateSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    supply_branch = serializers.PrimaryKeyRelatedField(
        queryset=models.Branch.objects.all()
    )
    demand_branch = serializers.PrimaryKeyRelatedField(
        queryset=models.Branch.objects.all()
    )
    requests = StockRequestCategorySerializer(many=True)
    request_reason = serializers.CharField(required=False)

    def validate(self, attrs):
        if attrs.get("requests") == []:
            raise serializers.ValidationError({"errors": "requests can not be empty."})
        if attrs.get("demand_branch") == attrs.get("supply_branch"):
            raise serializers.ValidationError(
                {"errors": "same values for demand and supply branches."}
            )
        return attrs


class StockRequestSummarySerializer(serializers.Serializer):
    date = serializers.DateTimeField()
    requested_by = serializers.CharField()
    category = serializers.IntegerField()
    item = serializers.IntegerField()
    request_id = serializers.CharField()
    quantity = serializers.IntegerField()
    request_time = serializers.DateTimeField()
    request_reason = serializers.CharField()
    demand_branch = serializers.CharField()
    supply_branch = serializers.CharField()
    status = serializers.CharField()
    approved_by = serializers.CharField()
    date_approved = serializers.CharField()

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        branch = self.context.get("branch")
        if branch.name == instance.get("demand_branch"):
            representation["type"] = "INCOMING"
            representation["branch"] = instance.get("supply_branch")
        if branch.name == instance.get("supply_branch"):
            representation["type"] = "OUTGOING"
            representation["branch"] = instance.get("demand_branch")
        representation.pop("demand_branch")
        representation.pop("supply_branch")
        return representation


class StockRequestActionItemSerializer(serializers.Serializer):
    id = serializers.PrimaryKeyRelatedField(queryset=models.StockRequest.objects.all())
    quantity_approved = serializers.IntegerField()
    unique_ids = serializers.PrimaryKeyRelatedField(
        queryset=models.StockUniqueId.objects.all(),
        many=True,
        required=False,
    )


class StockRequestActionHandlerSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    request_id = serializers.CharField()
    status = serializers.ChoiceField(choices=RequestStatusChoices.choices)
    items = StockRequestActionItemSerializer(many=True, required=False)
    decline_reason = serializers.CharField(required=False)

    def validate(self, attrs):
        if attrs.get("status") not in ["APPROVED", "DECLINED"]:
            raise serializers.ValidationError(
                {"errors": "supported action includes 'APPROVED' or 'DECLINED'."}
            )
        if attrs.get("items") is None and attrs.get("decline_reason") is None:
            raise serializers.ValidationError(
                {
                    "errors": "stock request action must include either 'items' or 'decline reason'."
                }
            )
        if attrs.get("items") and attrs.get("items") == []:
            raise serializers.ValidationError({"errors": "items can not be empty."})
        return super().validate(attrs)


class InventoryStatusSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.InventoryStatus
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["item"] = instance.item.name
        representation["branch"] = instance.branch.name
        representation["company"] = instance.company.company_name
        return representation


class BranchInventoryStatusSummarySerializer(serializers.Serializer):
    date = serializers.DateField()
    opening_stock = serializers.IntegerField()
    opening_value = serializers.DecimalField(max_digits=13, decimal_places=2)
    closing_stock = serializers.IntegerField()
    closing_value = serializers.DecimalField(max_digits=13, decimal_places=2)
    branch = serializers.CharField()
    company = serializers.CharField()


class BranchStockHistorySerializer(serializers.ModelSerializer):

    class Meta:
        model = models.StockHistory
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = instance.branch.name
        representation["supplier"] = (
            instance.supplier.name if instance.supplier is not None else ""
        )
        representation["category"] = (
            instance.category.name if instance.category is not None else ""
        )
        representation["item"] = instance.item.name
        representation["type"] = instance.transaction_type
        return representation


class BranchStockRequestTransitSerializer(serializers.ModelSerializer):
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())

    class Meta:
        model = models.StockRequest
        fields = [
            "company",
            "branch",
            "request_id",
            "status",
            "image",
            "transit_date",
            "transit_by",
        ]
        extra_kwargs = {"status": {"required": True}, "image": {"required": True}}
        read_only_fields = ["transit_date", "transit_by"]

    def validate(self, attrs):
        if attrs.get("status") != "TRANSIT":
            raise serializers.ValidationError(
                {"errors": "expected 'status' is 'TRANSIT'."}
            )
        return super().validate(attrs)


class BranchStockRequestReceiveSerializer(serializers.ModelSerializer):
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())

    class Meta:
        model = models.StockRequest
        fields = [
            "company",
            "branch",
            "request_id",
            "status",
            "received_date",
            "received_by",
        ]
        extra_kwargs = {"status": {"required": True}}
        read_only_fields = ["transit_date", "transit_by"]

    def validate(self, attrs):
        if attrs.get("status") != "DELIVERED":
            raise serializers.ValidationError(
                {"errors": "expected 'status' is 'DELIVERED'."}
            )
        return super().validate(attrs)


class PurchaseOrderSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.PurchaseOrder
        fields = "__all__"
        read_only_fields = [
            "requested_by",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = instance.branch.name
        representation["requested_by"] = instance.requested_by.full_name
        representation["category"] = instance.category.name
        representation["item"] = instance.item.name
        representation["supplier"] = instance.supplier.name
        return representation


class CategoryProductSerializer(serializers.Serializer):
    category = serializers.PrimaryKeyRelatedField(
        queryset=models.Category.objects.all()
    )
    item = serializers.PrimaryKeyRelatedField(queryset=models.Product.objects.all())
    quantity = serializers.IntegerField()


class PurchaseOrderCreateSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    supplier = serializers.PrimaryKeyRelatedField(
        queryset=models.Supplier.objects.all()
    )
    orders = CategoryProductSerializer(many=True)

    def validate(self, attrs):
        # ensure that purchase order contains unique product(s).
        purchase_order = set()
        orders = attrs.get("orders")
        for order in orders:
            pair = (order["category"], order["item"])
            if pair in purchase_order:
                raise serializers.ValidationError(
                    "duplicate product entry for single request."
                )
            purchase_order.add(pair)
        return super().validate(attrs)


class PurchaseOrderSummarySerializer(serializers.Serializer):
    branch = serializers.CharField()
    request_id = serializers.CharField()
    date_issued = serializers.DateTimeField()
    supplier = serializers.CharField()
    category = serializers.IntegerField()
    item = serializers.IntegerField()
    quantity = serializers.IntegerField()
    status = serializers.CharField()
    date_delivered = serializers.DateTimeField()


class StockTransferSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.StockTransfer
        fields = "__all__"
        read_only_fields = ["approved_by"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = instance.branch.name
        representation["transfer_to"] = instance.transfer_to.name
        representation["approved_by"] = instance.approved_by.full_name
        representation["category"] = instance.category.name
        representation["item"] = instance.item.name
        representation["variant"] = (
            instance.variant.description if instance.variant is not None else None
        )
        return representation


class StockTransferActionSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.StockTransfer
        fields = [
            "company",
            "branch",
            "request_id",
            "image",
        ]
        extra_kwargs = {
            "image": {"required": True},
        }


class TransferVariantSerializer(CategoryProductSerializer):
    variant = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    stock_price = serializers.DecimalField(
        max_digits=13, decimal_places=2, validators=[MinValueValidator(0.00)]
    )
    selling_price = serializers.DecimalField(
        max_digits=13, decimal_places=2, validators=[MinValueValidator(0.00)]
    )

    def validate(self, attrs):
        if "variant" in attrs and not is_valid_string(attrs.get("variant")):
            attrs.pop("variant")
        if "variant" in attrs and is_valid_string(attrs.get("variant")):
            if not is_valid_uuid(attrs.get("variant")):
                raise serializers.ValidationError(
                    {"error": "provide a valid variant ID."}
                )
            else:
                variant = models.StockVariant.objects.filter(
                    id=attrs.get("variant")
                ).last()
                if variant is None:
                    raise serializers.ValidationError(
                        {"error": "provide a valid variant ID."}
                    )
        return attrs


class StockTransferCreateSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    transfer_to = serializers.PrimaryKeyRelatedField(
        queryset=models.Branch.objects.all()
    )
    transfers = TransferVariantSerializer(many=True)

    def validate(self, attrs):
        if not attrs["branch"].is_super_branch:
            raise serializers.ValidationError(
                {"error": "action not permitted, the branch is not a super branch."}
            )
        return super().validate(attrs)


class StockTransferSummarySerializer(serializers.Serializer):
    date = serializers.DateTimeField()
    request_id = serializers.CharField()
    approved_by = serializers.CharField()
    branch = serializers.CharField()
    category = serializers.IntegerField()
    item = serializers.IntegerField()
    quantity = serializers.IntegerField()
    status = serializers.CharField()


class AvailableStockSerializer(serializers.Serializer):
    category = serializers.CharField(source="category.name")
    category_id = serializers.CharField(source="category.id")
    item = serializers.CharField(source="item.name")
    item_id = serializers.CharField(source="item.id")
    description = serializers.CharField(source="item.product_description")
    image = serializers.URLField(source="item.product_image_1")
    product_vat = serializers.FloatField(source="item.vat")
    ranking = serializers.IntegerField(source="item.sales_ranking")

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.item.subcategory is not None:
            representation["subcategory"] = instance.item.subcategory.name
            representation["subcategory_id"] = instance.item.subcategory.id
        else:
            representation["subcategory"] = ""
            representation["subcategory_id"] = ""

        if instance.item.sku is not None:
            representation["sku"] = instance.item.sku
        else:
            representation["sku"] = ""

        ###########################     MANAGE PRICE VARIATION    ##############################

        price_tag = self.context.get("price_tag")
        if price_tag != None and not isinstance(price_tag, str):
            price_variation = models.PriceVariation.objects.filter(
                company=self.context.get("company"),
                item=instance.item_id,
                price_tag=price_tag,
            ).first()
            if price_variation is not None:
                if price_variation.price > 0.00:
                    representation["price_tag"] = price_tag.name
                    representation["selling_price"] = price_variation.price
                else:
                    representation["price_tag"] = "selling_price"
                    representation["selling_price"] = instance.price
        else:
            representation["price_tag"] = "selling_price"
            representation["selling_price"] = instance.price

        ###########################     MANAGE PRICE VARIATION    ##############################

        branch_stock_item = models.StockDetail.objects.filter(
            branch=self.context.get("branch"),
            category=instance.category_id,
            item=instance.item_id,
        ).first()
        if branch_stock_item is not None:
            representation["stock_price"] = branch_stock_item.stock_price
            representation["quantity"] = branch_stock_item.quantity
        else:
            representation["stock_price"] = 0.0
            representation["quantity"] = 0
        branch_quantity_sold = (
            SalesTransactionItem.objects.filter(
                branch=self.context.get("branch"),
                sales_transaction__status="SUCCESSFUL",
                category=instance.category_id,
                item=instance.item_id,
            ).aggregate(quantity_sold=Sum("quantity"))["quantity_sold"]
            or 0
        )
        representation["quantity_sold"] = branch_quantity_sold
        representation["is_bnpl"] = False
        representation["supplier_detail"] = dict()
        representation["similar_products"] = list()
        return representation


class ChatStockUploadSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    category = serializers.CharField(max_length=255)
    product = serializers.CharField(max_length=255)
    quantity = serializers.IntegerField()
    cost_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.00)],
    )
    selling_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.00)],
    )
    image = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )


class CreateStoreSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    description = serializers.CharField(required=True)
    store_url = serializers.CharField(required=True)
    brand_color = serializers.CharField(required=True)
    store_logo = serializers.FileField(required=True)

    def validate(self, attrs):
        store_url = attrs.get("store_url").lower()
        valid_subdomain = validate_subdomain(store_url)
        if not valid_subdomain:
            raise serializers.ValidationError({"message": f"{store_url} is not valid"})
        return super().validate(attrs)


class ViewCompanyStoreSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.CompanyStore
        fields = [
            "contact_visible",
            "contact_phone_number",
            "contact_phone_number",
            "contact_email",
            "contact_address",
            "navigation_visible",
            "navigation_set_menu",
            "navigation_alignment",
            "header_alignment",
            "header_image",
            "header_logo",
            "header_logo_text",
            "store_brand_color",
            "redirect_phone_number",
            "order_completion_message",
            "redirect_after_payment_url",
            "success_message_url",
            "notification_url",
            "store_description",
            "store_url",
        ]


class EditStoreSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.CompanyStore
        fields = [
            "contact_visible",
            "contact_phone_number",
            "contact_phone_number",
            "contact_email",
            "contact_address",
            "navigation_visible",
            "navigation_set_menu",
            "navigation_alignment",
            "header_alignment",
            "header_logo_text",
            "store_brand_color",
            "redirect_phone_number",
            "order_completion_message",
            "redirect_after_payment_url",
            "success_message_url",
            "notification_url",
        ]


class EditStoreLogoSerializer(serializers.Serializer):
    header_logo = serializers.FileField(required=True, allow_null=True)


class EditStoreHeaderImageSerializer(serializers.Serializer):
    header_image = serializers.FileField(required=True, allow_null=True)


class ProductVariantSerializer(serializers.Serializer):
    description = serializers.CharField()
    product_value = serializers.CharField()
    quantity = serializers.IntegerField()
    variant_price = serializers.DecimalField(
        max_digits=13, decimal_places=2, validators=[MinValueValidator(0.00)]
    )
    selling_price = serializers.DecimalField(
        max_digits=13, decimal_places=2, validators=[MinValueValidator(0.00)]
    )
    variant_discount = serializers.DecimalField(
        max_digits=13, decimal_places=2, validators=[MinValueValidator(0.00)]
    )
    currency = serializers.ChoiceField(choices=Currency.choices)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        discount = attrs.get("variant_discount")
        selling_price = attrs.get("selling_price")
        if discount > selling_price:
            raise serializers.ValidationError(
                {"message": "discount cannot be greater than selling price"}
            )

        return super().validate(attrs)


class CreateProductSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    category = serializers.PrimaryKeyRelatedField(
        queryset=models.Category.objects.all()
    )
    product_name = serializers.CharField()
    product_description = serializers.CharField()
    product_tag = serializers.CharField()
    stock_quantity = serializers.IntegerField()
    stock_alert = serializers.IntegerField()
    product_image_1 = serializers.URLField(required=False, allow_blank=False)
    product_image_2 = serializers.URLField(required=False, allow_blank=False)
    product_image_3 = serializers.URLField(required=False, allow_blank=False)
    product_image_4 = serializers.URLField(required=False, allow_blank=False)
    product_image_5 = serializers.URLField(required=False, allow_blank=False)
    cost_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.00)],
    )
    selling_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.00)],
    )
    discount = serializers.DecimalField(
        max_digits=13, decimal_places=2, validators=[MinValueValidator(0.00)]
    )
    sell_without_inventory = serializers.BooleanField(default=True)
    restaurant_item = serializers.BooleanField(default=False)
    has_variants = serializers.BooleanField(default=True)
    currency = serializers.ChoiceField(choices=Currency.choices)
    variants = ProductVariantSerializer(many=True, required=False)
    product_location = serializers.CharField(required=False, allow_blank=True)
    allow_backorders = serializers.ChoiceField(
        choices=Backorders.choices, default=Backorders.DO_NOT_ALLOW
    )
    low_stock_threshold = serializers.IntegerField(default=0)
    out_of_stock_threshold = serializers.IntegerField(default=0)
    item_sold_individually = serializers.BooleanField(default=False)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        sell_without_inventory = attrs.get("sell_without_inventory")
        has_variants = attrs.get("has_variants")
        stock_quantity = attrs.get("stock_quantity")
        variants = attrs.get("variants", None)
        product_name = attrs.get("product_name").title()
        discount = attrs.get("discount")
        selling_price = attrs.get("selling_price")
        low_stock_threshold = attrs.get("low_stock_threshold", 0)
        out_of_stock_threshold = attrs.get("out_of_stock_threshold", 0)
        if low_stock_threshold < 0 or out_of_stock_threshold < 0:
            raise serializers.ValidationError(
                {"message": "Thresholds cannot be negative"}
            )
        if discount > selling_price:
            raise serializers.ValidationError(
                {"message": "discount cannot be greater than selling price"}
            )
        product = models.Product.objects.filter(
            name=product_name, company=attrs["company"]
        ).last()
        if product:
            raise serializers.ValidationError(
                {"message": "Product name already exists"}
            )
        attrs["product_name"] = product_name
        if not sell_without_inventory:
            if has_variants:
                if variants is not None:
                    total_variants = sum(
                        [variant.get("quantity") for variant in variants]
                    )
                    if total_variants > stock_quantity:
                        raise serializers.ValidationError(
                            {
                                "message": "the total quantity for the variants can not be more than the total stock quantity of items."
                            }
                        )
                else:
                    raise serializers.ValidationError(
                        {"message": "variant cannot be empty"}
                    )
        return super().validate(attrs)


class UpdateProductSerializer(serializers.ModelSerializer):
    variants = ProductVariantSerializer(many=True, required=False)

    class Meta:
        model = models.Product
        fields = [
            "company",
            "category",
            "subcategory",
            "name",
            "selected_branches",
            "product_description",
            "product_tag",
            "product_image_1",
            "product_image_2",
            "product_image_3",
            "product_image_4",
            "product_image_5",
            "product_price",
            "selling_price",
            "discount",
            "sell_without_inventory",
            "is_restaurant_item",
            "has_variants",
            "currency",
            "variants",
        ]

    def validate(self, attrs):
        discount = attrs.get("discount")
        selling_price = attrs.get("selling_price")
        product_name = attrs.get("product_name", "").title()

        if discount and selling_price and discount > selling_price:
            raise serializers.ValidationError(
                {"message": "Discount cannot be greater than the selling price"}
            )

        if product_name:
            company = (
                self.instance.company
            )  # Get the company from the instance being updated
            if (
                models.Product.objects.filter(name=product_name, company=company)
                .exclude(id=self.instance.id)
                .exists()
            ):
                raise serializers.ValidationError(
                    {"message": "Product name already exists for this company"}
                )

        if not attrs.get("sell_without_inventory") and attrs.get("has_variants"):
            variants = attrs.get("variants", [])
            stock_quantity = attrs.get("stock_quantity")
            if variants and stock_quantity:
                total_variants = sum([variant["quantity"] for variant in variants])
                if total_variants > stock_quantity:
                    raise serializers.ValidationError(
                        {
                            "message": "The total quantity for the variants cannot exceed the total stock quantity."
                        }
                    )

        return attrs

    def update(self, instance, validated_data):
        variants_data = validated_data.pop("variants", None)

        # Update the product instance fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance


class AIStockSerializer(serializers.Serializer):
    category = serializers.CharField(max_length=255)
    product = serializers.CharField(max_length=255)
    quantity = serializers.IntegerField()
    cost_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.00)],
    )
    selling_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.00)],
    )


class AIStockUploadSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    stocks = AIStockSerializer(many=True)


class CompanyProductCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.PriceList
        fields = ["company"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company_name"] = instance.company.company_name
        if instance.category is not None:
            category_id = instance.category.id
            representation["id"] = category_id
            representation["name"] = instance.category.name
            subcategories = models.SubCategory.objects.filter(category_id=category_id)
            representation["subcategories"] = subcategories.values("id", "name")
        return representation


class StockSerializer(serializers.ModelSerializer):
    vat = serializers.FloatField(default=0.0)

    class Meta:
        model = models.StockDetail
        fields = [
            "company",
            "branch",
            "category",
            "subcategory",
            "supplied_by",
            "name",
            "item",
            "sku",
            "quantity",
            "measure_unit",
            "stock_price",
            "has_bulk",
            "bulk_name",
            "bulk_qty",
            "stock_alert",
            "selling_price",
            "image",
            "description",
            "show_in_web_stores",
            "expiry_date",
            "vat",
        ]


class FinishedProductRecipeSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.FinishedProductRecipe
        fields = "__all__"


class FinishedProductSerializer(serializers.ModelSerializer):
    recipes = FinishedProductRecipeSerializer(many=True, required=False)

    class Meta:
        model = models.FinishedProduct
        fields = "__all__"

    @transaction.atomic
    def create(self, validated_data):
        recipes_data = validated_data.pop("recipes", [])
        finished_product = models.FinishedProduct.objects.create(**validated_data)

        recipe_instances = [
            models.FinishedProductRecipe(
                finished_product=finished_product, **recipe_data
            )
            for recipe_data in recipes_data
        ]
        models.FinishedProductRecipe.objects.bulk_create(recipe_instances)

        return finished_product

    def update(self, instance, validated_data):
        recipes_data = validated_data.pop("recipes", [])
        instance.name = validated_data.get("name", instance.name)
        instance.save()

        # Update or create recipes
        for recipe_data in recipes_data:
            recipe_id = recipe_data.get("id", None)
            if recipe_id:
                recipe_instance = models.FinishedProductRecipe.objects.get(
                    id=recipe_id, finished_product=instance
                )
                recipe_instance.product = recipe_data.get(
                    "product", recipe_instance.product
                )
                recipe_instance.quantity = recipe_data.get(
                    "quantity", recipe_instance.quantity
                )
                recipe_instance.save()
            else:
                models.FinishedProductRecipe.objects.create(
                    finished_product=instance, **recipe_data
                )

        return instance


class ManageProductSerializer(serializers.Serializer):
    category = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    subcategory = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    name = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    product_id = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    spec_model_variant = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    stock_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )
    selling_price = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )
    discount = serializers.DecimalField(
        max_digits=13,
        decimal_places=2,
        required=False,
    )
    product_description = serializers.CharField(
        max_length=2300,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    product_tag = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    product_location = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    sku = serializers.CharField(
        max_length=255,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    product_image_1 = serializers.CharField(
        max_length=2300,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    product_image_2 = serializers.CharField(
        max_length=2300,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    product_image_3 = serializers.CharField(
        max_length=2300,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    product_image_4 = serializers.CharField(
        max_length=2300,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    product_image_5 = serializers.CharField(
        max_length=2300,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    low_stock_threshold = serializers.IntegerField(required=False)
    out_of_stock_threshold = serializers.IntegerField(required=False)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        company = self.context.get("company")
        product_id = str(self.context.get("product_id"))
        if "sku" in attrs and is_valid_string(attrs.get("sku")):
            existing_sku = models.Product.objects.filter(
                company=company, sku=attrs.get("sku")
            ).last()
            if existing_sku and (str(existing_sku.id) != product_id):
                raise serializers.ValidationError(
                    {"message": "This SKU has been assigned to a product previously."}
                )
        if "category" in attrs:
            if is_valid_string(attrs.get("category")):
                if is_valid_uuid(attrs.get("category")):
                    category = models.Category.objects.filter(
                        id=attrs.get("category")
                    ).first()
                    if category is None:
                        raise serializers.ValidationError(
                            "Provide a valid category ID."
                        )
                    attrs["category"] = category
                else:
                    raise serializers.ValidationError("Provide a valid category ID.")
            else:
                attrs.pop("category")
        if "subcategory" in attrs:
            if is_valid_string(attrs.get("subcategory")):
                if is_valid_uuid(attrs.get("subcategory")):
                    subcategory = models.SubCategory.objects.filter(
                        id=attrs.get("subcategory")
                    ).first()
                    if subcategory is None:
                        raise serializers.ValidationError(
                            "Provide a valid subcategory ID."
                        )
                    attrs["subcategory"] = subcategory
                else:
                    raise serializers.ValidationError("Provide a valid subcategory ID.")
            else:
                attrs.pop("subcategory")
        if "name" in attrs and not is_valid_string(attrs.get("name")):
            attrs.pop("name")
        if "stock_price" in attrs and attrs.get("stock_price") <= 0:
            attrs.pop("stock_price")
        if "selling_price" in attrs and attrs.get("selling_price") <= 0:
            attrs.pop("selling_price")
        if "discount" in attrs and attrs.get("discount") <= 0:
            attrs.pop("discount")
        if "product_tag" in attrs and not is_valid_string(attrs.get("product_tag")):
            attrs.pop("product_tag")
        attrs["product_price"] = attrs.get("stock_price")
        if attrs.get("product_price") is None:
            attrs.pop("product_price")
        attrs["price"] = attrs.get("selling_price")
        if attrs.get("price") is None:
            attrs.pop("price")
        if "low_stock_threshold" in attrs and attrs.get("low_stock_threshold") < 0:
            attrs.pop("low_stock_threshold")
        if (
            "out_of_stock_threshold" in attrs
            and attrs.get("out_of_stock_threshold") < 0
        ):
            attrs.pop("out_of_stock_threshold")
        return attrs


class DepleteStockActionSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    product = serializers.PrimaryKeyRelatedField(queryset=models.Product.objects.all())
    quantity = serializers.IntegerField()
    reason = serializers.CharField(max_length=255)


class BranchSettingsSerializer(serializers.Serializer):
    sell_without_inventory = serializers.BooleanField(
        required=False,
        allow_null=True,
    )
    transfer_charges_to_customer = serializers.BooleanField(
        required=False,
        allow_null=True,
    )
    use_product_vat = serializers.BooleanField(
        required=False,
        allow_null=True,
    )
    allow_store_credit = serializers.BooleanField(
        required=False,
        allow_null=True,
    )

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if len(attrs) < 1:
            raise serializers.ValidationError(
                {"message": "no field(s) was passed to be updated."}
            )
        return attrs


class PriceTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.PriceTag
        fields = "__all__"
        read_only_fields = ["created_by"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = (
            instance.company.company_name if instance.company is not None else ""
        )
        representation["created_by"] = (
            instance.created_by.full_name if instance.created_by is not None else ""
        )
        representation["updated_by"] = (
            instance.updated_by.full_name if instance.updated_by is not None else ""
        )
        return representation

    def validate(self, attrs):
        attrs = super().validate(attrs)
        """
        NOTE:
        - A company should not be allowed more than five (5) price tags
        after the system default(s).
        - A company and associated price tag name has a unique together constraint.
        """
        if models.PriceTag.objects.filter(company=attrs.get("company")).count() >= 5:
            raise serializers.ValidationError(
                {"message": "company cannot have more than five (5) price tags."}
            )
        if models.PriceTag.objects.filter(
            company=attrs.get("company"), name=attrs.get("name").lower()
        ).exists():
            raise serializers.ValidationError({"message": "duplicate price tag name."})
        return attrs


class UpdatePriceTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.PriceTag
        fields = [
            "name",
        ]

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
            try:
                instance.save()
            except IntegrityError as error:
                raise serializers.ValidationError(
                    {"message": "duplicate price tag name."}
                )
        return super().update(instance, validated_data)


class PriceTagDeleteSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    price_tag = serializers.PrimaryKeyRelatedField(
        queryset=models.PriceTag.objects.filter(system_default=False)
    )

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if not models.PriceTag.objects.filter(
            company=attrs.get("company"),
            id=attrs.get("price_tag").id,
        ).exists():
            raise serializers.ValidationError(
                {"message": "provide a valid price tag ID."}
            )
        return attrs


class PriceVariationSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.PriceVariation
        fields = [
            "price_tag",
            "method",
            "percentage",
            "price",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["tag"] = instance.price_tag.name
        return representation


class ProductPriceVariationSerializer(serializers.ModelSerializer):
    price_variation = serializers.SerializerMethodField()

    class Meta:
        model = models.Product
        fields = [
            "id",
            "name",
            "product_price",
            "selling_price",
            "price_variation",
        ]

    def get_price_variation(self, obj):
        data = dict()
        for item in obj.pricevariation_set.all():
            item_dict = dict()
            item_dict["price_tag"] = str(item.price_tag.id)
            item_dict["method"] = item.method
            item_dict["percentage"] = item.percentage
            item_dict["price"] = item.price
            item_dict["tag"] = item.price_tag.name

            data[str(item.price_tag.name)] = item_dict
            ...
        return data

    # def to_representation(self, instance):
    #     representation = super().to_representation(instance)
    #     representation["price_variations"] = PriceVariationSerializer(
    #         instance.pricevariation_set.all(), many=True
    #     ).data
    #     return representation


class PriceTagVariationSerializer(serializers.Serializer):
    price_tag = serializers.PrimaryKeyRelatedField(
        queryset=models.PriceTag.objects.all()
    )
    percentage = serializers.FloatField(required=False)
    price = serializers.DecimalField(max_digits=13, decimal_places=2, required=False)


class UpdatePriceVariationSerializer(serializers.Serializer):
    product = serializers.PrimaryKeyRelatedField(queryset=models.Product.objects.all())
    product_price = serializers.DecimalField(max_digits=13, decimal_places=2)
    selling_price = serializers.DecimalField(max_digits=13, decimal_places=2)
    method = serializers.ChoiceField(choices=PriceVariationMethods.choices)
    price_variations = PriceTagVariationSerializer(many=True)

    def validate(self, attrs):
        """
        NOTE:
        - mark down percentage is capped at 100.
        - mark up percentage is not capped at all.
        - margin percentage is capped at 99.
        """
        attrs = super().validate(attrs)
        if attrs.get("price_variations") == []:
            raise serializers.ValidationError(
                {"message": "price variations cannot be empty."}
            )
        for variation in attrs.get("price_variations"):
            if (
                attrs.get("method")
                in [
                    PriceVariationMethods.MARGIN,
                    PriceVariationMethods.MARK_DOWN,
                    PriceVariationMethods.MARK_UP,
                ]
                and "percentage" not in variation
            ):
                raise serializers.ValidationError(
                    {
                        "message": f"provide a valid percentage value for {attrs.get('method')}."
                    }
                )
            if "percentage" in variation:
                if attrs.get("method") == PriceVariationMethods.MARK_DOWN and not (
                    variation.get("percentage") >= 0
                    and variation.get("percentage") <= 100
                ):
                    raise serializers.ValidationError(
                        {"message": "mark down percentage must range between 0 to 100."}
                    )
                if attrs.get("method") == PriceVariationMethods.MARGIN and not (
                    variation.get("percentage") >= 0
                    and variation.get("percentage") <= 99
                ):
                    raise serializers.ValidationError(
                        {"message": "margin percentage must range between 0 to 99."}
                    )
                attrs["percentage"] = round(float(variation.get("percentage")), 2)
        return super().validate(attrs)


class ManagePriceVariationSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    products = UpdatePriceVariationSerializer(many=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if attrs.get("products") == []:
            raise serializers.ValidationError({"message": "products cannot be empty."})
        return super().validate(attrs)


class SupplierProductDetailSerializerIn(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    sku = serializers.CharField(required=False)
    size = serializers.CharField(required=False)
    color = serializers.CharField(required=False)
    weight = serializers.FloatField(required=False)
    length = serializers.FloatField(required=False)
    width = serializers.FloatField(required=False)
    height = serializers.FloatField(required=False)
    stock = serializers.IntegerField(required=False)
    price = serializers.FloatField(required=False)
    discount = serializers.FloatField(required=False)
    low_stock_threshold = serializers.IntegerField(required=False)
    shipping_days = serializers.IntegerField(required=False)

    def validate(self, attrs):
        prod_detail_id = attrs.get("id")
        if (
            prod_detail_id
            and not models.CompanyStoreSupplierProductDetail.objects.filter(
                id=prod_detail_id
            ).exists()
        ):
            raise InvalidRequestException(
                {"message": f"Incorrect ProductDetailID: {prod_detail_id}"}
            )
        return attrs


class SupplierProductImageSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = models.SupplierProductImage
        exclude = []


class SupplierProductDetailSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = models.SupplierProductDetail
        exclude = ["product"]


class SimilarSupplierProductSerializerOut(serializers.ModelSerializer):
    average_rating = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()
    price = serializers.SerializerMethodField()
    discount = serializers.SerializerMethodField()
    supplier = serializers.SerializerMethodField()

    def get_supplier(self, obj):
        if obj.supplier:
            image = None
            request = self.context.get("request")
            if obj.supplier.logo:
                image = request.build_absolute_uri(obj.supplier.logo.url)
            return {
                "id": obj.supplier.id,
                "name": obj.supplier.name,
                "address": obj.supplier.address,
                "active": obj.supplier.is_active,
                "logo": image,
            }
        return None

    def get_price(self, obj):
        query = models.SupplierProductDetail.objects.filter(product=obj)
        if query.exists():
            return query.first().price
        return 0

    def get_discount(self, obj):
        query = models.SupplierProductDetail.objects.filter(product=obj)
        if query.exists():
            return query.first().discount
        return 0

    def get_average_rating(self, obj):
        return (
            models.SupplierProductReview.objects.filter(product=obj).aggregate(
                Avg("rating")
            )["rating__avg"]
            or 0
        )

    def get_image(self, obj):
        return SupplierProductImageSerializerOut(
            models.SupplierProductImage.objects.filter(
                product=obj, is_primary=True
            ).last()
        ).data

    class Meta:
        model = models.SupplierProduct
        fields = [
            "id",
            "name",
            "is_featured",
            "average_rating",
            "image",
            "price",
            "discount",
            "supplier",
        ]


class BrandSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = models.Brand
        exclude = []


class SupplierProductSerializerOut(serializers.ModelSerializer):
    supplier = serializers.SerializerMethodField()
    brand = serializers.SerializerMethodField()
    category = serializers.SerializerMethodField()
    sub_category = serializers.SerializerMethodField()
    product_type = serializers.SerializerMethodField()
    similar = serializers.SerializerMethodField()
    product_details = serializers.SerializerMethodField()
    images = serializers.SerializerMethodField()

    def get_images(self, obj):
        return SupplierProductImageSerializerOut(
            models.SupplierProductImage.objects.filter(product=obj), many=True
        ).data

    def get_product_details(self, obj):
        prod_details = models.SupplierProductDetail.objects.filter(product=obj)
        if not obj.out_of_stock:
            total_stock = prod_details.aggregate(Sum("stock"))["stock__sum"] or 0
            if total_stock < 1:
                obj.out_of_stock = True
                obj.save()
        return SupplierProductDetailSerializerOut(
            prod_details.order_by("-stock"), many=True
        ).data

    def get_supplier(self, obj):
        if obj.supplier:
            image = None
            request = self.context.get("request")
            if obj.supplier.logo:
                image = request.build_absolute_uri(obj.supplier.logo.url)
            return {
                "id": obj.supplier.id,
                "name": obj.supplier.name,
                "address": obj.supplier.address,
                "active": obj.supplier.is_active,
                "logo": image,
            }
        return None

    def get_brand(self, obj):
        if obj.brand:
            return {"id": obj.brand.id, "name": obj.brand.name}
        return None

    def get_category(self, obj):
        if obj.category:
            return {"id": obj.category.id, "name": obj.category.name}
        return None

    def get_sub_category(self, obj):
        if obj.sub_category:
            return {"id": obj.sub_category.id, "name": obj.sub_category.name}
        return None

    def get_product_type(self, obj):
        if obj.product_type:
            return {"id": obj.product_type.id, "name": obj.product_type.name}
        return None

    def get_similar(self, obj):
        product = (
            models.SupplierProduct.objects.filter(
                supplier__is_active=True, is_active=True, sub_category=obj.sub_category
            )
            .order_by("?")
            .exclude(pk=obj.id)
            .distinct()[:5]
        )
        return SimilarSupplierProductSerializerOut(
            product, many=True, context={"request": self.context.get("request")}
        ).data

    class Meta:
        model = models.SupplierProduct
        exclude = []


class SupplierProductImageSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    images = serializers.ListField(
        child=AssetImageUploadSerializerIn(),
        required=False,
        allow_null=True,
        allow_empty=False,
    )
    product_id = serializers.UUIDField(required=False, allow_null=True)

    def create(self, validated_data):
        user = validated_data.get("user")
        images = validated_data.get("images")
        product_id = validated_data.get("product_id")

        if not all([images, product_id]):
            raise InvalidRequestException(
                {"message": "ProductID and List of images are required"}
            )

        supplier = is_supplier_check(user.email)

        products = models.SupplierProduct.objects.filter(
            id=product_id, supplier=supplier
        )
        if not products.exists():
            raise InvalidRequestException({"message": "Invalid product selected"})

        product = products.last()

        image_list = list()

        first_image = True
        for img in images:
            image = img.get("image")

            # Create SupplierProductImage
            sup_prod_image = models.SupplierProductImage.objects.create(product=product)
            url = upload_file_aws_s3_bucket(
                model_instance_id=sup_prod_image.id,
                file=image,
                model_name="SupplierProductImage",
            )

            if url:
                sup_prod_image.image = url
                if first_image:
                    sup_prod_image.is_primary = True
                    first_image = False
                sup_prod_image.save()
                image_list.append(str(sup_prod_image.id))

        return SupplierProductImageSerializerOut(
            models.SupplierProductImage.objects.filter(id__in=image_list), many=True
        ).data

    def update(self, instance, validated_data):
        # Set image as Primary
        other_images = models.SupplierProductImage.objects.filter(
            product=instance.product
        ).exclude(id=instance.id)
        if other_images:
            other_images.update(is_primary=False)
        instance.is_primary = True
        instance.save()

        return SupplierProductImageSerializerOut(instance).data


class ProductDataSerializer(serializers.Serializer):
    brand_id = serializers.UUIDField(required=False)
    product_name = serializers.CharField(required=False)
    category_id = serializers.UUIDField(required=False)
    sub_category_id = serializers.UUIDField(required=False)
    product_type_id = serializers.UUIDField(required=False)
    description = serializers.CharField(required=False)
    tags = serializers.CharField(required=False)
    model = serializers.CharField(required=False)
    publish = serializers.BooleanField(required=False)
    product_details = serializers.ListField(
        child=SupplierProductDetailSerializerIn(), required=False
    )
    new_product_details = serializers.ListField(
        child=SupplierProductDetailSerializerIn(), required=False
    )
    images = serializers.ListField(
        child=AssetImageUploadSerializerIn(), required=False, allow_empty=False
    )
    image_urls = serializers.ListField(
        child=serializers.CharField(), required=False, allow_empty=True, allow_null=True
    )

    def validate(self, attrs):
        category_id = attrs.get("category_id")
        sub_category_id = attrs.get("sub_category_id")
        product_type_id = attrs.get("product_type_id")
        brand_id = attrs.get("brand_id")
        if brand_id:
            if not models.Brand.objects.filter(id=brand_id).exists():
                raise InvalidRequestException({"message": "Brand not found"})
        if category_id:
            if not models.Category.objects.filter(id=category_id).exists():
                raise InvalidRequestException({"message": "Category not found"})
        if sub_category_id:
            if not models.SubCategory.objects.filter(
                id=sub_category_id, category_id=category_id
            ).exists():
                raise InvalidRequestException({"message": "Sub Category not found"})
        if product_type_id:
            if not models.ProductType.objects.filter(
                id=product_type_id, category_id=sub_category_id
            ).exists():
                raise InvalidRequestException({"message": "Product Type not found"})

        return attrs


class SupplierProductSerializerIn(ProductDataSerializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())

    def validate(self, attrs):
        category_id = attrs.get("category_id")
        sub_category_id = attrs.get("sub_category_id")
        product_type_id = attrs.get("product_type_id")
        brand_id = attrs.get("brand_id")
        if brand_id:
            if not models.Brand.objects.filter(id=brand_id).exists():
                raise InvalidRequestException({"message": "Brand not found"})
        if category_id:
            if not models.Category.objects.filter(id=category_id).exists():
                raise InvalidRequestException({"message": "Category not found"})
        if sub_category_id:
            if not models.SubCategory.objects.filter(
                id=sub_category_id, category_id=category_id
            ).exists():
                raise InvalidRequestException({"message": "Sub Category not found"})
        if product_type_id:
            if not models.ProductType.objects.filter(
                id=product_type_id, category_id=sub_category_id
            ).exists():
                raise InvalidRequestException({"message": "Product Type not found"})

        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        brand_id = validated_data.get("brand_id")
        product_name = validated_data.get("product_name")
        category_id = validated_data.get("category_id")
        sub_category_id = validated_data.get("sub_category_id")
        product_type_id = validated_data.get("product_type_id")
        tags = validated_data.get("tags")
        description = validated_data.get("description")
        model = validated_data.get("model")
        product_details = validated_data.get("product_details")
        images = validated_data.get("images")
        published = validated_data.get("publish", False)

        if not all(
            [
                brand_id,
                product_name,
                category_id,
                sub_category_id,
                model,
                description,
                product_details,
            ]
        ):
            raise InvalidRequestException(
                {
                    "message": "Required: Brand, Product Name, Description, Category, Sub Category, Model, Stock Count, Price, and Product Details"
                }
            )

        supplier = is_supplier_check(user.email)

        product, _ = models.SupplierProduct.objects.get_or_create(
            supplier=supplier, name=product_name, brand_id=brand_id
        )
        product.category_id = category_id
        product.sub_category_id = sub_category_id
        product.product_type_id = product_type_id
        product.tags = tags
        product.model = model
        product.description = description
        if published:
            product.published = True
            product.published_on = datetime.datetime.now()
        product.save()

        keys = [
            "sku",
            "size",
            "color",
            "weight",
            "length",
            "width",
            "height",
            "stock",
            "price",
            "discount",
            "low_stock_threshold",
            "shipping_days",
        ]

        for product_detail in product_details:
            product_data = {key: product_detail.get(key) for key in keys}
            product_data["product"] = product
            models.SupplierProductDetail.objects.create(**product_data)

        if images:
            first_image = True
            for img in images:
                image = img.get("image")

                # Create SupplierProductImage
                sup_prod_image = models.SupplierProductImage.objects.create(
                    product=product
                )
                url = upload_file_aws_s3_bucket(
                    model_instance_id=sup_prod_image.id,
                    file=image,
                    model_name="SupplierProductImage",
                )

                if url:
                    sup_prod_image.image = url
                    if first_image:
                        sup_prod_image.is_primary = True
                        first_image = False
                    sup_prod_image.save()

        return SupplierProductSerializerOut(
            product, context={"request": self.context.get("request")}
        ).data

    def update(self, instance, validated_data):
        instance.brand_id = validated_data.get("brand_id", instance.brand_id)
        instance.name = validated_data.get("product_name", instance.name)
        instance.category_id = validated_data.get("category_id", instance.category_id)
        instance.sub_category_id = validated_data.get(
            "sub_category_id", instance.sub_category_id
        )
        instance.product_type_id = validated_data.get(
            "product_type_id", instance.product_type_id
        )
        instance.tags = validated_data.get("tags", instance.tags)
        instance.description = validated_data.get("description", instance.description)
        instance.model = validated_data.get("model", instance.model)
        product_details = validated_data.get("product_details")
        new_product_details = validated_data.get("new_product_details")
        published = validated_data.get("publish", False)

        if published:
            instance.published = True
            instance.published_on = datetime.datetime.now()
        if product_details:
            for product_detail in product_details:
                try:
                    prod_variation = models.SupplierProductDetail.objects.get(
                        id=product_detail.get("id")
                    )
                except models.SupplierProductDetail.DoesNotExist:
                    raise InvalidRequestException(
                        {"message": "Product detail not found"}
                    )
                prod_variation.sku = product_detail.get("sku", prod_variation.sku)
                prod_variation.size = product_detail.get("size", prod_variation.size)
                prod_variation.color = product_detail.get("color", prod_variation.color)
                prod_variation.weight = product_detail.get(
                    "weight", prod_variation.weight
                )
                prod_variation.length = product_detail.get(
                    "length", prod_variation.length
                )
                prod_variation.width = product_detail.get("width", prod_variation.width)
                prod_variation.height = product_detail.get(
                    "height", prod_variation.height
                )
                prod_variation.stock = product_detail.get("stock", prod_variation.stock)
                prod_variation.price = product_detail.get("price", prod_variation.price)
                prod_variation.discount = product_detail.get(
                    "discount", prod_variation.discount
                )
                prod_variation.low_stock_threshold = product_detail.get(
                    "low_stock_threshold", prod_variation.low_stock_threshold
                )
                prod_variation.shipping_days = product_detail.get(
                    "shipping_days", prod_variation.shipping_days
                )
                prod_variation.save()

        if new_product_details:
            keys = [
                "sku",
                "size",
                "color",
                "weight",
                "length",
                "width",
                "height",
                "stock",
                "price",
                "discount",
                "low_stock_threshold",
                "shipping_days",
            ]
            for product_detail in new_product_details:
                product_data = {key: product_detail.get(key) for key in keys}
                product_data["product"] = instance
                models.SupplierProductDetail.objects.create(**product_data)

        instance.save()
        return SupplierProductSerializerOut(
            instance, context={"request": self.context.get("request")}
        ).data


class CreateBNPLProductSupplierSerializerIn(serializers.Serializer):
    company_id = serializers.UUIDField()
    supplier_id = serializers.UUIDField(required=False)
    supplier_data = serializers.ListField(
        child=SupplierDataSerializer(), required=False
    )
    products = serializers.ListField(child=ProductDataSerializer())

    def create(self, validated_data):
        company_id = validated_data.get("company_id")
        supplier_id = validated_data.get("supplier_id")
        supplier_data = validated_data.get("supplier_data")
        products = validated_data.get("products")

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise InvalidRequestException({"message": "Invalid Company ID"})

        if not any([supplier_id, supplier_data]) or all([supplier_id, supplier_data]):
            raise InvalidRequestException(
                {
                    "message": "You can either select an existing supplier or fill data for new supplier"
                }
            )

        if supplier_id:
            try:
                supplier = models.Supplier.objects.get(id=supplier_id)
            except models.Supplier.DoesNotExist:
                raise InvalidRequestException({"message": "Invalid supplier selected"})
        else:
            # Create supplier
            raw_supplier = supplier_data[0]
            if models.Supplier.objects.filter(
                name__iexact=raw_supplier.get("business_name")
            ).exists():
                raise InvalidRequestException(
                    {"message": "Business name already exist"}
                )
            if User.objects.filter(
                email__iexact=raw_supplier.get("business_email")
            ).exists():
                raise InvalidRequestException(
                    {"message": "User with provided business email already exist"}
                )

            supplier = onboard_supplier(
                raw_supplier, company, approve_status=True, user=None
            )

        # Create Product(s)
        keys = [
            "sku",
            "size",
            "color",
            "weight",
            "length",
            "width",
            "height",
            "stock",
            "price",
            "discount",
            "low_stock_threshold",
            "shipping_days",
        ]
        created_products = list()

        for prod in products:
            product_name = prod.get("product_name")
            brand_id = prod.get("brand_id")
            category_id = prod.get("category_id")
            sub_category_id = prod.get("sub_category_id")
            product_type_id = prod.get("product_type_id")
            description = prod.get("description")
            tags = prod.get("tags")
            model = prod.get("model")
            product_details = prod.get("product_details")

            images = prod.get("image_urls")

            product, _ = models.SupplierProduct.objects.get_or_create(
                supplier=supplier, name=product_name, brand_id=brand_id
            )
            product.category_id = category_id
            product.sub_category_id = sub_category_id
            product.product_type_id = product_type_id
            product.tags = tags
            product.model = model
            product.published = True
            product.published_on = datetime.datetime.now()
            product.description = description
            product.save()

            created_products.append(product)

            for product_detail in product_details:
                product_data = {key: product_detail.get(key) for key in keys}
                product_data["product"] = product
                models.SupplierProductDetail.objects.create(**product_data)

            if images:
                first_image = True
                for img in images:

                    # Create SupplierProductImage
                    sup_prod_image = models.SupplierProductImage.objects.create(
                        product=product, image=img
                    )
                    if first_image:
                        sup_prod_image.is_primary = True
                        first_image = False
                        sup_prod_image.save()
        context = {"request": self.context.get("request")}
        return {
            "supplier": SupplierSerializerOut(supplier, context=context).data,
            "products": SupplierProductSerializerOut(
                created_products, context=context, many=True
            ).data,
        }


class UnlistProductSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = serializers.PrimaryKeyRelatedField(queryset=models.Branch.objects.all())
    items = serializers.PrimaryKeyRelatedField(
        queryset=models.Product.objects.all(),
        many=True,
    )
    unlist = serializers.BooleanField()

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if attrs.get("items") == []:
            raise serializers.ValidationError({"message": "items cannot be empty."})
        attrs["items"] = [item.id for item in attrs.get("items")]
        return attrs


class CompanyBarcodeSerializer(serializers.Serializer):
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    product_barcode = serializers.CharField()
