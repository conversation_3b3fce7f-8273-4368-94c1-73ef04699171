from datetime import datetime

from django.conf import settings
from django.core.management.base import (
    BaseCommand,
)
import pytz

from stock_inventory.models import Branch, Product


class Command(BaseCommand):
    help = "UPDATE PRODUCT DETAILS."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        products = Product.objects.all()
        print(f"\n\n\nTOTAL PRODUCTS:        {products.count()}\n\n\n")
        for product in products:
            print(f"\n\n\n{product}\n\n\n")
            company_branches = Branch.objects.filter(company=product.company)
            product.selected_branches.set(company_branches)
            product.save()
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
        )
        self.stdout.write(self.style.SUCCESS("SUCCESSFULLY UPDATED INVENTORY DETAILS."))
