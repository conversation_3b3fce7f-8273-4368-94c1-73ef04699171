from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status

from django.utils import timezone
from django.db.models import Sum, Count, Avg
from django.db.models.functions import TruncMonth
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from core.auth.custom_auth import CustomUserAuthentication

from account.models import Transaction, Wallet
from account.enums import AccountType, TransactionType, TransactionStatus
from requisition.models import Company, Expense
from sales_app.models import (
    SalesTransaction,
    Customer,
    POSDevice,
    SalesTransactionItem,
    CardTransaction,
)
from sales_app.helper.enums import TransactionStatusChoices
from helpers.enums import ChannelTypes
from dashboard.helpers import AgencyBankingAPI, get_filter_dates, get_filter_date_values
import uuid


class WalletBalanceView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        company = Company.objects.filter(id=company_id).last()

        if company:
            wallet_qs = Wallet.objects.filter(
                account__company=company,
            )

            spend_wallet = wallet_qs.filter(
                wallet_type=AccountType.COMPANY_SPEND_MGMT,
            ).aggregate(total_amount=Sum("balance"))["total_amount"] or 0.00

            sales_wallet = wallet_qs.filter(
                wallet_type=AccountType.SALES,
            ).aggregate(total_amount=Sum("balance"))["total_amount"] or 0.00

            hr_payroll_wallet = wallet_qs.filter(
                wallet_type=AccountType.PAYROLL,
            ).aggregate(total_amount=Sum("balance"))["total_amount"] or 0.00

            data = {
                "wallet_balance": spend_wallet,
                "savings_balance": 0.00,
                "sales_wallet": sales_wallet,
                "hr_payroll_wallet": hr_payroll_wallet,
                "spend_management_wallet": spend_wallet,
            }
        else:
            data = {
                "wallet_balance": 0.00,
                "savings_balance": 0.00,
                "sales_wallet": 0.00,
                "hr_payroll_wallet": 0.00,
                "spend_management_wallet": 0.00,
            }
        return Response(data, status=status.HTTP_200_OK)


class CustomerOverviewView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        company = Company.objects.filter(id=company_id).last()

        if company:
            total_customers = Customer.objects.filter(company=company).count()
            total_savings = Wallet.objects.aggregate(total=Sum("balance"))[
                "total"
            ] or Decimal("0")

            pos_devices_count = POSDevice.objects.filter(company=company).count()

            data = {
                "total_customers": total_customers,
                "total_pos_devices": pos_devices_count,
                "total_savings": 0.00,
            }
        else:
            data = {
                "total_customers": 0,
                "total_pos_devices": 0,
                "total_savings": 0.00,
            }
        return Response(data, status=status.HTTP_200_OK)


class TotalTransferView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        company = Company.objects.filter(id=company_id).last()

        # requisition transfer from company wallet/account
        # - Account.Transaction.filter for transaction payout_type is ÁccountType.SPEND_MGT for transfer out

        # - FOR PROCUREMENT
        # - FOR

        filter_dates = get_filter_dates()
        today = filter_dates.get("today")
        yesterday = filter_dates.get("yesterday")
        # today_weekday = filter_dates.get("today_weekday")
        this_week_start = filter_dates.get("this_week_start")
        last_week_start = filter_dates.get("last_week_start")
        last_week_end = filter_dates.get("last_week_end")
        this_month_start = filter_dates.get("this_month_start")
        last_month_end = filter_dates.get("last_month_end")
        last_month_start = filter_dates.get("last_month_start")
        this_year_start = filter_dates.get("this_year_start")
        last_year_end = filter_dates.get("last_year_end")
        last_year_start = filter_dates.get("last_year_start")

        self.user_transactions_qs = Transaction.objects.filter(
            user=company.user,
            status=TransactionStatus.SUCCESSFUL,
        )

        # Get transfers for different time periods
        total_transfer = {
            "total_transfer": {
                "today": self._get_transfer_total(start_date=today, end_date=today),
                "yesterday": self._get_transfer_total(
                    start_date=yesterday, end_date=yesterday
                ),
                "this_week": self._get_transfer_total(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_transfer_total(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_transfer_total(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_transfer_total(
                    start_date=last_month_start, end_date=last_month_end
                ),
                "this_year": self._get_transfer_total(
                    start_date=this_year_start, end_date=today
                ),
                "last_year": self._get_transfer_total(
                    start_date=last_year_start, end_date=last_year_end
                ),
            },
            "total_inflow": {
                "today": self._get_inflow_total(start_date=today, end_date=today),
                "yesterday": self._get_inflow_total(
                    start_date=yesterday, end_date=yesterday
                ),
                "this_week": self._get_inflow_total(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_inflow_total(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_inflow_total(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_inflow_total(
                    start_date=last_month_start, end_date=last_month_end
                ),
                "this_year": self._get_inflow_total(
                    start_date=this_year_start, end_date=today
                ),
                "last_year": self._get_inflow_total(
                    start_date=last_year_start, end_date=last_year_end
                ),
            },
        }

        return Response(total_transfer, status=status.HTTP_200_OK)
        # payout tyoe is sales
        # deposit ==== to get inflows for sales

    def _get_transfer_total(self, start_date, end_date):
        total = self.user_transactions_qs.filter(
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            payout_type__in=[
                AccountType.SALES, AccountType.COMPANY_SPEND_MGMT, AccountType.PAYROLL
                ],
        ).aggregate(total=Sum("amount"))["total"] or Decimal("0")

        return total

    def _get_inflow_total(self, start_date, end_date):
        total = self.user_transactions_qs.filter(
            transaction_type__in=[
                TransactionType.DEPOSIT,
            ],
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            # status=TransactionStatus.SUCCESSFUL,
        ).aggregate(total=Sum("amount"))["total"] or Decimal("0")

        return total


class TransactionStatsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        today = timezone.now().date()
        company_id = request.query_params.get("company_id")
        company = Company.objects.filter(id=company_id).last()

        filter_dates = get_filter_dates()
        today = filter_dates.get("today")
        yesterday = filter_dates.get("yesterday")
        # today_weekday = filter_dates.get("today_weekday")
        this_week_start = filter_dates.get("this_week_start")
        last_week_start = filter_dates.get("last_week_start")
        last_week_end = filter_dates.get("last_week_end")
        this_month_start = filter_dates.get("this_month_start")
        last_month_end = filter_dates.get("last_month_end")
        last_month_start = filter_dates.get("last_month_start")
        this_year_start = filter_dates.get("this_year_start")
        last_year_end = filter_dates.get("last_year_end")
        last_year_start = filter_dates.get("last_year_start")

        self.user_transactions_qs = Transaction.objects.filter(
            user=company.user,
            status=TransactionStatus.SUCCESSFUL,
        )

        data = {
            "total_transaction_amount": {
                "today": self._get_total_amount(start_date=today),
                "yesterday": self._get_total_amount(start_date=yesterday),
                "this_week": self._get_total_amount(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_total_amount(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_total_amount(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_total_amount(
                    start_date=last_month_start, end_date=last_month_end
                ),
                "this_year": self._get_total_amount(
                    start_date=this_year_start, end_date=today
                ),
                "last_year": self._get_total_amount(
                    start_date=last_year_start, end_date=last_year_end
                ),
            },
            "total_transaction_count": {
                "today": self._get_total_count(start_date=today),
                "yesterday": self._get_total_count(start_date=yesterday),
                "this_week": self._get_total_count(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_total_count(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_total_count(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_total_count(
                    start_date=last_month_start, end_date=last_month_end
                ),
                "this_year": self._get_total_count(
                    start_date=this_year_start, end_date=today
                ),
                "last_year": self._get_total_count(
                    start_date=last_year_start, end_date=last_year_end
                ),
            },
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_total_amount(self, start_date, end_date=None):
        if end_date is None:
            end_date = start_date
        return self.user_transactions_qs.filter(
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
        ).aggregate(total=Sum("amount"))["total"] or Decimal("0")

    def _get_total_count(self, start_date, end_date=None):
        if end_date is None:
            end_date = start_date
        return self.user_transactions_qs.filter(
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
        ).count()


class FinancialMetricsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        company = Company.objects.filter(id=company_id).last()
        query_filter = request.query_params.get("filter")

        filter_dates = get_filter_dates()
        today = filter_dates.get("today")
        yesterday = filter_dates.get("yesterday")

        filter_start_date, filter_end_date = get_filter_date_values(
            query_filter=query_filter
            )

        self.sales_transactions_qs = SalesTransaction.objects.filter(
            company=company,
            status=TransactionStatusChoices.SUCCESSFUL,
        )

        self.sales_items_qs = SalesTransactionItem.objects.filter(
            company=company,
            refunded=False,
        )

        self.expense_qs = Expense.objects.filter(
            company=company,
            status=TransactionStatus.SUCCESSFUL
            )

        # Get base values
        gross_profit_today = self._get_gross_profit(start_date=today, end_date=today)
        gross_profit_yesterday = self._get_gross_profit(
            start_date=yesterday, end_date=yesterday
        )
        expense_today = self._get_total_expense(start_date=today, end_date=today)
        expense_yesterday = self._get_total_expense(
            start_date=yesterday, end_date=yesterday
        )

        gross_profit_total = self._get_gross_profit(
            start_date=filter_start_date, end_date=filter_end_date
        )
        expense_total = self._get_total_expense(
            start_date=filter_start_date, end_date=filter_end_date
        )

        data = {
            "gross_profit": {
                "total_amount": gross_profit_total,
                "today": gross_profit_today,
                "yesterday": gross_profit_yesterday,
                "previous_day_average": (gross_profit_today + gross_profit_yesterday)
                / 2,
                "percent_change": self._calculate_percent_change(
                    gross_profit_today, gross_profit_yesterday
                ),
                "change_direction": self._get_change_direction(
                    gross_profit_today, gross_profit_yesterday
                ),
            },
            "total_expense": {
                "total_amount": expense_total,
                "today": expense_today,
                "yesterday": expense_yesterday,
                "previous_day_average": (expense_today + expense_yesterday) / 2,
                "percent_change": self._calculate_percent_change(
                    expense_today, expense_yesterday
                ),
                "change_direction": self._get_change_direction(
                    expense_today, expense_yesterday
                ),
            },
        }

        return Response(data, status=status.HTTP_200_OK)

    def _calculate_percent_change(self, current_value, previous_value):
        if previous_value == 0:
            return 0
        change = ((current_value - previous_value) / previous_value) * 100
        return round(change, 2)

    def _get_change_direction(self, current_value, previous_value):
        if current_value > previous_value:
            return "up"
        elif current_value < previous_value:
            return "down"
        return "same"

    def _get_gross_profit(self, start_date, end_date):
        return self.sales_items_qs.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
        ).aggregate(total=Sum("gross_profit"))["total"] or Decimal("0")

    def _get_total_expense(self, start_date, end_date=None):
        return self.expense_qs.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
        ).aggregate(
            total=Sum("expense_amount")  # Updated to use expense_amount
        )[
            "total"
        ] or Decimal(
            "0"
        )


class TransactionComparativeView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        company = Company.objects.filter(id=company_id).last()
        query_filter = request.query_params.get("filter", "today")

        filter_start_date, filter_end_date = get_filter_date_values(
            query_filter=query_filter
            )

        if company_id is None:
            return Response(
                {"error": "Company ID is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            self.user_transactions_qs = Transaction.objects.filter(
                user=company.user,
                status=TransactionStatus.SUCCESSFUL,
                date_created__date__gte=filter_start_date,
                date_created__date__lte=filter_end_date,
            )

            self.user_cashout_transactions_qs = CardTransaction.objects.filter(
                company=company,
                status=True,
            )
        except Exception as e:
            return Response(
                {"error": f"Error fetching transactions: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get all-time values for each transaction type
        cashout_total = self._get_transaction_amount(transaction_type="WITHDRAWAL")
        send_money_total = self._get_transaction_amount(
            transaction_type=TransactionType.BANK_TRANSFER
        )
        bills_total = self._get_transaction_amount(
            transaction_type=TransactionType.AIRTIME_TOP_UP
        )

        # Calculate total for percentage calculations
        total_amount = (
            float(cashout_total) + float(send_money_total) + float(bills_total)
        )

        # Calculate percentages for each type
        data = {
            "cashout": {
                "total": cashout_total,
                "percentage": self._calculate_percentage(cashout_total, total_amount),
            },
            "send_money": {
                "total": send_money_total,
                "percentage": self._calculate_percentage(
                    send_money_total, total_amount
                ),
            },
            "utilities_and_bills": {
                "total": bills_total,
                "percentage": self._calculate_percentage(bills_total, total_amount),
            },
            "doughnut_chart_data": {
                "labels": ["Cashout", "Send Money", "Bills & Utilities"],
                "data": [cashout_total, send_money_total, bills_total],
                "percentages": [
                    self._calculate_percentage(cashout_total, total_amount),
                    self._calculate_percentage(send_money_total, total_amount),
                    self._calculate_percentage(bills_total, total_amount),
                ],
            },
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_transaction_amount(self, transaction_type):
        if transaction_type == "WITHDRAWAL":
            cashout_trans_amount = (
                self.user_cashout_transactions_qs.aggregate(total_amount=Sum("amount"))[
                    "total_amount"
                ]
                or 0.00
            )
            return cashout_trans_amount
        elif transaction_type == TransactionType.BANK_TRANSFER:
            send_money_amount = self.user_transactions_qs.filter(
                payout_type__in=[
                    AccountType.SALES, AccountType.COMPANY_SPEND_MGMT, AccountType.PAYROLL
                    ]
            ).aggregate(total=Sum("amount"))["total"] or 0.00
            return send_money_amount

        return self.user_transactions_qs.filter(
            # user=self.request.user,
            transaction_type=transaction_type,
            # status=TransactionStatus.SUCCESSFUL,
        ).aggregate(total=Sum("amount"))["total"] or Decimal("0")

    def _calculate_percentage(self, value, total):
        if total == 0:
            return 0
        return round((float(value) / float(total)) * 100, 2)


class TransactionMetricsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        company = Company.objects.filter(id=company_id).last()
        query_filter = request.query_params.get("filter", "today")

        filter_dates = get_filter_dates()
        today = filter_dates.get("today")
        yesterday = filter_dates.get("yesterday")
        this_week_start = filter_dates.get("this_week_start")
        last_week_start = filter_dates.get("last_week_start")
        last_week_end = filter_dates.get("last_week_end")
        this_month_start = filter_dates.get("this_month_start")
        last_month_end = filter_dates.get("last_month_end")
        last_month_start = filter_dates.get("last_month_start")
        this_year_start = filter_dates.get("this_year_start")
        last_year_end = filter_dates.get("last_year_end")
        last_year_start = filter_dates.get("last_year_start")

        filter_start_date, filter_end_date = get_filter_date_values(
            query_filter=query_filter
            )

        self.user_transactions_qs = Transaction.objects.filter(
            company_id=company.user,
            status=TransactionStatus.SUCCESSFUL,
        )

        self.user_cashout_transactions_qs = CardTransaction.objects.filter(
            company=company,
            status=True,
        )

        data = {
            "total_cashout": {
                "total_amount":self._get_total_amount(
                    start_date=filter_start_date, end_date=filter_end_date,
                    transaction_type="WITHDRAWAL"
                ),
                "today": self._get_total_amount(
                    start_date=today, end_date=today, transaction_type="WITHDRAWAL"
                ),
                "yesterday": self._get_total_amount(
                    start_date=yesterday,
                    end_date=yesterday,
                    transaction_type="WITHDRAWAL",
                ),
                "this_week": self._get_total_amount(
                    start_date=this_week_start,
                    end_date=today,
                    transaction_type="WITHDRAWAL",
                ),
                "last_week": self._get_total_amount(
                    start_date=last_week_start,
                    end_date=last_week_end,
                    transaction_type="WITHDRAWAL",
                ),
                "this_month": self._get_total_amount(
                    start_date=this_month_start,
                    end_date=today,
                    transaction_type="WITHDRAWAL",
                ),
                "last_month": self._get_total_amount(
                    start_date=last_month_start,
                    end_date=last_month_end,
                    transaction_type="WITHDRAWAL",
                ),
                "this_year": self._get_total_amount(
                    start_date=this_year_start,
                    end_date=today,
                    transaction_type="WITHDRAWAL",
                ),
                "last_year": self._get_total_amount(
                    start_date=last_year_start,
                    end_date=last_year_end,
                    transaction_type="WITHDRAWAL",
                ),
                "all_time": self._get_total_amount(
                    start_date=timezone.now().replace(year=2021),
                    end_date=today,
                    transaction_type="WITHDRAWAL",
                ),
            },
            "total_commission": {
                "total_amount": self._get_total_commission(start_date=today, end_date=today),
                "today": self._get_total_commission(start_date=today, end_date=today),
                "yesterday": self._get_total_commission(
                    start_date=yesterday, end_date=yesterday
                ),
                "this_week": self._get_total_commission(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_total_commission(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_total_commission(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_total_commission(
                    start_date=last_month_start, end_date=last_month_end
                ),
                "this_year": self._get_total_commission(
                    start_date=this_year_start, end_date=today
                ),
                "last_year": self._get_total_commission(
                    start_date=last_year_start, end_date=last_year_end
                ),
                "all_time": self._get_total_commission(
                    start_date=timezone.now().replace(year=2021),
                    end_date=timezone.now(),
                ),
            },
            "average_commission_per_transaction": {
                "today": self._get_average_commission(start_date=today, end_date=today),
                "yesterday": self._get_average_commission(
                    start_date=yesterday, end_date=yesterday
                ),
                "this_week": self._get_average_commission(
                    start_date=this_week_start,
                    end_date=today,
                ),
                "last_week": self._get_average_commission(
                    start_date=last_week_start,
                    end_date=last_week_end,
                ),
                "this_month": self._get_average_commission(
                    start_date=this_month_start,
                    end_date=today,
                ),
                "last_month": self._get_average_commission(
                    start_date=last_month_start,
                    end_date=last_month_end,
                ),
                "this_year": self._get_average_commission(
                    start_date=this_year_start,
                    end_date=today,
                ),
                "last_year": self._get_average_commission(
                    start_date=last_year_start,
                    end_date=last_year_end,
                ),
                "all_time": self._get_average_commission_all_time(),
            },
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_total_amount(self, start_date, end_date, transaction_type):
        if transaction_type == "WITHDRAWAL":
            result = (
                self.user_cashout_transactions_qs.filter(
                    created_at__date__gte=start_date,
                    created_at__date__lte=end_date,
                ).aggregate(total_amount=Sum("amount"))["total_amount"]
                or 0.00
            )
            return result

        return self.user_transactions_qs.filter(
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            transaction_type=transaction_type,
            user=self.request.user,
        ).aggregate(total=Sum("amount"))["total"] or Decimal("0")

    def _get_total_commission(self, start_date, end_date=None):
        return self.user_transactions_qs.filter(
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            # transaction_type__in=[TransactionType.COMMISSION],
            user=self.request.user,
        ).aggregate(total=Sum("commission"))["total"] or Decimal("0")

    def _get_average_commission(self, start_date, end_date):
        transactions = self.user_transactions_qs.filter(
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            # transaction_type__in=[TransactionType.COMMISSION],
        )
        total_commission = transactions.aggregate(total=Sum("commission"))[
            "total"
        ] or Decimal("0")

        count = transactions.count()
        if count == 0:
            return Decimal("0")

        return total_commission / count

    def _get_average_commission_all_time(self):
        transactions = self.user_transactions_qs.filter(
            # transaction_type__in=["TRANSFER", "WITHDRAWAL"]
        )
        total_commission = transactions.aggregate(total=Sum("commission"))[
            "total"
        ] or Decimal("0")

        count = transactions.count()
        if count == 0:
            return Decimal("0")

        return total_commission / count


class AverageTransactionStatsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        query_filter = request.query_params.get("filter", "today")

        if company_id is None:
            return Response(
                {"error": "Company ID is required"}, status=status.HTTP_400_BAD_REQUEST
            )
        company = Company.objects.filter(id=company_id).last()
        self.companies_count = Company.objects.aggregate(
            total_count=Count("id"))["total_count"] or 1

        try:
            self.user_transactions_qs = Transaction.objects.filter(
                user=company.user,
                status=TransactionStatus.SUCCESSFUL,
            )
        except Exception as e:
            return Response(
                {"error": f"Error fetching transactions: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        filter_dates = get_filter_dates()
        today = filter_dates.get("today")
        yesterday = filter_dates.get("yesterday")
        this_week_start = filter_dates.get("this_week_start")
        last_week_start = filter_dates.get("last_week_start")
        last_week_end = filter_dates.get("last_week_end")

        filter_start_date, filter_end_date = get_filter_date_values(
            query_filter=query_filter
            )

        # Get count values
        count_today = self._get_transaction_count(start_date=today, end_date=today)
        count_yesterday = self._get_transaction_count(
            start_date=yesterday, end_date=yesterday
        )
        count_this_week = self._get_transaction_count(
            start_date=this_week_start, end_date=today
        )
        count_last_week = self._get_transaction_count(
            start_date=last_week_start, end_date=last_week_end
        )

        total_count = self._get_transaction_count(
            start_date=filter_start_date, end_date=filter_end_date
        )

        # Get amount values
        amount_today = self._get_average_transaction_amount(
            start_date=today, end_date=today
        )
        amount_yesterday = self._get_average_transaction_amount(
            start_date=yesterday, end_date=yesterday
        )
        amount_this_week = self._get_average_transaction_amount(
            start_date=this_week_start, end_date=today
        )
        amount_last_week = self._get_average_transaction_amount(
            start_date=last_week_start, end_date=last_week_end
        )
        total_amount = self._get_average_transaction_amount(
            start_date=filter_start_date, end_date=filter_end_date
        )

        data = {
            "average_transaction_count": {
                "total_count": total_count,
                "previous_value": count_yesterday,
                "today": count_today,
                "yesterday": count_yesterday,
                "this_week": count_this_week,
                "last_week": count_last_week,
                "percent_change": self._calculate_percent_change(
                    count_today, count_yesterday
                ),
                "change_direction": self._get_change_direction(
                    count_today, count_yesterday
                ),
            },
            "average_transaction_amount": {
                "total_amount": total_amount,
                "previous_value": amount_yesterday,
                "today": amount_today,
                "yesterday": amount_yesterday,
                "this_week": amount_this_week,
                "last_week": amount_last_week,
                "percent_change": self._calculate_percent_change(
                    amount_today, amount_yesterday
                ),
                "change_direction": self._get_change_direction(
                    amount_today, amount_yesterday
                ),
            },
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_transaction_count(self, start_date, end_date):
        return self.user_transactions_qs.filter(
            date_created__date__gte=start_date, date_created__date__lte=end_date
        ).count() / self.companies_count

    def _get_average_transaction_amount(self, start_date, end_date):
        transactions = self.user_transactions_qs.filter(
            date_created__date__gte=start_date, date_created__date__lte=end_date
        )
        total_amount = transactions.aggregate(total=Avg("amount"))["total"] or Decimal(
            "0"
        )

        return round(total_amount, 2)

    def _calculate_percent_change(self, current_value, previous_value):
        if previous_value == 0:
            return 0
        change = ((float(current_value) - previous_value) / previous_value) * 100
        return round(change, 2)

    def _get_change_direction(self, current_value, previous_value):
        if current_value > previous_value:
            return "up"
        elif current_value < previous_value:
            return "down"
        return "same"


class SalesStatsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        query_filter = request.query_params.get("filter")

        try:
            company = Company.objects.filter(id=company_id).last()
            self.sales_transactions_qs = SalesTransaction.objects.filter(
                company=company,
                status=TransactionStatus.SUCCESSFUL,
            )

            self.sales_items_qs = SalesTransactionItem.objects.filter(
                company=company,
                refunded=False,
            )
        except Exception as e:
            return Response(
                {"error": f"Error fetching transactions: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        filter_dates = get_filter_dates()
        today = filter_dates.get("today")
        yesterday = filter_dates.get("yesterday")
        this_week_start = filter_dates.get("this_week_start")
        last_week_start = filter_dates.get("last_week_start")
        last_week_end = filter_dates.get("last_week_end")
        this_month_start = filter_dates.get("this_month_start")
        last_month_end = filter_dates.get("last_month_end")
        last_month_start = filter_dates.get("last_month_start")

        filter_start_date, filter_end_date = get_filter_date_values(
            query_filter=query_filter
            )

        data = {
            "total_sales": {
                "total_amount": self._get_total_sales(
                    start_date=filter_start_date, end_date=filter_end_date
                ),
                "previous_value": 0.00,
                "today": self._get_total_sales(start_date=today),
                "this_week": self._get_total_sales(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_total_sales(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_total_sales(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_total_sales(
                    start_date=last_month_start, end_date=last_month_end
                ),
            },
            "gross_sales": {
                "total_amount": self._get_gross_sales(
                    start_date=filter_start_date, end_date=filter_end_date
                ),
                "previous_value": 0.00,
                "today": self._get_gross_sales(start_date=today),
                "this_week": self._get_gross_sales(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_gross_sales(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_gross_sales(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_gross_sales(
                    start_date=last_month_start, end_date=last_month_end
                ),
            },
            "sales_count": {
                "total_count": self._get_sales_count(
                    start_date=filter_start_date, end_date=filter_end_date
                ),
                "previous_value": 0.00,
                "today": self._get_sales_count(start_date=today),
                "this_week": self._get_sales_count(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_sales_count(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_sales_count(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_sales_count(
                    start_date=last_month_start, end_date=last_month_end
                ),
            },
            "average_sales": {
                "total_amount": self._get_average_sales(
                    start_date=filter_start_date, end_date=filter_end_date
                ),
                "previous_value": 0.00,
                "today": self._get_average_sales(start_date=today),
                "this_week": self._get_average_sales(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_average_sales(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_average_sales(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_average_sales(
                    start_date=last_month_start, end_date=last_month_end
                ),
            },
            "total_products_sold": {
                "total_count": self._get_total_products_sold(
                    start_date=filter_start_date, end_date=filter_end_date
                ),
                "previous_value": 0.00,
                "today": self._get_total_products_sold(start_date=today),
                "this_week": self._get_total_products_sold(
                    start_date=this_week_start, end_date=today
                ),
                "last_week": self._get_total_products_sold(
                    start_date=last_week_start, end_date=last_week_end
                ),
                "this_month": self._get_total_products_sold(
                    start_date=this_month_start, end_date=today
                ),
                "last_month": self._get_total_products_sold(
                    start_date=last_month_start, end_date=last_month_end
                ),
            },
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_total_sales(self, start_date, end_date=None):
        if end_date is None:
            end_date = start_date
        return self.sales_transactions_qs.filter(
            created_at__date__gte=start_date, created_at__date__lte=end_date
        ).aggregate(total=Sum("amount_paid"))["total"] or Decimal("0")

    def _get_gross_sales(self, start_date, end_date=None):
        if end_date is None:
            end_date = start_date
        return self.sales_transactions_qs.filter(
            created_at__date__gte=start_date, created_at__date__lte=end_date
        ).aggregate(total=Sum("amount_paid"))[
            "total"
        ] or Decimal(
            "0"
        )

    def _get_sales_count(self, start_date, end_date=None):
        if end_date is None:
            end_date = start_date
        return self.sales_transactions_qs.filter(
            created_at__date__gte=start_date, created_at__date__lte=end_date
        ).count()

    def _get_average_sales(self, start_date, end_date=None):
        if end_date is None:
            end_date = start_date
        transactions = self.sales_transactions_qs.filter(
            created_at__date__gte=start_date, created_at__date__lte=end_date
        )

        total = transactions.aggregate(total=Avg("amount_paid"))[
            "total"
        ] or Decimal("0")

        return round(total, 2)

    def _get_total_products_sold(self, start_date, end_date=None):
        if end_date is None:
            end_date = start_date
        return (
            self.sales_items_qs.filter(
                created_at__date__gte=start_date,
                created_at__date__lte=end_date,
            ).count()
        )


class TransactionLineChartView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get current date
        current_date = timezone.now().date()
        company_id = request.query_params.get("company_id")
        query_filter = request.query_params.get("filter")

        if company_id is None:
            return Response(
                {"error": "Company ID is required"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        company = Company.objects.filter(id=company_id).last()

        filter_start_date, filter_end_date = get_filter_date_values(
            query_filter=query_filter
            )

        try:
            self.user_transactions_qs = Transaction.objects.filter(
                user=company.user,
                status=TransactionStatus.SUCCESSFUL,
                date_created__date__gte=filter_start_date,
                date_created__date__lte=filter_end_date,
            )
        except Exception as e:
            return Response(
                {"error": f"Error fetching transactions: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get date from 12 months ago
        start_date = (current_date - timedelta(days=365)).replace(day=1)

        # Get monthly inflow data
        inflow_data = self._get_monthly_transactions(
            start_date,
            current_date,
            transaction_type=TransactionType.DEPOSIT,
        )

        # Get monthly transfer data
        transfer_data = self._get_monthly_transactions(
            start_date,
            current_date,
            transaction_type=TransactionType.BANK_TRANSFER,
        )

        # Generate month labels
        month_labels = self._generate_month_labels(start_date, current_date)

        # Calculate total amounts
        total_inflow = self._get_total_amount(
            start_date,
            current_date,
            TransactionType.DEPOSIT,
        )

        total_transfer = self._get_total_amount(
            start_date,
            current_date,
            TransactionType.BANK_TRANSFER,
        )

        data = {
            "labels": month_labels,
            "inflow_data": inflow_data,
            "transfer_data": transfer_data,
            "total_inflow_amount": total_inflow,
            "total_transfer_amount": total_transfer,
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_monthly_transactions(self, start_date, end_date, transaction_type):
        if transaction_type == TransactionType.BANK_TRANSFER:
            monthly_data = (
                self.user_transactions_qs.filter(
                    date_created__date__gte=start_date,
                    date_created__date__lte=end_date,
                    payout_type__in=[
                        AccountType.SALES, AccountType.COMPANY_SPEND_MGMT, AccountType.PAYROLL
                        ]
                )
                .annotate(month=TruncMonth("date_created"))
                .values("month")
                .annotate(total=Sum("amount"))
                .order_by("month")
            )
        else:
            monthly_data = (
                self.user_transactions_qs.filter(
                    date_created__date__gte=start_date,
                    date_created__date__lte=end_date,
                    transaction_type=transaction_type,
                )
                .annotate(month=TruncMonth("date_created"))
                .values("month")
                .annotate(total=Sum("amount"))
                .order_by("month")
            )

        # Create a dictionary of month: amount
        data_dict = {
            item["month"].strftime("%Y-%m"): float(item["total"])
            for item in monthly_data
        }

        # Fill in missing months with 0
        filled_data = []
        current = start_date
        while current <= end_date:
            month_key = current.strftime("%Y-%m")
            filled_data.append(data_dict.get(month_key, 0))
            # Move to next month
            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)

        return filled_data

    def _generate_month_labels(self, start_date, end_date):
        labels = []
        current = start_date

        while current <= end_date:
            labels.append(current.strftime("%b %Y"))
            # Move to next month
            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)

        return labels

    def _get_total_amount(self, start_date, end_date, transaction_type):
        if transaction_type == TransactionType.BANK_TRANSFER:
            return self.user_transactions_qs.filter(
                date_created__date__gte=start_date,
                date_created__date__lte=end_date,
                payout_type__in=[
                AccountType.SALES, AccountType.COMPANY_SPEND_MGMT, AccountType.PAYROLL
                ],
            ).aggregate(total=Sum("amount"))["total"] or Decimal("0")

        return self.user_transactions_qs.filter(
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            transaction_type=transaction_type,
        ).aggregate(total=Sum("amount"))["total"] or Decimal("0")


class TransactionComparativeChartsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    TRANSACTION_TYPES = [
        "TRANSFER",
        "DATA_PURCHASE",
        "AIRTIME_PURCHASE",
        "UTILITIES",
        "LOTTO_GAMES",
    ]

    def get(self, request):
        today = timezone.now().date()
        company_id = request.query_params.get("company_id")
        query_filter = request.query_params.get("filter")

        filter_start_date, filter_end_date = get_filter_date_values(
            query_filter=query_filter
            )

        if company_id is None:
            return Response(
                {"error": "Company ID is required"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        company = Company.objects.get(id=company_id)

        try:
            self.user_transactions_qs = Transaction.objects.filter(
                user=company.user,
                status=TransactionStatus.SUCCESSFUL,
                date_created__date__gte=filter_start_date,
                date_created__date__lte=filter_end_date,
            )
        except Exception as e:
            return Response(
                {"error": f"Error fetching transactions: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        count_data = self._get_transaction_counts()
        amount_data = self._get_transaction_amounts()

        data = {
            "labels": self.TRANSACTION_TYPES,
            "count_data": count_data,
            "amount_data": amount_data,
            "total_count": sum(count_data),
            "total_amount": sum(amount_data),
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_transaction_counts(self):
        counts = []
        for transaction_type in self.TRANSACTION_TYPES:
            if transaction_type == "TRANSFER":
                count = self.user_transactions_qs.filter(
                    payout_type__in=[AccountType.SALES, AccountType.COMPANY_SPEND_MGMT],
                ).count()
            else:
                count = self.user_transactions_qs.filter(
                    transaction_type=transaction_type
                ).count()
            counts.append(count)
        return counts

    def _get_transaction_amounts(self):
        amounts = []
        for transaction_type in self.TRANSACTION_TYPES:
            if transaction_type == "TRANSFER":
                amount = self.user_transactions_qs.filter(
                    payout_type__in=[AccountType.SALES, AccountType.COMPANY_SPEND_MGMT],
                ).aggregate(total=Sum("amount"))["total"] or Decimal("0")
            else:
                amount = self.user_transactions_qs.filter(
                    transaction_type=transaction_type
                ).aggregate(total=Sum("amount"))["total"] or Decimal("0")
            amounts.append(float(amount))
        return amounts


class TransactionStatusLineChartView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get current date
        current_date = timezone.now().date()
        company_id = request.query_params.get("company_id")

        if company_id is None:
            return Response(
                {"error": "Company ID is required"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        company = Company.objects.get(id=company_id)

        self.user_transactions_qs = Transaction.objects.filter(
                user=company.user,
                status=TransactionStatus.SUCCESSFUL,
            )

        # Get date from 12 months ago
        start_date = (current_date - timedelta(days=365)).replace(day=1)

        # Get monthly successful transactions data
        successful_data = self._get_monthly_transactions(
            start_date, current_date, status=TransactionStatus.SUCCESSFUL
        )

        # Get monthly failed transactions data
        failed_data = self._get_monthly_transactions(
            start_date, current_date, status=TransactionStatus.FAILED
        )

        # Generate month labels
        month_labels = self._generate_month_labels(start_date, current_date)

        # Calculate total amounts
        total_successful = self._get_total_count(start_date, current_date, TransactionStatus.SUCCESSFUL)
        total_failed = self._get_total_count(start_date, current_date, TransactionStatus.FAILED)

        data = {
            "labels": month_labels,
            "successful_data": successful_data,
            "failed_data": failed_data,
            "total_successful_transactions": total_successful,
            "total_failed_transactions": total_failed,
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_monthly_transactions(self, start_date, end_date, status):
        monthly_data = (
            self.user_transactions_qs.filter(
                date_created__date__gte=start_date,
                date_created__date__lte=end_date,
                status=status,
            )
            .annotate(month=TruncMonth("date_created"))
            .values("month")
            .annotate(count=Count("id"))
            .order_by("month")
        )

        # Create a dictionary of month: count
        data_dict = {
            item["month"].strftime("%Y-%m"): item["count"] for item in monthly_data
        }

        # Fill in missing months with 0
        filled_data = []
        current = start_date
        while current <= end_date:
            month_key = current.strftime("%Y-%m")
            filled_data.append(data_dict.get(month_key, 0))
            # Move to next month
            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)

        return filled_data

    def _get_total_count(self, start_date, end_date, status):
        return self.user_transactions_qs.filter(
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            status=status,
        ).count()

    def _generate_month_labels(self, start_date, end_date):
        labels = []
        current = start_date

        while current <= end_date:
            labels.append(current.strftime("%b %Y"))
            # Move to next month
            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)

        return labels


class SessionByTrafficSourceView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        today = timezone.now().date()

        data = {
            "pos_terminals": {
                # "unique_visitors": self._get_unique_visitors(today, "POS_TERMINAL"),
                # "session": self._get_sessions(today, "POS_TERMINAL"),
                "unique_visitors": 0,
                "session": 0,
            },
            "direct": {
                # "unique_visitors": self._get_unique_visitors(today, "DIRECT"),
                # "session": self._get_sessions(today, "DIRECT"),
                "unique_visitors": 0,
                "session": 0,
            },
            "website": {
                # "unique_visitors": self._get_unique_visitors(today, "WEBSITE"),
                # "session": self._get_sessions(today, "WEBSITE"),
                "unique_visitors": 0,
                "session": 3,
            },
            "web_pos": {
                # "unique_visitors": self._get_unique_visitors(today, "WEB_POS"),
                # "session": self._get_sessions(today, "WEB_POS"),
                "unique_visitors": 0,
                "session": 0,
            },
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_unique_visitors(self, date, source):
        return (
            UserSession.objects.filter(created_at__date=date, source=source)
            .values("user")
            .distinct()
            .count()
        )

    def _get_sessions(self, date, source):
        return UserSession.objects.filter(created_at__date=date, source=source).count()


class TransactionStatusMetricsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        company = Company.objects.filter(id=company_id).last()
        query_filter = request.query_params.get("filter")

        if company_id:
            self.transactions_qs = Transaction.objects.filter(user=company.user)
        else:
            return Response(
                {"error": "Company ID is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        filter_dates = get_filter_dates()
        today = filter_dates.get("today")
        yesterday = filter_dates.get("yesterday")
        this_week_start = filter_dates.get("this_week_start")
        last_week_start = filter_dates.get("last_week_start")
        last_week_end = filter_dates.get("last_week_end")
        this_month_start = filter_dates.get("this_month_start")
        last_month_end = filter_dates.get("last_month_end")
        last_month_start = filter_dates.get("last_month_start")

        filter_start_date, filter_end_date = get_filter_date_values(
            query_filter=query_filter
            )

        data = {
            "successful_transactions": {
                "total_count": self._get_transaction_count(
                    filter_start_date, filter_end_date, TransactionStatus.SUCCESSFUL
                ),
                "today": self._get_transaction_count(
                    today, today, TransactionStatus.SUCCESSFUL
                ),
                "yesterday": self._get_transaction_count(
                    yesterday, yesterday, TransactionStatus.SUCCESSFUL
                ),
                "this_week": self._get_transaction_count(
                    this_week_start, today, TransactionStatus.SUCCESSFUL
                ),
                "last_week": self._get_transaction_count(
                    last_week_start, last_week_end, TransactionStatus.SUCCESSFUL
                ),
                "this_month": self._get_transaction_count(
                    this_month_start, today, TransactionStatus.SUCCESSFUL
                ),
                "last_month": self._get_transaction_count(
                    last_month_start, last_month_end, TransactionStatus.SUCCESSFUL
                ),
                "all_time": self._get_transaction_count(
                    None, None, TransactionStatus.SUCCESSFUL
                ),
            },
            "failed_transactions": {
                "total_count": self._get_transaction_count(
                    filter_start_date, filter_end_date, TransactionStatus.FAILED
                ),
                "today": self._get_transaction_count(
                    today, today, TransactionStatus.FAILED
                ),
                "yesterday": self._get_transaction_count(
                    yesterday, yesterday, TransactionStatus.FAILED
                ),
                "this_week": self._get_transaction_count(
                    this_week_start, today, TransactionStatus.FAILED
                ),
                "last_week": self._get_transaction_count(
                    last_week_start, last_week_end, TransactionStatus.FAILED
                ),
                "this_month": self._get_transaction_count(
                    this_month_start, today, TransactionStatus.FAILED
                ),
                "last_month": self._get_transaction_count(
                    last_month_start, last_month_end, TransactionStatus.FAILED
                ),
                "all_time": self._get_transaction_count(
                    None, None, TransactionStatus.FAILED
                ),
            },
            "average_commission_per_transaction": {
                "total_amount": self._get_average_commission_per_transaction(
                    start_date=filter_start_date, end_date=filter_end_date
                )
            },
        }

        return Response(data, status=status.HTTP_200_OK)

    def _get_transaction_count(self, start_date, end_date, status):
        queryset = Transaction.objects.filter(status=status)

        if start_date:
            queryset = queryset.filter(date_created__date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date_created__date__lte=end_date)

        return queryset.count()

    def _get_average_commission_per_transaction(self, start_date, end_date):
        transactions = Transaction.objects.filter(
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            # transaction_type__in=[TransactionType.COMMISSION],
            status=TransactionStatus.SUCCESSFUL,
        )
        total_commission = transactions.aggregate(total=Sum("commission"))[
            "total"
        ] or Decimal("0")

        count = transactions.count()
        if count == 0:
            return Decimal("0")

        return total_commission / count
