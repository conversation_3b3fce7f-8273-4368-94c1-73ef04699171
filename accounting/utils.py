from django.core.exceptions import ValidationError
from django.db.transaction import atomic
from accounting.constants import ACCOUNT_DATA
from requisition.models import Company
from .models import Account, JournalEntry, JournalLine
from account.models import Transaction
import random
from django.db.models import Sum, F, Q
from decimal import Decimal
from .serializers import JournalLineSerializer
from django.conf import settings

logger = settings.LOGGER

MAPPINGS = {
    # HR Module Integration
    "hr": {
        "payroll_expenses": {
            "debit": ["Salaries and Employee Wages"],
            "credit": ["Cash/Bank", "Employee Reimbursements"],
            "accounts_involved": [
                "Salaries and Employee Wages",
                "Cash/Bank",
                "Employee Reimbursements",
            ],
        },
        "regulatory_payments": {
            "debit": ["Salaries and Employee Wages"],
            "credit": ["Accounts Payable"],
            "accounts_involved": [
                "Salaries and Employee Wages",
                "Accounts Payable",
                "Cash/Bank",
            ],
            "taxes": {
                "debit": ["Other Expenses"],
                "credit": ["Tax Payable"],
                "accounts_involved": [
                    "Other Expenses",
                    "Tax Payable",
                    "Cash/Bank",
                ],
            },
        },
        "employee_loans": {
            "debit": ["Employee Advance"],
            "credit": ["Cash/Bank"],
            "accounts_involved": ["Employee Advance", "Cash/Bank"],
        },
        "company_payroll_wallets": {
            "debit": ["Cash/Bank"],
            "credit": ["Accounts Receivable"],
            "accounts_involved": ["Cash/Bank", "Accounts Receivable"],
        },
        "regulatory_payments_pensions": {
            "debit": ["Employee Benefits Expense"],
            "credit": ["Accounts Payable"],
            "accounts_involved": [
                "Employee Benefits Expense",
                "Accounts Payable",
                "Cash/Bank",
            ],
        },
        "regulatory_payments_hmo": {
            "debit": ["Salaries and Employee Wages"],
            "credit": ["Employee Benefits Expense"],
            "accounts_involved": [
                "Salaries and Employee Wages",
                "Employee Benefits Expense",
                "Cash/Bank",
            ],
        },
    },
    # Stocks & Inventory Module Integration
    "stocks": {
        "inventory_purchases": {
            "debit": ["Inventory"],
            "credit": ["Accounts Payable"],
            "accounts_involved": ["Inventory", "Accounts Payable", "Cash/Bank"],
        },
        "inventory_sales": {
            "debit": ["Cost of Goods Sold"],
            "credit": ["Inventory Asset"],
            "accounts_involved": [
                "Cost of Goods Sold",
                "Inventory Asset",
                "Accounts Receivable",
                "Sales Revenue",
            ],
        },
        "inventory_adjustments": {
            "debit": ["Repairs and Maintenance"],
            "credit": ["Inventory Asset"],
            "accounts_involved": ["Repairs and Maintenance", "Inventory Asset"],
        },
    },
    # Spend Management & Procurement Module Integration
    "spend_management": {
        "expenses_and_requisitions": {
            "debit": ["Other Expenses"],
            "credit": ["Accounts Payable"],
            "accounts_involved": [
                "Other Expenses",
                "Accounts Payable",
                "Cash/Bank",
            ],
        },
        "supplier_management": {
            "debit": ["Other Expenses"],
            "credit": ["Accounts Payable"],
            "accounts_involved": [
                "Expense Accounts or Asset Accounts",
                "Accounts Payable",
                "Cash/Bank",
            ],
        },
        "invoices_and_bills": {
            "debit": ["Other Expenses"],
            "credit": ["Accounts Payable"],
            "accounts_involved": [
                "Expense Accounts or Asset Accounts",
                "Accounts Payable",
            ],
        },
        "credit_notes": {
            "debit": ["Accounts Payable"],
            "credit": ["Other Expenses"],
            "accounts_involved": [
                "Accounts Payable",
                "Other Expenses",
            ],
        },
        "pending_item_deliveries": {
            "debit": ["Other Expenses"],
            "credit": ["Accounts Payable"],
            "accounts_involved": ["Other Expenses", "Accounts Payable"],
        },
    },
    # Sales Module Integration
    "sales": {
        "sales_transactions": {
            "debit": ["Accounts Receivable", "Cash/Bank"],
            "credit": ["Sales"],
            "accounts_involved": ["Accounts Receivable", "Cash/Bank", "Sales"],
        },
        "inventory": {
            "debit": ["Cost of Goods Sold"],
            "credit": ["Inventory Asset"],
            "accounts_involved": [
                "Cost of Goods Sold",
                "Inventory Asset",
            ],
        },
        "refunds": {
            "debit": ["Cost of Goods Sold"],
            "credit": ["Cash/Bank"],
            "accounts_involved": [
                "Cost of Goods Sold",
                "Cash/Bank",
                "Accounts Receivable",
                "Inventory",
                "Cost of Goods Sold",
            ],
        },
        "credits": {
            "debit": ["Cost of Goods Sold"],
            "credit": ["Accounts Receivable"],
            "accounts_involved": [
                "Cost of Goods Sold",
                "Accounts Receivable",
            ],
        },
        "sales_by_transfer_cash_pos": {
            "cash_sales": {
                "debit": ["Cash/Bank"],
                "credit": ["Sales"],
                "accounts_involved": ["Cash/Bank", "Sales"],
            },
            "sales_by_transfer": {
                "debit": ["Accounts Receivable"],
                "credit": ["Sales"],
                "accounts_involved": ["Accounts Receivable", "Sales"],
            },
            "pos_sales": {
                "debit": ["Cash/Bank"],
                "credit": ["Sales"],
                "accounts_involved": ["Cash/Bank", "Sales"],
            },
        },
    },
}


# Utility to create a journal entry and associated lines
@atomic
def create_journal_entry(transaction: Transaction, mapping):
    """
    Creates a journal entry based on the categorization of the transaction.

    Args:
        transaction: The transaction object.
        mapping: The account mapping for the transaction.

    Returns:
        JournalEntry: The created journal entry object.
    """
    try:
        company = Company.objects.get(id=transaction.company_id)
    except Company.DoesNotExist:
        raise ValidationError(
            detail={"error": "company data not recorded properly for this transaction"}
        )

    # Create the journal entry
    journal_entry = JournalEntry.objects.create(
        journal_number=generate_account_code(8),
        references=transaction.transaction_ref,
        date=transaction.date_created.date(),
        journal_type="CASH",
        company=company,
    )

    # Add journal entry lines for each account involved in the transaction
    for account_name in mapping.get("debit", []):
        try:
            account = Account.objects.get(
                name=account_name, is_active=True, paybox_default=True
            )
            JournalLine.objects.create(
                journal_entry=journal_entry,
                account=account,
                debit_amount=transaction.amount,
                credit_amount=transaction.amount,
                bal_after=transaction.balance_before,
                bal_before=transaction.balance_after,
            )
        except Account.DoesNotExist:
            logger.info(f"{account_name} does not exist in Account")

    for account_name in mapping.get("credit", []):
        try:
            account = Account.objects.get(
                name=account_name, is_active=True, paybox_default=True
            )
            JournalLine.objects.create(
                journal_entry=journal_entry,
                account=account,
                debit_amount=transaction.amount,
                credit_amount=transaction.amount,
                bal_after=transaction.balance_before,
                bal_before=transaction.balance_after,
            )
        except Account.DoesNotExist:
            logger.info(f"{account_name} does not exist in Account")
    return journal_entry


# Transaction categorization utility
def categorize_transaction(
    transaction: Transaction, source, transaction_type, sub_type=None
):
    """
    Categorizes a transaction based on the provided source, type, and optional sub-type, and maps it to an Account model instance.

    Args:
        transaction: The transaction object containing transaction details.
        source (str): The source of the transaction (e.g., 'HR Module', 'Sales Module').
        transaction_type (str): The type of transaction (e.g., 'Payroll Expenses', 'Sales Transactions').
        sub_type (str, optional): The sub-type of the transaction, if applicable (e.g., 'Pensions').

    Returns:
        dict: Updated transaction object with the mapped 'account' instance.
    """
    source_map = MAPPINGS.get(source.lower(), None)

    if not source_map:
        raise ValidationError(
            detail={"error": "module not configured for automatated journaling"}
        )

    transaction_type = transaction_type.lower()

    mapping = source_map.get(transaction_type, None)

    if not mapping:
        raise ValidationError(
            f"Unknown transaction type '{transaction_type}' under {source} module."
        )

    journal_entry = create_journal_entry(transaction, mapping)

    transaction.jounral = journal_entry
    transaction.save()

    return transaction


def generate_account_code(max_length=6):
    """
    Generate a unique numeric account code.

    Parameters:
        max_length (int): Maximum length of the generated code (default is 10).

    Returns:
        str: A unique numeric account code.
    """
    # Generate a unique numeric code
    existing_codes = Account.objects.values_list("account_code", flat=True)
    new_code = None

    while True:
        code = random.randint(10 ** (max_length - 1), 10**max_length - 1)
        new_code = str(code)
        if new_code not in existing_codes:
            break

    return new_code


def generate_journal_number(max_length=8):
    """
    Generate a unique 8-digit journal number.

    Parameters:
        max_length (int): Length of the generated journal number (fixed at 8 digits).

    Returns:
        str: A unique 8-digit journal number.
    """
    # Generate a unique 8-digit numeric code
    existing_codes = JournalEntry.objects.values_list("journal_number", flat=True)
    new_code = None

    while True:
        code = random.randint(********, ********)  # Generate 8 digit number
        new_code = str(code)
        if new_code not in existing_codes:
            break

    return new_code


# class ProfitAndLossReportGenerator:
#     def __init__(self, company_id, start_date=None, end_date=None):
#         self.company_id = company_id
#         self.start_date = start_date
#         self.end_date = end_date
#         self.date_filter = self.get_date_filter()

#     def get_date_filter(self):
#         if self.start_date and self.end_date:
#             return Q(journal_entry__date__range=[self.start_date, self.end_date])
#         return Q()

#     def get_filtered_total(self, statement_category, account_type, filter_by=None):
#         """
#         Generalized method to get total based on statement_category, account_type, and any additional filters.
#         Returns both the total and the journal lines that make up that total.
#         """
#         filters = Q(account__statement_category=statement_category) & Q(
#             account__account_type__name=account_type
#         )

#         if filter_by:
#             filters &= filter_by

#         credits = JournalLine.objects.filter(
#             Q(account__account_type__standard_balance="Credit")
#             & Q(journal_entry__company_id=self.company_id)
#             & self.date_filter
#             & filters
#         )
#         debits = JournalLine.objects.filter(
#             Q(account__account_type__standard_balance="Debit")
#             & Q(journal_entry__company_id=self.company_id)
#             & self.date_filter
#             & filters
#         )

#         total_credits = credits.aggregate(total=Sum(F("credit_amount")))["total"] or 0
#         total_debits = debits.aggregate(total=Sum(F("debit_amount")))["total"] or 0

#         return round(total_credits - total_debits, 2), list(credits) + list(debits)

#     def get_operating_income(self):
#         total, journal_lines = self.get_filtered_total(
#             statement_category="Revenue", account_type="Income"
#         )
#         return total, journal_lines

#     def get_cost_of_goods_sold(self):
#         total, journal_lines = self.get_filtered_total(
#             statement_category="Cost of Goods Sold", account_type="Cost of Goods Sold"
#         )
#         return total, journal_lines

#     def get_operating_expenses(self):
#         total, journal_lines = self.get_filtered_total(
#             statement_category="Operating Expenses", account_type="Expense"
#         )
#         return total, journal_lines

#     def get_non_operating_income(self):
#         total, journal_lines = self.get_filtered_total(
#             statement_category="Other Income", account_type="Income"
#         )
#         return total, journal_lines

#     def get_non_operating_expenses(self):
#         total, journal_lines = self.get_filtered_total(
#             statement_category="Other Expenses", account_type="Expense"
#         )
#         return total, journal_lines

#     def generate_report(self):
#         # Fetch all components
#         operating_income, operating_income_lines = self.get_operating_income()
#         cogs, cogs_lines = self.get_cost_of_goods_sold()
#         gross_profit = operating_income - cogs
#         operating_expenses, operating_expense_lines = self.get_operating_expenses()
#         operating_profit = gross_profit - operating_expenses
#         non_operating_income, non_operating_income_lines = (
#             self.get_non_operating_income()
#         )
#         non_operating_expenses, non_operating_expense_lines = (
#             self.get_non_operating_expenses()
#         )
#         net_profit = operating_profit + non_operating_income - non_operating_expenses

#         return {
#             "Operating Income": {
#                 "total": operating_income,
#                 "journal_lines": (
#                     JournalLineSerializer(operating_income_lines, many=True).data
#                     if operating_income_lines
#                     else []
#                 ),
#             },
#             "Cost of Goods Sold": {
#                 "total": cogs,
#                 "journal_lines": (
#                     JournalLineSerializer(cogs_lines, many=True).data
#                     if cogs_lines
#                     else []
#                 ),
#             },
#             "Gross Profit": gross_profit,
#             "Operating Expenses": {
#                 "total": operating_expenses,
#                 "journal_lines": (
#                     JournalLineSerializer(operating_expense_lines, many=True).data
#                     if operating_expense_lines
#                     else []
#                 ),
#             },
#             "Operating Profit": operating_profit,
#             "Non-Operating Income": {
#                 "total": non_operating_income,
#                 "journal_lines": (
#                     JournalLineSerializer(non_operating_income_lines, many=True).data
#                     if non_operating_income_lines
#                     else []
#                 ),
#             },
#             "Non-Operating Expenses": {
#                 "total": non_operating_expenses,
#                 "journal_lines": (
#                     JournalLineSerializer(non_operating_expense_lines, many=True).data
#                     if non_operating_expense_lines
#                     else []
#                 ),
#             },
#             "Net Profit/Loss": net_profit,
#         }


class ProfitAndLossReportGenerator:
    def __init__(self, company_id, start_date=None, end_date=None):
        self.company_id = company_id
        self.start_date = start_date
        self.end_date = end_date
        self.date_filter = self.get_date_filter()

    def get_date_filter(self):
        if self.start_date and self.end_date:
            return Q(journal_entry__date__range=[self.start_date, self.end_date])
        return Q()

    def get_filtered_total(self, statement_category, account_type, filter_by=None):
        # Only use journals where is_draft=False
        filters = Q(account__statement_category=statement_category) & Q(
            account__account_type__name=account_type,
            account__statement_type="Income Statement",
        ) & Q(journal_entry__is_draft=False)

        if filter_by:
            filters &= filter_by

        credits = JournalLine.objects.filter(
            Q(account__account_type__standard_balance="Credit")
            & Q(journal_entry__company_id=self.company_id)
            & self.date_filter
            & filters
        )
        debits = JournalLine.objects.filter(
            Q(account__account_type__standard_balance="Debit")
            & Q(journal_entry__company_id=self.company_id)
            & self.date_filter
            & filters
        )

        total_credits = credits.aggregate(total=Sum(F("credit_amount")))["total"] or 0
        total_debits = debits.aggregate(total=Sum(F("debit_amount")))["total"] or 0

        return round(total_credits - total_debits, 2), list(credits) + list(debits)

    def generate_report(self):
        report_data = {}

        # Iterate through each account type in ACCOUNT_DATA
        for account_type, accounts in ACCOUNT_DATA.items():
            if account_type in ["Income", "Expense", "Cost of Goods Sold"]:
                report_data[account_type] = {}
                for account in accounts:
                    statement_category = account["statement_category"]
                    # Get filtered total for each statement category
                    total, journal_lines = self.get_filtered_total(
                        statement_category=statement_category, account_type=account_type
                    )
                    report_data[account_type][statement_category] = {
                        "total": total,
                        "journal_lines": journal_lines,
                    }

        # Calculate totals for Income, COGS, and Expenses
        total_income = sum(
            report_data["Income"][category]["total"]
            for category in report_data["Income"]
        )
        total_cogs = sum(
            report_data["Cost of Goods Sold"][category]["total"]
            for category in report_data["Cost of Goods Sold"]
        )
        total_expenses = sum(
            report_data["Expense"][category]["total"]
            for category in report_data["Expense"]
        )

        gross_profit = total_income - total_cogs
        operating_profit = gross_profit - total_expenses
        net_profit = operating_profit

        return {
            "Income": {
                "total": total_income,
                "journal_lines": [
                    JournalLineSerializer(
                        report_data["Income"][category]["journal_lines"], many=True
                    ).data
                    for category in report_data["Income"]
                ],
            },
            "Cost of Goods Sold": {
                "total": total_cogs,
                "journal_lines": [
                    JournalLineSerializer(
                        report_data["Cost of Goods Sold"][category]["journal_lines"],
                        many=True,
                    ).data
                    for category in report_data["Cost of Goods Sold"]
                ],
            },
            "Gross Profit": gross_profit,
            "Expenses": {
                "total": total_expenses,
                "journal_lines": [
                    JournalLineSerializer(
                        report_data["Expense"][category]["journal_lines"], many=True
                    ).data
                    for category in report_data["Expense"]
                ],
            },
            "Operating Profit": operating_profit,
            "Net Profit/Loss": net_profit,
        }


# class BalanceSheetReportGenerator:
#     def __init__(self, company_id, date=None):
#         self.company_id = company_id
#         self.date = date
#         self.date_filter = self.get_date_filter()

#     def get_date_filter(self):
#         if self.date:
#             return Q(journal_entry__date__lte=self.date)
#         return Q()

#     def get_filtered_data(self, statement_category, include_journal_lines=True):
#         filters = Q(account__statement_category=statement_category) & Q(
#             journal_entry__company_id=self.company_id
#         )

#         credits = (
#             JournalLine.objects.filter(
#                 Q(account__account_type__standard_balance="Credit")
#                 & self.date_filter
#                 & filters
#             ).aggregate(total=Sum("credit_amount"))["total"]
#             or 0
#         )

#         debits = (
#             JournalLine.objects.filter(
#                 Q(account__account_type__standard_balance="Debit")
#                 & self.date_filter
#                 & filters
#             ).aggregate(total=Sum("debit_amount"))["total"]
#             or 0
#         )

#         journal_lines = (
#             JournalLine.objects.filter(self.date_filter & filters)
#             .select_related("journal_entry", "account")
#             .distinct()
#             if include_journal_lines
#             else []
#         )

#         serialized_journal_lines = (
#             JournalLineSerializer(journal_lines, many=True).data
#             if journal_lines
#             else []
#         )

#         return {
#             "total": round(debits - credits, 2),
#             "journal_lines": serialized_journal_lines,
#         }

#     def get_assets(self):
#         current_assets = self.get_filtered_data("Current Assets")
#         non_current_assets = self.get_filtered_data("Non-Current Assets")
#         total_assets = {
#             "total": current_assets["total"] + non_current_assets["total"],
#             "journal_lines": current_assets["journal_lines"]
#             + non_current_assets["journal_lines"],
#         }
#         return total_assets

#     def get_liabilities(self):
#         current_liabilities = self.get_filtered_data("Current Liabilities")
#         non_current_liabilities = self.get_filtered_data("Non-Current Liabilities")
#         total_liabilities = {
#             "total": current_liabilities["total"] + non_current_liabilities["total"],
#             "journal_lines": current_liabilities["journal_lines"]
#             + non_current_liabilities["journal_lines"],
#         }
#         return total_liabilities

#     def get_equity(self):
#         return self.get_filtered_data("Equity", include_journal_lines=False)

#     def generate_report(self):
#         assets = self.get_assets()
#         liabilities = self.get_liabilities()
#         equity = self.get_equity()
#         total_liabilities_and_equity = liabilities["total"] + equity["total"]

#         return {
#             "Assets": assets,
#             "Liabilities": liabilities,
#             "Equity": equity,
#             "Total Liabilities and Equity": total_liabilities_and_equity,
#         }


class BalanceSheetReportGenerator:
    def __init__(self, company_id, start_date=None, end_date=None):
        self.company_id = company_id
        self.start_date = start_date
        self.end_date = end_date
        self.date_filter = self.get_date_filter()

    def get_date_filter(self):
        if self.start_date and self.end_date:
            return Q(journal_entry__date__range=(self.start_date, self.end_date))
        elif self.start_date:
            return Q(journal_entry__date=self.start_date)
        return Q()

    def get_filtered_data(self, statement_category):
        # Only use journals where is_draft=False
        filters = (
            Q(account__statement_category=statement_category)
            & Q(journal_entry__company_id=self.company_id)
            & Q(account__statement_type="Balance Sheet")
            & Q(journal_entry__is_draft=False)
        )  # Added filter for Balance Sheet and is_draft

        credits = (
            JournalLine.objects.filter(
                Q(account__account_type__standard_balance="Credit")
                & self.date_filter
                & filters
            ).aggregate(total=Sum("credit_amount"))["total"]
            or 0
        )

        debits = (
            JournalLine.objects.filter(
                Q(account__account_type__standard_balance="Debit")
                & self.date_filter
                & filters
            ).aggregate(total=Sum("debit_amount"))["total"]
            or 0
        )

        journal_lines = (
            JournalLine.objects.filter(self.date_filter & filters)
            .select_related("journal_entry", "account")
            .distinct()
        )

        serialized_journal_lines = (
            JournalLineSerializer(journal_lines, many=True).data
            if journal_lines
            else []
        )

        return {
            "total": round(debits - credits, 2),
            "journal_lines": serialized_journal_lines,
        }

    def generate_report(self):
        report_data = {}

        # Iterate through each account type in ACCOUNT_DATA
        for account_type, accounts in ACCOUNT_DATA.items():
            if account_type != "Expense":
                report_data[account_type] = {}
                for account in accounts:
                    statement_category = account["statement_category"]
                    filtered_data = self.get_filtered_data(statement_category)
                    report_data[account_type][statement_category] = {
                        "total": filtered_data["total"],
                        "journal_lines": filtered_data["journal_lines"],
                    }

        total_assets = sum(
            report_data["Asset"][category]["total"] for category in report_data["Asset"]
        )
        total_liabilities = sum(
            report_data["Liability"][category]["total"]
            for category in report_data["Liability"]
        )
        total_equity = sum(
            report_data["Equity"][category]["total"]
            for category in report_data["Equity"]
        )
        total_income = sum(
            report_data["Income"][category]["total"]
            for category in report_data["Income"]
        )

        report_data["Total Assets"] = total_assets
        report_data["Total Liabilities"] = total_liabilities
        report_data["Total Equity"] = total_equity
        report_data["Total Income"] = total_income
        report_data["Total Liabilities and Equity"] = total_liabilities + total_equity

        return report_data


class TrialBalanceReportGenerator:
    def __init__(self, company_id, date=None):
        self.company_id = company_id
        self.date = date
        self.date_filter = self.get_date_filter()

    def get_date_filter(self):
        if self.date:
            return Q(journal_entry__date__lte=self.date)
        return Q()

    def generate_report(self):
        # Filter accounts with journal lines (only from non-draft journals)
        accounts_with_journals = Account.objects.filter(
            journalline__journal_entry__company_id=self.company_id,
            journalline__journal_entry__is_draft=False,
        ).distinct()

        report = []

        for account in accounts_with_journals:
            journal_lines = JournalLine.objects.filter(
                Q(account=account) & self.date_filter & Q(journal_entry__is_draft=False)
            ).select_related("journal_entry")

            total_debit = (
                journal_lines.aggregate(total=Sum("debit_amount"))["total"] or 0
            )
            total_credit = (
                journal_lines.aggregate(total=Sum("credit_amount"))["total"] or 0
            )

            if account.account_type.standard_balance == "Debit":
                net_balance = total_debit - total_credit
            else:
                net_balance = total_credit - total_debit

            report.append(
                {
                    "code": account.account_code,
                    "particulars": account.name,
                    "debit": total_debit,
                    "credit": total_credit,
                    "net": net_balance,
                    "journal_lines": JournalLineSerializer(
                        journal_lines, many=True
                    ).data,
                }
            )

        return report
