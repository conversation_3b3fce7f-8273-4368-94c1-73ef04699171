from django.db import models
from core.models import BaseModel
from requisition.models import Company
from stock_inventory.models import Branch
from django.db import models
from django.core.exceptions import ValidationError


class AccountType(BaseModel):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    standard_balance = models.CharField(
        max_length=10,
        choices=[("Debit", "Debit"), ("Credit", "Credit")],
        help_text="Standard balance for the account type.",
        null=True,
    )

    def __str__(self):
        return self.name


class Account(BaseModel):
    STATEMENT_TYPE_CHOICES = [
        ("Balance Sheet", "Balance Sheet"),
        ("Income Statement", "Income Statement"),
    ]

    STATEMENT_CATEGORY_CHOICES = [
        ("Current Assets", "Current Assets"),
        ("Non-Current Assets", "Non-Current Assets"),
        ("Cash and Cash Equivalents", "Cash and Cash Equivalents"),
        ("Current Liabilities", "Current Liabilities"),
        ("Non-Current Liabilities", "Non-Current Liabilities"),
        ("Equity", "Equity"),
        ("Cost of Goods Sold", "Cost of Goods Sold"),
        ("Revenue", "Revenue"),
        ("Operating Expenses", "Operating Expenses"),
        ("Other Income", "Other Income"),
        ("Other Expenses", "Other Expenses"),
    ]

    name = models.CharField(max_length=200, unique=True)
    account_type = models.ForeignKey(AccountType, on_delete=models.CASCADE)
    account_code = models.CharField(max_length=50, unique=True)
    statement_type = models.CharField(
        max_length=50,
        choices=STATEMENT_TYPE_CHOICES,
        null=True,
        help_text="Statement type associated with the account.",
    )
    statement_category = models.CharField(
        max_length=100,
        null=True,
        choices=STATEMENT_CATEGORY_CHOICES,
        help_text="Category under the statement type.",
    )
    description = models.TextField(blank=True, null=True)
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    created_by = models.ForeignKey(
        "core.User", on_delete=models.PROTECT, null=True, blank=True
    )
    paybox_default = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.account_code} - {self.name}"


class JournalEntry(BaseModel):
    date = models.DateField()
    journal_number = models.CharField(max_length=100, unique=True)
    references = models.TextField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    journal_type = models.CharField(
        max_length=50,
        choices=[("CASH", "Cash Basis"), ("ACCURAL", "Accrual Basis")],
        default="CASH",
    )
    transaction_type = models.CharField(max_length=100)
    recorded_by = models.ForeignKey("core.User", on_delete=models.CASCADE, null=True, blank=True)
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, null=True, blank=True)
    is_draft = models.BooleanField(default=False)

    def __str__(self):
        return f"Journal Entry {self.journal_number} - {self.date}"


class JournalLine(BaseModel):
    journal_entry = models.ForeignKey(
        JournalEntry, related_name="lines", on_delete=models.CASCADE, null=True
    )
    account = models.ForeignKey(Account, on_delete=models.CASCADE)
    description = models.TextField()
    debit_amount = models.FloatField()
    credit_amount = models.FloatField()
    bal_before = models.FloatField()
    bal_after = models.FloatField()
    customer = models.CharField(max_length=200, blank=True, null=True)
    tax = models.FloatField(default=0.0)

    def __str__(self):
        return f"Line for {self.journal_entry.journal_number}"
