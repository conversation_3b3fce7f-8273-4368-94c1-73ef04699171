from django.shortcuts import get_object_or_404
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response

from datetime import datetime
from accounting.utils import (
    BalanceSheetReportGenerator,
    ProfitAndLossReportGenerator,
    TrialBalanceReportGenerator,
    generate_account_code,
    generate_journal_number,
)
from requisition.models import Company
from .models import AccountType, Account, JournalEntry, JournalLine
from .serializers import (
    AccountTypeSerializer,
    AccountSerializer,
    JournalEntrySerializer,
    JournalEntryUploadSerializer,
    JournalLineSerializer,
)
from django.db import models
from django.db import transaction
from rest_framework.exceptions import ValidationError, PermissionDenied
from rest_framework.permissions import IsAuthenticated
from core.auth.custom_auth import CustomUserAuthentication
import csv
from rest_framework import generics, views
from decimal import Decimal


class AccountTypeListView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    queryset = AccountType.objects.all()
    serializer_class = AccountTypeSerializer


class AccountViewSet(viewsets.ModelViewSet):
    serializer_class = AccountSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    lookup_field = "id"

    def get_queryset(self):
        user_company = self.request.query_params.get("company")
        return (
            Account.objects.filter(is_active=True)
            .filter(models.Q(company__id=user_company) | models.Q(paybox_default=True))
            .distinct()
            .order_by("-created_at")
        )

    def get_object(self, id):
        return get_object_or_404(Account, id=id)

    def check_account_permission(self, account):
        if account.paybox_default:
            raise PermissionDenied(
                "You do not have permission to perform this action on this account."
            )
        companies = Company.objects.filter(user=self.request.user)
        if account.company not in companies:
            raise PermissionDenied(
                "You do not have permission to perform this action on this account."
            )

    def create(self, request, *args, **kwargs):
        company_id = request.data.get("company")
        if not company_id:
            raise ValidationError("Company ID must be provided.")
        try:
            Company.objects.get(id=company_id)
            request.data["account_code"] = generate_account_code()
        except Company.DoesNotExist:
            raise ValidationError("The specified company does not exist.")

        request.data["paybox_default"] = False
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(created_by=request.user)
        return Response(
            {"message": "Account created successfully.", "data": serializer.data},
            status=status.HTTP_201_CREATED,
        )

    def destroy(self, request, id, *args, **kwargs):
        account = self.get_object(id=id)
        self.check_account_permission(account)
        if account.journalline_set.exists() or account.transaction_set.exists():
            raise ValidationError(
                "Cannot delete account. It is tied to existing journals or transactions."
            )
        self.perform_destroy(account)
        return Response(
            {"message": "Account deleted successfully."},
            status=status.HTTP_204_NO_CONTENT,
        )

    def update(self, request, id, *args, **kwargs):
        account = self.get_object(id=id)
        self.check_account_permission(account)
        serializer = self.get_serializer(account, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"message": "Account updated successfully.", "data": serializer.data},
            status=status.HTTP_200_OK,
        )

    @action(methods=["put"], detail=True)
    def deactivate_account(self, request, id=None):
        account = self.get_object(id=id)
        self.check_account_permission(account)
        account.is_active = False
        account.save()
        return Response(
            {"message": "Account deactivated successfully."}, status=status.HTTP_200_OK
        )

    @action(methods=["get"], detail=False)
    def deactivated_accounts(self, request, id=None):
        user_company = self.request.query_params.get("company")
        queryset = (
            Account.objects.filter(is_active=False)
            .filter(models.Q(company__id=user_company) | models.Q(paybox_default=True))
            .distinct()
            .order_by("-created_at")
        )
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(methods=["put"], detail=True)
    def reactivate_account(self, request, id=None):
        account = self.get_object(id=id)
        self.check_account_permission(account)
        account.is_active = True
        account.save()
        return Response(
            {"message": "Account reactivated successfully."}, status=status.HTTP_200_OK
        )


class JournalEntryViewSet(viewsets.ModelViewSet):
    queryset = JournalEntry.objects.all().order_by("-created_at")
    serializer_class = JournalEntrySerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get_queryset(self):
        user_company = self.request.GET.get("company", None)
        user_branch = self.request.GET.get("branch", None)
        is_draft = self.request.GET.get("is_draft", None)
        queryset = self.queryset
        if user_company:
            queryset = queryset.filter(company__id=user_company)
        if user_branch:
            queryset = queryset.filter(branch__id=user_branch)
        if is_draft:
            queryset = queryset.filter(is_draft=is_draft)
        return queryset

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.validated_data["journal_number"] = generate_journal_number()
        serializer.save(recorded_by=request.user)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        return super().update(request, partial=True, *args, **kwargs)

    @action(detail=False, methods=["post"], url_path="upload-csv")
    def upload_csv(self, request):
        """
        Bulk upload journal entries with journal lines from a CSV file.
        """
        serializer = JournalEntryUploadSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        file = serializer.validated_data.get("file")
        branch = serializer.validated_data.get("branch")
        company = serializer.validated_data.get("company")
        recorded_by = request.user
        if not file.name.endswith(".csv"):
            return Response(
                {"error": "Please upload a valid CSV file."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        decoded_file = file.read().decode("utf-8").splitlines()
        reader = csv.DictReader(decoded_file)

        journal_entries = {}
        with transaction.atomic():
            try:
                for row in reader:
                    journal_number = generate_journal_number()
                    while journal_number in journal_entries:
                        journal_number = generate_journal_number()
                    date = datetime.strptime(row["date"], "%d/%m/%Y").date()

                    if journal_number not in journal_entries:
                        try:
                            journal_entry: JournalEntry = JournalEntry.objects.create(
                                date=date,
                                journal_number=journal_number,
                                references=row.get("references"),
                                notes=row.get("notes"),
                                journal_type=row["journal_type"],
                                transaction_type=row["transaction_type"],
                                recorded_by=recorded_by,
                                company=company,
                                branch=branch,
                            )
                            journal_entries[journal_number] = journal_entry
                        except Exception as e:
                            transaction.set_rollback(rollback=True)
                            return Response(
                                {
                                    "error": f"Error creating journal entry {journal_number}: {str(e)}"
                                },
                                status=400,
                            )

                    journal_entry: JournalEntry = journal_entries[journal_number]

                    try:
                        account = Account.objects.get(
                            name__iexact=row["account_name"].lower()
                        )
                    except Account.DoesNotExist:
                        transaction.set_rollback(rollback=True)
                        return Response(
                            {
                                "error": f"Account '{row['account_name']}' does not exist in our chart of accounts"
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                    try:
                        if (
                            float(row["debit_amount"]) == 0
                            or float(row["credit_amount"]) == 0
                        ):
                            transaction.set_rollback(rollback=True)
                            return Response(
                                {
                                    "error": "both debit_amount and credit_amount must be non-zero."
                                },
                                status=status.HTTP_400_BAD_REQUEST,
                            )

                        JournalLine.objects.create(
                            journal_entry=journal_entry,
                            account=account,
                            description=row["description"],
                            debit_amount=float(row["debit_amount"]),
                            credit_amount=float(row["credit_amount"]),
                            bal_before=float(row["bal_before"]),
                            bal_after=float(row["bal_after"]),
                            customer=row.get("customer"),
                            tax=float(row.get("tax", 0.0)),
                        )
                    except Exception as e:
                        transaction.set_rollback(rollback=True)
                        return Response(
                            {
                                "error": f"Error creating journal line for journal entry {journal_number}: {str(e)}"
                            },
                            status=400,
                        )

                # Validate debit and credit amounts for each journal entry
                for journal_number, journal_entry in journal_entries.items():
                    journal_lines = journal_entry.lines.all()
                    total_debit = sum(line.debit_amount for line in journal_lines)
                    total_credit = sum(line.credit_amount for line in journal_lines)

                    if total_debit != total_credit:
                        transaction.set_rollback(rollback=True)
                        return Response(
                            {
                                "error": f"Journal entry {journal_number} validation failed: Debit ({total_debit}) != Credit ({total_credit})."
                            },
                            status=400,
                        )

                return Response(
                    {"message": "CSV uploaded successfully!"},
                    status=status.HTTP_201_CREATED,
                )

            except Exception as e:
                transaction.set_rollback(rollback=True)
                return Response(
                    {"error": f"An error occurred while processing the CSV: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )


class JournalLineViewSet(viewsets.ModelViewSet):
    queryset = JournalLine.objects.all().order_by("-created_at")
    serializer_class = JournalLineSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def validate_debit_credit_balance(self, journal_entry):
        total_debit = (
            journal_entry.lines.aggregate(total=models.Sum("debit_amount"))["total"]
            or 0
        )
        total_credit = (
            journal_entry.lines.aggregate(total=models.Sum("credit_amount"))["total"]
            or 0
        )
        if total_debit != total_credit:
            raise ValidationError(
                detail={
                    "error": "The sum of debit amounts must equal the sum of credit amounts."
                }
            )

    def update(self, request, *args, **kwargs):
        journal_line = self.get_object()
        journal_entry = journal_line.journal_entry  # Get the associated JournalEntry

        with transaction.atomic():
            serializer = self.get_serializer(
                journal_line, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()

            # Validate the balance after the update
            self.validate_debit_credit_balance(journal_entry)

        return Response(
            {"message": "Journal line updated successfully.", "data": serializer.data},
            status=status.HTTP_200_OK,
        )

    def destroy(self, request, *args, **kwargs):
        journal_line = self.get_object()
        journal_entry = journal_line.journal_entry  # Get the associated JournalEntry

        with transaction.atomic():
            journal_line.delete()

            # Validate the balance after deletion
            self.validate_debit_credit_balance(journal_entry)

        return Response(
            {"message": "Journal line deleted successfully."},
            status=status.HTTP_204_NO_CONTENT,
        )


class ProfitAndLossReportView(views.APIView):
    """
    Class-based view to generate a Profit and Loss (P&L) Statement for a company.
    Date range is optional; if not provided, all data for the company is considered.
    """

    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, company_id):
        start_date = (
            datetime.strptime(request.GET.get("start_date"), "%Y-%m-%d")
            if request.GET.get("start_date")
            else None
        )
        end_date = (
            datetime.strptime(request.GET.get("end_date"), "%Y-%m-%d")
            if request.GET.get("end_date")
            else None
        )

        report = ProfitAndLossReportGenerator(
            company_id=company_id, start_date=start_date, end_date=end_date
        )
        data = report.generate_report()

        return Response(
            data,
            status=status.HTTP_200_OK,
        )


class BalanceSheetReportView(views.APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, company_id):
        start_date = request.GET.get("start_date", None)
        end_date = request.GET.get("end_date", None)
        report_generator = BalanceSheetReportGenerator(
            company_id=company_id, start_date=start_date, end_date=end_date
        )
        data = report_generator.generate_report()

        return Response(data, status=200)


class TrialBalanceReportView(views.APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, company_id):
        date = request.GET.get("date", None)
        report_generator = TrialBalanceReportGenerator(company_id=company_id, date=date)
        data = report_generator.generate_report()

        return Response(data, status=200)
