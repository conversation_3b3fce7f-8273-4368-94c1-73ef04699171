from django.db import models
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator, MaxValueValidator
from core.models import BaseModel
from payroll_app.models import CompanyDepartmentSettings, CompanyEmployeeList
from requisition.models import Company

# Create your models here.

KPI = [
    ("PERCENTAGE_KPI", "PERCENTAGE_KPI"),
    ("PERCENTAGE_TASK_TRACKED", "PERCENTAGE_TASK_TRACKED"),
    ("COUNT_TASK_TRACKED", "COUNT_TASK_TRACKED"),
    ("INCREASE_KPI", "INCREASE_KPI"),
    ("DECREASE_KPI", "DECREASE_KPI"),
    ("IN_BETWEEN", "IN_BETWEEN"),
    ("AT_MOST", "AT_MOST"),
    ("AT_LEAST", "AT_LEAST"),
    # ("MILESTONE_KPI", "MILESTONE_KPI"),
    # ("BASELIN<PERSON>_KPI", "BASELINE_KPI"),
    # ("CONTROL_KPI", "CONTROL_KPI"),
]

TASK_PRIORITY = [
    ("HIGH", "HIGH"),
    ("MEDIUM", "MEDIUM"),
    ("LOW", "LOW"),
]

TASK_STATUS = [
    ("NOT_STARTED", "NOT_STARTED"),
    ("SCHEDULED", "SCHEDULED"),
    ("IN_PROGRESS", "IN_PROGRESS"),
    ("COMPLETED", "COMPLETED"),
]

STATUS = [
    ("NOT_STARTED", "NOT_STARTED"),
    ("ON_TRACK", "ON_TRACK"),
    ("AT_RISK", "AT_RISK"),
    ("OFF_TRACK", "OFF_TRACK"),
    ("COMPLETED", "COMPLETED"),
]

KPI_CATEGORY_TYPE = [
    ("NUMBER", "NUMBER"),
    ("PERCENTAGE", "PERCENTAGE"),
]

GOAL_TYPE = [
    ("INDIVIDUAL", "INDIVIDUAL"),
    ("COMPANY", "COMPANY"),
]

APPRAISAL_ASSESSMENT_STATUS = [
    ("PENDING", "PENDING"),
    ("COMPLETED", "COMPLETED"),
    ("NOT_DONE", "NOT_DONE"),
]
class CompanyGoals(BaseModel):

    company = models.ForeignKey(Company, on_delete=models.CASCADE, 
                                related_name="performance_company_goal", null=True, blank=True)
    name = models.CharField(max_length=255)
    description = models.CharField(null=True, blank=True, max_length=255)
    goal_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    period_name = models.CharField(null=True, blank=True, max_length=255)
    period_start_date =  models.DateField()
    period_end_date = models.DateField()
    goal_type = models.CharField(max_length=255, choices=GOAL_TYPE, default="COMPANY")
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    created_by = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, related_name="individual_created_by", null=True, blank=True)
    aligned_goals = models.ManyToManyField('self', symmetrical=False, blank=True, related_name="aligned_with")
    aligned_key_results = models.ManyToManyField("performance_management.CompanyKeyResult", blank=True, symmetrical=False, related_name="goal_aligned_with_key_results")
    employees = models.ManyToManyField(CompanyEmployeeList, related_name="performance_company_employees", blank=True)
    department = models.ManyToManyField(CompanyDepartmentSettings, related_name="performance_company_department", blank=True)
    
    def __str__(self):
        return str(self.name)
    
    # def clean(self):
    #     """Ensure that start_date is before end_date."""
    #     if self.period_start_date > self.period_end_date:
    #         raise ValidationError({"message": "Start date must be before end date."})
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY GOAL"
        verbose_name_plural = "COMPANY GOALS"

    @property
    def combined_assignees(self):
        get_owners = []
        for employee in self.employees.all():
            get_owners.append({
                "id": employee.id,
                "type": "employee",
                "name": employee.full_name,
            })
        for department in self.department.all():
            get_owners.append({
                "id": department.id,
                "type": "department",
                "name": department.department_name,
            })
        # return self.employees.all().union(self.department.all())
        return get_owners

    def calculate_percentage(self, processed_goals=None, processed_krs=None):
        """Calculate goal percentage including alignments while preventing recursion"""
        if processed_goals is None:
            processed_goals = set()
        if processed_krs is None:
            processed_krs = set()
            
        # Prevent infinite recursion
        if self.id in processed_goals:
            return 0
            
        processed_goals.add(self.id)

        # Calculate base percentage from key results
        key_results = CompanyKeyResult.objects.filter(company_goal__id=self.id, is_deleted=False)
        total_percentage = 0
        count = 0

        for key_result in key_results:
            kpi_percentage = key_result.calculate_percentage(processed_krs) or 0
            total_percentage += kpi_percentage
            count += 1 if kpi_percentage else 1

        this_percent = (total_percentage / count) if count > 0 else 0

        # Calculate alignment percentages
        key_result_alignment_count = 0
        key_result_alignment_percentage = 0
        all_key_result_alignment = self.aligned_key_results.filter(is_deleted=False)
        for alignment in all_key_result_alignment:
            if alignment.id not in processed_krs:
                key_result_alignment_count += 1
                key_result_alignment_percentage += alignment.calculate_percentage(processed_krs)
        try:
            all_key_result_alignment_percentage = key_result_alignment_percentage / key_result_alignment_count
        except ZeroDivisionError:
            all_key_result_alignment_percentage = 0

        all_goal_alignment_count = 0
        all_goal_alignment_percentage = 0
        all_goal_alignment = self.aligned_goals.filter(is_deleted=False)
        for alignment in all_goal_alignment:
            if alignment.id not in processed_goals:
                all_goal_alignment_count += 1
                all_goal_alignment_percentage += alignment.calculate_percentage(processed_goals, processed_krs)
        try:
            all_alignment_percentage = all_goal_alignment_percentage / all_goal_alignment_count
        except ZeroDivisionError:
            all_alignment_percentage = 0
        
        if all_key_result_alignment or all_goal_alignment:
            try:
                final_percent = (this_percent + all_key_result_alignment_percentage + all_alignment_percentage) / (key_result_alignment_count + all_goal_alignment_count + 1)
            except ZeroDivisionError:
                final_percent = 0
        else:
            final_percent = this_percent

        return final_percent

    @property
    def percentage(self):
        """Property wrapper for calculate_percentage"""
        return self.calculate_percentage(None, None)
    
    @classmethod
    def clone_goal(cls, company_goal, employee):
        if company_goal is None:
            return False
        else:
            this_goal = cls.objects.create(
                company = company_goal.company,
                name = company_goal.name,
                description = company_goal.description,
                period_name = company_goal.period_name,
                period_start_date = company_goal.period_start_date,
                period_end_date = company_goal.period_end_date,
                goal_type = company_goal.goal_type,
                is_active = company_goal.is_active,
                is_deleted = company_goal.is_deleted,
                created_by = employee if company_goal.goal_type == "INDIVIDUAL" else None,
            )
            # Clone many-to-many relationships
            this_goal.aligned_goals.set(company_goal.aligned_goals.all())
            this_goal.aligned_key_results.set(company_goal.aligned_key_results.all())
            this_goal.employees.set(company_goal.employees.all())
            this_goal.department.set(company_goal.department.all())
            return True
            
class CheckInFrequency(BaseModel):
    name = models.CharField(max_length=255)
    is_deleted = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CHECK_IN FREQUENCY"
        verbose_name_plural = "CHECK_IN FREQUENCIES"

    def __str__(self):
        return str(self.name)

class KPICategory(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, 
                                related_name="kpi_category_company", null=True, blank=True)
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=255, choices=KPI_CATEGORY_TYPE, default="NUMBER")
    is_deleted = models.BooleanField(default=False)

    def __str__(self):
        if self.company:
            return f"{self.company.company_name}-{self.name}"
        else:
            return str(self.name)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "KPI CATEGORY"
        verbose_name_plural = "KPI CATEGORIES"
    
class Tasks(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, 
                                related_name="task_company", null=True, blank=True)
    name = models.CharField(max_length=255)
    assign_to = models.ManyToManyField(CompanyEmployeeList, related_name="task_assigned_to", blank=True) 
    company_goal = models.ForeignKey(CompanyGoals, on_delete=models.CASCADE, 
                                related_name="task_company_goals", null=True, blank=True)
    key_result = models.ForeignKey("performance_management.CompanyKeyResult", on_delete=models.CASCADE, 
                                    related_name='task_key_result', null=True, blank=True)
    priority = models.CharField(max_length=25, choices=TASK_PRIORITY, default="HIGH")
    start_date =  models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    created_by = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, related_name="task_created_by", null=True, blank=True)
    status = models.CharField(max_length=25, choices=TASK_STATUS, default="NOT_STARTED")
    task_type = models.CharField(max_length=255, choices=GOAL_TYPE, default="COMPANY")
    is_deleted = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "TASK"
        verbose_name_plural = "TASKS"
    
    @property
    def get_assign_to(self):
        assigned_to = []
        for employee in self.assign_to.all():
            assigned_to.append(
                {
                    "id": employee.id,
                    "name": employee.full_name,
                }
            )
        return assigned_to


class CompanyKeyResult(BaseModel):

    company = models.ForeignKey(Company, on_delete=models.CASCADE, 
                                related_name="performance_company_key_feature", null=True, blank=True)
    company_goal = models.ForeignKey(CompanyGoals, on_delete=models.CASCADE, 
                                related_name="performance_company_goals", null=True, blank=True)
    name = models.CharField(max_length=255)

    description = models.CharField(null=True, blank=True, max_length=255)
    period_name = models.CharField(null=True, blank=True, max_length=255)
    period_start_date =  models.DateField(null=True, blank=True)
    period_end_date = models.DateField(null=True, blank=True)
    check_in_frequency = models.ManyToManyField(CheckInFrequency, related_name="performance_company_check_in_frequency")
    kpi_type = models.CharField(max_length=25, choices=KPI, default="PERCENTAGE_KPI")
    start_count = models.PositiveIntegerField(null=True, blank=True)
    end_count = models.PositiveIntegerField(null=True, blank=True)
    status = models.CharField(max_length=25, choices=STATUS, default="ON_TRACK")

    employees = models.ManyToManyField(CompanyEmployeeList, related_name="performance_key_result_employees", blank=True)
    department = models.ManyToManyField(CompanyDepartmentSettings, related_name="performance_key_result_department", blank=True)

    # Valid fields for tracking KPI
    # percentage_kpi = models.FloatField(null=True, blank=True, default=0)
    kpi_category = models.ForeignKey(KPICategory, on_delete=models.CASCADE, 
                                related_name="key_result_kpi_category", null=True, blank=True)
    from_count = models.FloatField(null=True, blank=True)
    to_count = models.FloatField(null=True, blank=True)
    achieved_value = models.FloatField(null=True, blank=True)
    upper_limit = models.FloatField(null=True, blank=True)
    lower_limit = models.FloatField(null=True, blank=True)
    # Fields for KPI tracking
    target_value = models.FloatField(null=True, blank=True)
    # achieved_value = models.FloatField(null=True, blank=True)
    baseline_value = models.FloatField(null=True, blank=True)
    milestone_achieved = models.BooleanField(default=False)
    control_value = models.FloatField(null=True, blank=True)  # For CONTROL_KPI
    progress = models.FloatField(null=True, blank=True)
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    # aligned_goals = models.ManyToManyField(CompanyGoals, related_name="aligned_key_results", blank=True)
    aligned_key_results = models.ManyToManyField('self', blank=True, symmetrical=False, related_name="aligned_with_key_results")

    class Meta:
        ordering = ["-created_at"]
        # ordering = ['-period_start_date']
        verbose_name = "COMPANY KEY RESULT"
        verbose_name_plural = "COMPANY KEY RESULTS"

    @property
    def combined_assignees(self):
        get_owners = []
        for employee in self.employees.all():
            get_owners.append({
                "id": employee.id,
                "type": "employee",
                "name": employee.full_name,
            })
        for department in self.department.all():
            get_owners.append({
                "id": department.id,
                "type": "department",
                "name": department.department_name,
            })
        # return self.employees.all().union(self.department.all())
        return get_owners
    
    # Calculate progress for percentage KPI
    def calculate_progress_percentage(self):
        if self.kpi_type == "PERCENTAGE_KPI" and self.target_value:
            if self.achieved_value is not None:
                self.progress = (self.achieved_value / self.target_value) * 100
        return self.progress

    # Check if milestones have been achieved
    def check_milestone(self):
        if self.kpi_type == "MILESTONE_KPI":
            self.milestone_achieved = self.achieved_value is not None and self.achieved_value >= self.target_value
        return self.milestone_achieved

    # Check if current value exceeds baseline
    def check_baseline(self):
        if self.kpi_type == "BASELINE_KPI":
            if self.achieved_value is not None and self.baseline_value is not None:
                return self.achieved_value >= self.baseline_value
        return False

    # Calculate increase from previous value
    def calculate_increase(self, previous_value):
        if self.kpi_type == "INCREASE_KPI":
            if self.achieved_value is not None:
                return self.achieved_value - previous_value
        return 0

    # Calculate decrease from previous value
    def calculate_decrease(self, previous_value):
        if self.kpi_type == "DECREASE_KPI":
            if self.achieved_value is not None:
                return previous_value - self.achieved_value
        return 0

    # Check control limits
    def check_control(self):
        if self.kpi_type == "CONTROL_KPI":
            if self.control_value is not None:
                return self.achieved_value is not None and self.achieved_value <= self.control_value
        return False

    def __str__(self):
        return f"{self.name}-{self.id}"
    
    def calculate_percentage(self, processed_krs=None):
        """Calculate the percentage for each KPI type."""
        if processed_krs is None:
            processed_krs = set()
            
        # Prevent infinite recursion
        if self.id in processed_krs:
            return 0
            
        processed_krs.add(self.id)

        if self.kpi_type == "PERCENTAGE_KPI":
            achieved_value = self.achieved_value
            if achieved_value is None:
                this_percent = 0
            else:
                this_percent = self.achieved_value

            # Get alignments while avoiding recursion
            alignment_count = 0
            alignment_percentage = 0
            all_alignment = self.aligned_key_results.filter(is_deleted=False)
            for alignment in all_alignment:
                if alignment.id not in processed_krs:
                    alignment_count += 1
                    alignment_percentage += alignment.calculate_percentage(processed_krs)
            try:
                all_alignment_percentage = alignment_percentage / alignment_count
            except ZeroDivisionError:
                all_alignment_percentage = 0
            
            if all_alignment:
                try:
                    final_percent = (this_percent + all_alignment_percentage) / (alignment_count + 1)
                except ZeroDivisionError:
                    final_percent = 0
            else:
                final_percent = this_percent

        elif self.kpi_type == "PERCENTAGE_TASK_TRACKED":
            all_tasks = Tasks.objects.filter(key_result=self, is_deleted=False)
            task_count = all_tasks.count() or 0
            completed_task =  all_tasks.filter(status="COMPLETED").count() or 0
            try:
                this_percent = (completed_task / task_count) * 100
            except ZeroDivisionError:
                this_percent = 0

            alignment_count = 0
            alignment_percentage = 0
            all_alignment = self.aligned_key_results.all()
            for alignment in all_alignment:
                alignment_count += 1
                alignment_percentage += alignment.calculate_percentage(processed_krs)
            try:
                all_alignment_percentage = alignment_percentage / alignment_count
            except ZeroDivisionError:
                all_alignment_percentage = 0
            
            if all_alignment:
                try:
                    final_percent = (this_percent + all_alignment_percentage) / (alignment_count + 1)
                except ZeroDivisionError:
                    final_percent = 0
            else:
                final_percent = this_percent

        elif self.kpi_type == "COUNT_TASK_TRACKED":
            start_count = self.start_count if self.start_count else 0
            end_count = self.end_count if self.end_count else 0
            all_tasks = Tasks.objects.filter(key_result=self, is_deleted=False)
            task_count = all_tasks.count() or 0
            if task_count > start_count:
                completed_task =  all_tasks.filter(status="COMPLETED").count() or 0
                if completed_task > start_count:
                    try:
                        this_percent = (completed_task / end_count) * 100
                    except ZeroDivisionError:
                        this_percent = 0
                else:
                    this_percent = 0
            else:
                this_percent = 0

            alignment_count = 0
            alignment_percentage = 0
            all_alignment = self.aligned_key_results.all()
            for alignment in all_alignment:
                alignment_count += 1
                alignment_percentage += alignment.calculate_percentage(processed_krs)
            try:
                all_alignment_percentage = alignment_percentage / alignment_count
            except ZeroDivisionError:
                all_alignment_percentage = 0
            
            if all_alignment:
                try:
                    final_percent = (this_percent + all_alignment_percentage) / (alignment_count + 1)
                except ZeroDivisionError:
                    final_percent = 0
            else:
                final_percent = this_percent

        elif self.kpi_type == "INCREASE_KPI":
            from_count = self.from_count
            to_count = self.to_count
            achieved_value = self.achieved_value
            if achieved_value is None:
                this_percent = 0
            else:
                if achieved_value > from_count:
                    try:
                        this_percent = ((achieved_value - from_count) / (to_count - from_count)) * 100
                    except ZeroDivisionError:
                        this_percent = 0
                else:
                    this_percent = 0

            alignment_count = 0
            alignment_percentage = 0
            all_alignment = self.aligned_key_results.all()
            for alignment in all_alignment:
                alignment_count += 1
                alignment_percentage += alignment.calculate_percentage(processed_krs)
            try:
                all_alignment_percentage = alignment_percentage / alignment_count
            except ZeroDivisionError:
                all_alignment_percentage = 0
            
            if all_alignment:
                try:
                    final_percent = (this_percent + all_alignment_percentage) / (alignment_count + 1)
                except ZeroDivisionError:
                    final_percent = 0
            else:
                final_percent = this_percent

        elif self.kpi_type == "DECREASE_KPI":
            from_count = self.from_count
            to_count = self.to_count
            achieved_value = self.achieved_value
            if achieved_value is None:
                this_percent = 0
            else:
                if achieved_value < from_count:
                    try:
                        this_percent = ((from_count - achieved_value) / (from_count - to_count))  * 100
                    except ZeroDivisionError:
                        this_percent = 0
                else:
                    this_percent = 0

            alignment_count = 0
            alignment_percentage = 0
            all_alignment = self.aligned_key_results.all()
            for alignment in all_alignment:
                alignment_count += 1
                alignment_percentage += alignment.calculate_percentage(processed_krs)
            try:
                all_alignment_percentage = alignment_percentage / alignment_count
            except ZeroDivisionError:
                all_alignment_percentage = 0
            
            if all_alignment:
                try:
                    final_percent = (this_percent + all_alignment_percentage) / (alignment_count + 1)
                except ZeroDivisionError:
                    final_percent = 0
            else:
                final_percent = this_percent

        elif self.kpi_type == "AT_MOST":
            upper_limit = self.upper_limit
            achieved_value = self.achieved_value
            if achieved_value is None:
                this_percent = 0
            else:
                if achieved_value <= upper_limit:
                    this_percent = 100
                else:
                    this_percent = 0

            alignment_count = 0
            alignment_percentage = 0
            all_alignment = self.aligned_key_results.all()
            for alignment in all_alignment:
                alignment_count += 1
                alignment_percentage += alignment.calculate_percentage(processed_krs)
            try:
                all_alignment_percentage = alignment_percentage / alignment_count
            except ZeroDivisionError:
                all_alignment_percentage = 0
            
            if all_alignment:
                try:
                    final_percent = (this_percent + all_alignment_percentage) / (alignment_count + 1)
                except ZeroDivisionError:
                    final_percent = 0
            else:
                final_percent = this_percent

        elif self.kpi_type == "AT_LEAST":
            lower_limit = self.lower_limit
            achieved_value = self.achieved_value
            if achieved_value is None:
                this_percent = 0
            else:
                if achieved_value >= lower_limit:
                    this_percent = 100
                else:
                    this_percent = 0

            alignment_count = 0
            alignment_percentage = 0
            all_alignment = self.aligned_key_results.all()
            for alignment in all_alignment:
                alignment_count += 1
                alignment_percentage += alignment.calculate_percentage(processed_krs)
            try:
                all_alignment_percentage = alignment_percentage / alignment_count
            except ZeroDivisionError:
                all_alignment_percentage = 0
            
            if all_alignment:
                try:
                    final_percent = (this_percent + all_alignment_percentage) / (alignment_count + 1)
                except ZeroDivisionError:
                    final_percent = 0
            else:
                final_percent = this_percent

        elif self.kpi_type == "IN_BETWEEN":
            lower_limit = self.lower_limit
            upper_limit = self.upper_limit
            achieved_value = self.achieved_value
            if achieved_value is None:
                this_percent = 0
            else:
                if lower_limit <= achieved_value <= upper_limit:
                    this_percent = 100
                else:
                    this_percent = 0

            alignment_count = 0
            alignment_percentage = 0
            all_alignment = self.aligned_key_results.all()
            for alignment in all_alignment:
                alignment_count += 1
                alignment_percentage += alignment.calculate_percentage(processed_krs)
            try:
                all_alignment_percentage = alignment_percentage / alignment_count
            except ZeroDivisionError:
                all_alignment_percentage = 0
            
            if all_alignment:
                try:
                    final_percent = (this_percent + all_alignment_percentage) / (alignment_count + 1)
                except ZeroDivisionError:
                    final_percent = 0
            else:
                final_percent = this_percent

        return final_percent

    @property
    def percentage(self):
        """Property wrapper for calculate_percentage"""
        return self.calculate_percentage()

class KPICheckIn(BaseModel):
    key_result = models.ForeignKey(CompanyKeyResult, on_delete=models.CASCADE, 
                                    related_name='check_ins')
    check_in_date = models.DateField()  # Automatically set the date when created
    achieved_value = models.FloatField(null=True, blank=True)
    status = models.CharField(max_length=25, choices=STATUS, default="ON_TRACK")
    comments = models.TextField(null=True, blank=True)  # Optional comments for additional context
    created_by = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, related_name="check_in_created_by", null=True, blank=True)

    def __str__(self):
        return f"Check-in for {self.key_result.name} on {self.check_in_date}"
    
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "KPI CHECK-IN"
        verbose_name_plural = "KPI CHECK-INS"

class Review(BaseModel):
    company_goal = models.ForeignKey(CompanyGoals, on_delete=models.CASCADE, 
                                related_name="performance_company_review", null=True, blank=True)
    title = models.CharField(max_length=155)
    progress = models.TextField(null=True, blank=True)
    plan = models.TextField(null=True, blank=True)
    problems = models.TextField(null=True, blank=True)  
    created_by = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, related_name="review_created_by", null=True, blank=True) 
    is_deleted = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY REVIEW"
        verbose_name_plural = "COMPANY REVIEWS"

class StatusUpdate(BaseModel):
    key_result = models.ForeignKey(CompanyKeyResult, on_delete=models.CASCADE, 
                                    related_name='status_update_ins')
    check_in_date = models.DateField()
    status = models.CharField(max_length=255, null=True, blank=True)
    percentage = models.FloatField(null=True, blank=True)

    def __str__(self):
        return f"Check-in for {self.key_result.name} on {self.check_in_date}"
    
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "STATUS UPDATE"
        verbose_name_plural = "STATUS UPDATES"

class ActivityLog(BaseModel):
    status_update = models.ForeignKey(StatusUpdate, on_delete=models.CASCADE, 
                                    related_name='activity_log_status_update_ins', null=True)
    status = models.CharField(max_length=255, null=True, blank=True)
    percentage = models.FloatField(null=True, blank=True)
    achieved_value = models.FloatField(null=True, blank=True)
    comments = models.TextField(null=True, blank=True)
    created_by = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, related_name="activity_log_created_by", null=True, blank=True)
    
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ACTIVITY LOG"
        verbose_name_plural = "ACTIVITY LOGS"

class RatingCategory(BaseModel):
    name = models.CharField(max_length=255, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return str(self.name)
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "RATING CATEGORY"
        verbose_name_plural = "RATING CATEGORIES"

class Rating(BaseModel):
    name = models.CharField(max_length=255, null=True, blank=True)
    rating_category = models.ForeignKey(RatingCategory, on_delete=models.CASCADE,
                                        related_name="rating_category", null=True, blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return str(self.name)
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "RATING"
        verbose_name_plural = "RATINGS"

class Assessment(BaseModel):
    rating_category = models.ManyToManyField(RatingCategory,
                                        related_name="assessment_rating_category", blank=True)
    rating = models.ManyToManyField(Rating,
                                related_name="assessment_rating", blank=True)
    description = models.CharField(max_length=255, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE,
                                related_name="assessment_company", null=True, blank=True)
    created_by = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE,
                                    related_name="assessment_created_by", null=True, blank=True)
    is_deleted = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.description}-{self.company.company_name if self.company else 'No Company'}"
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ASSESSMENT"
        verbose_name_plural = "ASSESSMENTS"

    @property
    def all_ratings(self):
        get_all_ratings = []
        for rating in self.rating.all():
            get_all_ratings.append({
                "id": rating.id,
                "name": rating.name,
            })
        return get_all_ratings
    
    @property
    def all_ratings_categories(self):
        get_all_rating_category = []
        for rating_category in self.rating_category.all():
            get_all_rating_category.append({
                "id": rating_category.id,
                "name": rating_category.name,
            })
        return get_all_rating_category

class Appraisal(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, 
                                related_name="appraisal_company", null=True, blank=True)
    access = models.ForeignKey(CompanyDepartmentSettings, on_delete=models.CASCADE,
                                related_name="appraisal_access", null=True, blank=True)
    period_name = models.CharField(max_length=255, null=True, blank=True)
    period_start_date = models.DateField(null=True, blank=True)
    period_end_date = models.DateField(null=True, blank=True)
    access_list = models.ManyToManyField(CompanyEmployeeList, related_name="apprraisal_access_list", blank=True)
    peer_reviewer = models.ManyToManyField(CompanyEmployeeList, related_name="appraisal_peer_reviewers", blank=True)
    review_assessment = models.ManyToManyField(Assessment, related_name="appraisal_peer_reviewers", blank=True)
    created_by = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, related_name="appraisal_created_by", null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    
    def __str__(self):
        return f"{self.period_name}-{self.period_start_date}-{self.period_end_date}-{self.company.company_name if self.company else 'No Company'}"
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "APPRAISAL"
        verbose_name_plural = "APPRAISALS"

    @property
    def all_access_asignees(self):
        get_all_access_asignees = []
        for employee in self.peer_reviewer.all():
            get_all_access_asignees.append({
                "id": employee.id,
                "name": employee.full_name,
            })
        return get_all_access_asignees
    
    @property
    def all_peer_reviewers(self):
        get_all_peer_reviewers = []
        for employee in self.peer_reviewer.all():
            get_all_peer_reviewers.append({
                "id": employee.id,
                "name": employee.full_name,
            })
        return get_all_peer_reviewers
    
    @property
    def all_review_assessments(self):
        get_all_review_assessments = []
        for assessment in self.review_assessment.all():
            get_all_review_assessments.append({
                "id": assessment.id,
                "description": assessment.description,
                "ratings": assessment.all_ratings,
                "rating_categories": assessment.all_ratings_categories,
            })
        return get_all_review_assessments

class AssessmentReview(BaseModel):
    appraisal = models.ForeignKey(Appraisal, on_delete=models.CASCADE, 
                                related_name="assessment_review_appraisal", null=True, blank=True)
    assessment = models.ForeignKey(Assessment, on_delete=models.CASCADE, 
                                related_name="assessment_review_assessment", null=True, blank=True)
    rating_category = models.ForeignKey(RatingCategory, on_delete=models.CASCADE,
                                        related_name="assessment_review_rating_category", null=True, blank=True)
    rating = models.ForeignKey(Rating, on_delete=models.CASCADE,
                                        related_name="assessment_review_rating", null=True, blank=True)
    employee = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, 
                                related_name="appraisal_review_employee", null=True, blank=True)
    appraisal_for = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, 
                                related_name="appraisal_review_appraisal_for", null=True, blank=True)
    def __str__(self):
        return f"{self.assessment.description}-{self.appraisal.id}"    
    
    
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ASSESSMENT REVIEW"
        verbose_name_plural = "ASSESSMENT REVIEWS"

class AppraisalAssessment(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, 
                                related_name="appraisal_assessment_company", null=True, blank=True)
    appraisal = models.ForeignKey(Appraisal, on_delete=models.CASCADE, 
                                related_name="appraisal_assessment_appraisal", null=True, blank=True)
    assessment_review = models.ManyToManyField(AssessmentReview,
                                related_name="appraisal_assessment_assessment_review", blank=True)
    employee = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, 
                                related_name="appraisal_assessment_employee", null=True, blank=True)
    appraisal_for = models.ForeignKey(CompanyEmployeeList, on_delete=models.CASCADE, 
                                related_name="appraisal_assessment_appraisal_for", null=True, blank=True)
    status = models.CharField(max_length=25, choices=APPRAISAL_ASSESSMENT_STATUS, default="PENDING")
    class Meta:
        ordering = ["-created_at"]
        verbose_name = "APPRAISAL ASSESSMENT"
        verbose_name_plural = "APPRAISAL ASSESSMENTS"