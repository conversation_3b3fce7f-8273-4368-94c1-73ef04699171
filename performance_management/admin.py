from django.contrib import admin

from import_export.admin import ImportExportModelAdmin
from performance_management.models import ActivityLog, CompanyGoals, CompanyKeyResult, StatusUpdate
from performance_management.resources import *

# Register your models here.

class CompanyGoalsResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyGoalsResource
    search_fields = ["company__company_name", "name", "description"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class CompanyKeyResultResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyKeyResultResource
    search_fields = ["company_goal__name", "name", "description"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class KPICheckInResourceAdmin(ImportExportModelAdmin):
    resource_class = KPICheckInResource
    search_fields = ["key_result__name", "status"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class TasksResourceAdmin(ImportExportModelAdmin):
    resource_class = TasksResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class CheckInFrequencyResourceAdmin(ImportExportModelAdmin):
    resource_class = CheckInFrequencyResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class KPICategoryResourceAdmin(ImportExportModelAdmin):
    resource_class = KPICategoryResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class ReviewResourceAdmin(ImportExportModelAdmin):
    resource_class = ReviewResource
    search_fields = ["title"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class StatusUpdateResourceAdmin(ImportExportModelAdmin):
    resource_class = StatusUpdateResource
    search_fields = ["status"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class ActivityLogResourceAdmin(ImportExportModelAdmin):
    resource_class = ActivityLogResource
    search_fields = ["status"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class RatingCategoryResourceAdmin(ImportExportModelAdmin):
    resource_class = RatingCategoryResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class RatingResourceAdmin(ImportExportModelAdmin):
    resource_class = RatingResource
    search_fields = ["name"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class AssessmentResourceAdmin(ImportExportModelAdmin):
    resource_class = AssessmentResource
    search_fields = ["company__company_name", "description", "created_by__employee_email"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class AppraisalResourceAdmin(ImportExportModelAdmin):
    resource_class = AppraisalResource
    search_fields = ["company__company_name", "created_by__employee_email"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class AssessmentReviewResourceAdmin(ImportExportModelAdmin):
    resource_class = AssessmentReviewResource
    search_fields = ["employee__employee_email",]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class AssessmentReviewInline(admin.TabularInline):
    model = AppraisalAssessment.assessment_review.through
    extra = 0
    verbose_name = "Assessment Review"
    verbose_name_plural = "Assessment Reviews"

class AppraisalAssessmentResourceAdmin(ImportExportModelAdmin):
    resource_class = AppraisalAssessmentResource
    search_fields = ["appraisal__id", "employee__employee_email"]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    inlines = [AssessmentReviewInline]
    autocomplete_fields = ("company",)

admin.site.register(CompanyGoals, CompanyGoalsResourceAdmin)
admin.site.register(CompanyKeyResult, CompanyKeyResultResourceAdmin)
admin.site.register(KPICheckIn, KPICheckInResourceAdmin)
admin.site.register(Tasks, TasksResourceAdmin)
admin.site.register(CheckInFrequency, CheckInFrequencyResourceAdmin)
admin.site.register(KPICategory, KPICategoryResourceAdmin)
admin.site.register(Review, ReviewResourceAdmin)
admin.site.register(StatusUpdate, StatusUpdateResourceAdmin)
admin.site.register(ActivityLog, ActivityLogResourceAdmin)
admin.site.register(RatingCategory, RatingCategoryResourceAdmin)
admin.site.register(Rating, RatingResourceAdmin)
admin.site.register(Assessment, AssessmentResourceAdmin)
admin.site.register(Appraisal, AppraisalResourceAdmin)
admin.site.register(AppraisalAssessment, AppraisalAssessmentResourceAdmin)
admin.site.register(AssessmentReview, AssessmentReviewResourceAdmin)