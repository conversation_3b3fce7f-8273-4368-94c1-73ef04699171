from django.core.exceptions import ObjectDoesNotExist 
from payroll_app.models import CompanyDepartmentSettings, CompanyEmployeeList
from payroll_app.services import valid_uuid_check
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from performance_management.models import STATUS, ActivityLog, Appraisal, AppraisalAssessment, Assessment, AssessmentReview, CheckInFrequency, CompanyGoals, CompanyKeyResult, KPICategory, KPICheckIn, Rating, RatingCategory, Review, Tasks, StatusUpdate

class CompanyGoalPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid Goal ID.',
        'incorrect_type': 'Incorrect type. Expected a valid Goal ID, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', True)
        # kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        # if not data is None:
        #     return None
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)
        except ValidationError:
            raise ValidationError(
                f"Invalid ID {data}"
            )

class CompanyNullGoalPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid Goal ID.',
        'incorrect_type': 'Incorrect type. Expected a valid Goal ID, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', True)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        # if not data is None:
        #     return None
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)

class CompanyAssignedPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid Employee ID.',
        'incorrect_type': 'Incorrect type. Expected a valid Employee ID, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', False)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        if not data is None:
            return None
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)

class CompanyKeyResultPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid Key Result ID.',
        'incorrect_type': 'Incorrect type. Expected a valid Key Result ID, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', True)
        # kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        # if not data is None:
        #     return None
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)
        except ValidationError:
            raise ValidationError(
                f"Invalid ID {data}"
            )

class CompanyNullKeyResultPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid Key Result ID.',
        'incorrect_type': 'Incorrect type. Expected a valid Key Result ID, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', True)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        # if not data is None:
        #     return None
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)

class CompanyNullKPICategoryPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid KPI Category ID.',
        'incorrect_type': 'Incorrect type. Expected a valid KPI Category, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', False)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        # if not data is None:
        #     return None
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)

class DepartmentNullGoalPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    default_error_messages = {
        'does_not_exist': 'Please provide a valid Department ID.',
        'incorrect_type': 'Incorrect type. Expected a valid Department ID, but got {data_type}.',
    }
    def __init__(self, *args, **kwargs):
        # Set required to False
        kwargs['required'] = kwargs.get('required', False)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        super().__init__(*args, **kwargs)
    def to_internal_value(self, data):
        # if not data is None:
        #     return None
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)

class CompanyGoalSerializer(serializers.ModelSerializer):
    owner = serializers.ListField(
        child=serializers.DictField(child=serializers.CharField()),
        required=False,
        allow_empty=True
    )
    class Meta:
        model = CompanyGoals
        fields = ("id", "name", "description", "period_name", "period_start_date", "period_end_date", "is_active", "is_deleted", "owner", "created_by")

    def validate(self, attrs):
        company_id = self.context.get('company_id')
        owner_data = attrs.pop("owner", [])
        # Process the assign_to data
        counter = 0
        for entry in owner_data:
            this_counter = counter
            number_id = entry.get("id")
            type = entry.get("type")
            if type == "department":
                if not valid_uuid_check(number_id):
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
                department = CompanyDepartmentSettings.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not department:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
            elif type == "employee":
                employee = CompanyEmployeeList.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not employee:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid employee"})
            else:
                raise serializers.ValidationError({f"owner_{this_counter}":  "invalid owner type"})
            counter += 1

        attrs["owner"] = owner_data
        return attrs
    
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        employee_full_name = instance.created_by.full_name if instance.created_by else ""
        serialized_data["owner"] = instance.combined_assignees if instance.goal_type == "COMPANY" else employee_full_name
        return serialized_data
    
class ThisTaskSerializer(serializers.ModelSerializer):
    assign_to = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.all()),
        required=False,  # Make assign_to optional
        allow_empty=True  # Allow empty lists
    )
    class Meta:
        model = Tasks
        fields = ("id", "name", "assign_to", "priority", "start_date", "end_date", "status")


class ThisKeyResultSerializer(serializers.ModelSerializer):
    tasks = ThisTaskSerializer(many=True)
    
    assign_to = CompanyAssignedPrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.all())
    owner = serializers.ListField(
        child=serializers.DictField(child=serializers.CharField()),
        required=False,
        allow_empty=True
    )
    kpi_category = CompanyNullKPICategoryPrimaryKeyRelatedField(queryset=KPICategory.objects.all())
    class Meta:
        model = CompanyKeyResult
        fields = ("id", "name", "description", "period_name", "period_start_date", 
                  "kpi_type", "achieved_value", "period_end_date", "assign_to", "owner", "is_active", 
                  "start_count", "end_count", "percentage", "check_in_frequency",
                  "kpi_category", "from_count", "to_count", "upper_limit", "lower_limit", "tasks")
    
    def validate(self, attrs):
        company_id = self.context.get('company_id')
        # company_goal = self.context.get('company_goal')
        assign_to = attrs.get('assign_to')
        kpi_type = attrs.get('kpi_type')
        start_count = attrs.get('start_count')
        end_count = attrs.get('end_count')
        kpi_category = attrs.get('kpi_category')
        from_count = attrs.get('from_count')
        to_count = attrs.get('to_count')
        upper_limit = attrs.get('upper_limit')
        lower_limit = attrs.get('lower_limit')
        check_in_frequency = attrs.get("check_in_frequency")
        owner_data = attrs.pop("owner", [])
        if not kpi_type:
            raise serializers.ValidationError({"kpi_type":"kpi_type field is required"})
        # if not check_in_frequency:
        #     raise serializers.ValidationError({"check_in_frequency":"check in frequency field is required"})
        # if assign_to:
        #     if assign_to.company.is_deleted:
        #         raise serializers.ValidationError({"assign_to":"invalid employee"})
        #     if str(assign_to.company.id) != str(company_id):
        #         raise serializers.ValidationError({"assign_to":"invalid employee"})
        if kpi_type == "COUNT_TASK_TRACKED":
            if start_count is None:
                raise serializers.ValidationError({"start_count":"Start count cannot be empty"})
            if end_count is None:
                raise serializers.ValidationError({"end_count":"End count cannot be empty"})
            if end_count <= start_count:
                raise serializers.ValidationError({"end_count":"Start count and End Count cannot be same"})
        if kpi_type  == "INCREASE_KPI":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if from_count is None:
                raise serializers.ValidationError({"from_count":"From should not be empty"})
            if to_count is None:
                raise serializers.ValidationError({"to_count":"To should not be empty"})
            if to_count == from_count:
                raise serializers.ValidationError({"to_count":"From and To should not be the same"})
            if to_count < from_count:
                raise serializers.ValidationError({"to_count":"To should not be higher than From"})
        if kpi_type  == "DECREASE_KPI":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if from_count is None:
                raise serializers.ValidationError({"from_count":"From should not be empty"})
            if to_count is None:
                raise serializers.ValidationError({"to_count":"To should not be empty"})
            if from_count == to_count:
                raise serializers.ValidationError({"to_count":"From and To should not be the same"})
            if from_count < to_count:
                raise serializers.ValidationError({"from_count":"From should be higher than To"})
        if kpi_type  == "AT_MOST":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if upper_limit is None:
                raise serializers.ValidationError({"upper_limit":"Upper limit should not be empty"})
        if kpi_type  == "AT_LEAST":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if lower_limit is None:
                raise serializers.ValidationError({"lower_limit":"Lower limit should not be empty"})
        if kpi_type  == "IN_BETWEEN":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if lower_limit is None:
                raise serializers.ValidationError({"lower_limit":"Lower limit should not be empty"})
            if upper_limit is None:
                raise serializers.ValidationError({"upper_limit":"Upper limit should not be empty"})
            if lower_limit == upper_limit:
                raise serializers.ValidationError({"upper_limit":"Lower limit and Upper limit should not be the same"})
            if lower_limit > upper_limit:
                raise serializers.ValidationError({"upper_limit":"Upper limit should be higher than Lower limit"})
            
        # Process the assign_to data
        counter = 0
        for entry in owner_data:
            this_counter = counter
            number_id = entry.get("id")
            type = entry.get("type")
            if type == "department":
                if not valid_uuid_check(number_id):
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
                department = CompanyDepartmentSettings.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not department:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
            elif type == "employee":
                employee = CompanyEmployeeList.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not employee:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid employee"})
            else:
                raise serializers.ValidationError({f"owner_{this_counter}":  "invalid owner type"})
            counter += 1

        attrs["owner"] = owner_data
        attrs["check_in_frequency"] = check_in_frequency
        return attrs

    def create(self, validated_data):
        tasks_data = validated_data.pop('tasks')
        key_result = CompanyKeyResult.objects.create(**validated_data)
        for task_data in tasks_data:
            Tasks.objects.create(key_result=key_result, **task_data)
        return key_result

class GoalSerializer(serializers.ModelSerializer):
    key_results = ThisKeyResultSerializer(many=True)
    owner = serializers.ListField(
        child=serializers.DictField(child=serializers.CharField()),
        required=False,
        allow_empty=True
    )
    class Meta:
        model = CompanyGoals
        fields = ("id", "name", "description", "period_name", "period_start_date", "period_end_date", "key_results", "goal_type", "created_by", "owner")
    
    def validate(self, attrs):
        company_id = self.context.get('company_id')
        goal_start_date = attrs.get('period_start_date')
        goal_end_date = attrs.get('period_end_date')
        key_results_data = attrs.get("key_results")
        owner_data = attrs.pop("owner", [])

        if not goal_start_date:
            raise serializers.ValidationError({"period_start_date":"start date field is required"})
        if not goal_end_date:
            raise serializers.ValidationError({"period_end_date":"end date field is required"})
        if goal_start_date >= goal_end_date:
            raise serializers.ValidationError({"period_start_date":"start date must lesser than end date"})
        
        counter = 0
        # Validate each key result period_start_date and period_end_date
        for key_result_data in key_results_data:
            period_start_date = key_result_data.get('period_start_date')
            period_end_date = key_result_data.get('period_end_date')
            
            if not period_start_date:
                raise serializers.ValidationError({f"period_start_date_{counter}":"start date field is required"})
            if not period_end_date:
                raise serializers.ValidationError({f"period_end_date_{counter}":"end date field is required"})
            if goal_start_date > period_start_date:
                raise serializers.ValidationError({f"period_start_date_{counter}":f"start date must be within {goal_start_date} - {goal_end_date}"})
            if period_start_date > goal_end_date:
                raise serializers.ValidationError({f"period_start_date_{counter}":f"start date must lesser than {goal_end_date}"})
            if period_start_date >= period_end_date:
                raise serializers.ValidationError({f"period_start_date_{counter}":"start date must lesser than end date"})
            if period_end_date > goal_end_date:
                raise serializers.ValidationError({f"period_end_date_{counter}":f"end date must be lesser than {goal_end_date}"})
            
            counter += 1
                # Process the assign_to data
        owner_counter = 0
        for entry in owner_data:
            this_counter = counter
            number_id = entry.get("id")
            type = entry.get("type")
            if type == "department":
                if not valid_uuid_check(number_id):
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
                department = CompanyDepartmentSettings.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not department:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
            elif type == "employee":
                employee = CompanyEmployeeList.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not employee:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid employee"})
            else:
                raise serializers.ValidationError({f"owner_{this_counter}":  "invalid owner type"})
            owner_counter += 1

        attrs["owner"] = owner_data
        return attrs
    
    def create(self, validated_data):
        key_results_data = validated_data.pop('key_results')
        company_id = self.context.get('company_id')
        goal = CompanyGoals.objects.create(**validated_data)
        for key_result_data in key_results_data:
            tasks_data = key_result_data.pop('tasks')


            owner = key_result_data.pop("owner")
            check_in_frequency = key_result_data.pop('check_in_frequency', [])

            key_result = CompanyKeyResult.objects.create(company_goal=goal, company=goal.company, **key_result_data)

            if check_in_frequency:
                key_result.check_in_frequency.set(list(check_in_frequency))

            for entry in owner:
                number_id = entry.get("id")
                type = entry.get("type")
                if type == "department":
                    department = CompanyDepartmentSettings.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                    if department:
                        key_result.department.add(department)
                elif type == "employee":
                    employee = CompanyEmployeeList.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                    if employee:
                        key_result.employees.add(employee)

            for task_data in tasks_data:
                assign_to_data = task_data.pop("assign_to")
                task = Tasks.objects.create(key_result=key_result, company=goal.company,
                                     company_goal=goal, **task_data)
                if assign_to_data:
                    task.assign_to.set(list(assign_to_data))
        return goal
        
class CompanyKeyResultSerializer(serializers.ModelSerializer):
    percentage = serializers.SerializerMethodField()
    assign_to = CompanyAssignedPrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.all())
    owner = serializers.ListField(
        child=serializers.DictField(child=serializers.CharField()),
        required=False,
        allow_empty=True
    )
    company_goal = CompanyGoalPrimaryKeyRelatedField(queryset=CompanyGoals.objects.all())
    kpi_category = CompanyNullKPICategoryPrimaryKeyRelatedField(queryset=KPICategory.objects.all())
    class Meta:
        model = CompanyKeyResult
        fields = ("id", "company_goal", "name", "description", "period_name", "period_start_date", 
                  "kpi_type", "achieved_value", "period_end_date", "assign_to", "owner", "is_active", 
                  "start_count", "end_count", "percentage", "check_in_frequency",
                  "kpi_category", "from_count", "to_count", "upper_limit", "lower_limit", "status")

    def get_percentage(self, obj):
        percentage = round(obj.percentage, 2)
        return percentage
    
    def validate(self, attrs):
        company_id = self.context.get('company_id')
        key_result_type = self.context.get('key_result_type')
        company_goal = attrs.get('company_goal')
        assign_to = attrs.get('assign_to')
        kpi_type = attrs.get('kpi_type')
        start_count = attrs.get('start_count')
        end_count = attrs.get('end_count')
        kpi_category = attrs.get('kpi_category')
        from_count = attrs.get('from_count')
        to_count = attrs.get('to_count')
        upper_limit = attrs.get('upper_limit')
        lower_limit = attrs.get('lower_limit')
        period_start_date = attrs.get("period_start_date")
        period_end_date = attrs.get("period_end_date")
        check_in_frequency = attrs.get("check_in_frequency")
        owner_data = attrs.pop("owner", [])
        
        if company_goal.goal_type != key_result_type:
                raise serializers.ValidationError({"company_goal":"company goal mismatch"})
        if not kpi_type:
            raise serializers.ValidationError({"kpi_type":"kpi_type field is required"})
        if not check_in_frequency:
            raise serializers.ValidationError({"check_in_frequency":"check in frequency field is required"})
        if not period_start_date:
            raise serializers.ValidationError({"period_start_date":"start date field is required"})
        if not period_end_date:
            raise serializers.ValidationError({"period_end_date":"end date field is required"})
        if not company_goal.period_start_date:
            raise serializers.ValidationError({"company_goal":"company goal does not have a start date"})
        if not company_goal.period_end_date:
            raise serializers.ValidationError({"company_goal":"company goal does not have a end date"})
        if company_goal.period_start_date > period_start_date:
            raise serializers.ValidationError({"period_start_date":f"start date must be within {company_goal.period_start_date} - {company_goal.period_end_date}"})
        if period_start_date > company_goal.period_end_date:
            raise serializers.ValidationError({"period_start_date":f"start date must lesser than {company_goal.period_end_date}"})
        if period_start_date >= period_end_date:
            raise serializers.ValidationError({"period_start_date":"start date must lesser than end date"})
        if period_end_date > company_goal.period_end_date:
            raise serializers.ValidationError({"period_end_date":f"end date must be lesser than {company_goal.period_end_date}"})
        # if assign_to:
        #     if assign_to.company.is_deleted:
        #         raise serializers.ValidationError({"assign_to":"invalid employee"})
        #     if str(assign_to.company.id) != str(company_id):
        #         raise serializers.ValidationError({"assign_to":"invalid employee"})
        if kpi_type == "COUNT_TASK_TRACKED":
            if start_count is None:
                raise serializers.ValidationError({"start_count":"Start count cannot be empty"})
            if end_count is None:
                raise serializers.ValidationError({"end_count":"End count cannot be empty"})
            if end_count <= start_count:
                raise serializers.ValidationError({"end_count":"Start count and End Count cannot be same"})
        if kpi_type  == "INCREASE_KPI":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if from_count is None:
                raise serializers.ValidationError({"from_count":"From should not be empty"})
            if to_count is None:
                raise serializers.ValidationError({"to_count":"To should not be empty"})
            if to_count == from_count:
                raise serializers.ValidationError({"to_count":"From and To should not be the same"})
            if to_count < from_count:
                raise serializers.ValidationError({"to_count":"To should not be higher than From"})
        if kpi_type  == "DECREASE_KPI":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if from_count is None:
                raise serializers.ValidationError({"from_count":"From should not be empty"})
            if to_count is None:
                raise serializers.ValidationError({"to_count":"To should not be empty"})
            if from_count == to_count:
                raise serializers.ValidationError({"to_count":"From and To should not be the same"})
            if from_count < to_count:
                raise serializers.ValidationError({"from_count":"From should be higher than To"})
        if kpi_type  == "AT_MOST":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if upper_limit is None:
                raise serializers.ValidationError({"upper_limit":"Upper limit should not be empty"})
        if kpi_type  == "AT_LEAST":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if lower_limit is None:
                raise serializers.ValidationError({"lower_limit":"Lower limit should not be empty"})
        if kpi_type  == "IN_BETWEEN":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if lower_limit is None:
                raise serializers.ValidationError({"lower_limit":"Lower limit should not be empty"})
            if upper_limit is None:
                raise serializers.ValidationError({"upper_limit":"Upper limit should not be empty"})
            if lower_limit == upper_limit:
                raise serializers.ValidationError({"upper_limit":"Lower limit and Upper limit should not be the same"})
            if lower_limit > upper_limit:
                raise serializers.ValidationError({"upper_limit":"Upper limit should be higher than Lower limit"})
            
        # Process the assign_to data
        counter = 0
        for entry in owner_data:
            this_counter = counter
            number_id = entry.get("id")
            type = entry.get("type")
            if type == "department":
                if not valid_uuid_check(number_id):
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
                department = CompanyDepartmentSettings.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not department:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
            elif type == "employee":
                employee = CompanyEmployeeList.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not employee:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid employee"})
            else:
                raise serializers.ValidationError({f"owner_{this_counter}":  "invalid owner type"})
            counter += 1

        attrs["owner"] = owner_data
        attrs["check_in_frequency"] = check_in_frequency
        return attrs
    
    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["owner"] = instance.combined_assignees
        return serialized_data
    
class EditCompanyKeyResultSerializer(serializers.ModelSerializer):
    percentage = serializers.SerializerMethodField()
    assign_to = CompanyAssignedPrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.all())
    owner = serializers.ListField(
        child=serializers.DictField(child=serializers.CharField()),
        required=False,
        allow_empty=True
    )
    kpi_category = CompanyNullKPICategoryPrimaryKeyRelatedField(queryset=KPICategory.objects.all())
    class Meta:
        model = CompanyKeyResult
        fields = ("id", "name", "description", "period_name", "period_start_date", 
                  "kpi_type", "achieved_value", "period_end_date", "assign_to", "owner", "is_active", 
                  "start_count", "end_count", "percentage", "check_in_frequency",
                  "kpi_category", "from_count", "to_count", "upper_limit", "lower_limit", "is_deleted")

    def get_percentage(self, obj):
        percentage = round(obj.percentage, 2)
        return percentage
    
    def validate(self, attrs):
        company_id = self.context.get('company_id')
        company_goal = self.context.get('company_goal')
        assign_to = attrs.get('assign_to')
        kpi_type = attrs.get('kpi_type')
        start_count = attrs.get('start_count')
        end_count = attrs.get('end_count')
        kpi_category = attrs.get('kpi_category')
        from_count = attrs.get('from_count')
        to_count = attrs.get('to_count')
        upper_limit = attrs.get('upper_limit')
        lower_limit = attrs.get('lower_limit')
        period_start_date = attrs.get("period_start_date")
        period_end_date = attrs.get("period_end_date")
        check_in_frequency = attrs.get("check_in_frequency")
        owner_data = attrs.pop("owner", [])
        if not kpi_type:
            raise serializers.ValidationError({"kpi_type":"kpi_type field is required"})
        if not check_in_frequency:
            raise serializers.ValidationError({"check_in_frequency":"check in frequency field is required"})
        if not period_start_date:
            raise serializers.ValidationError({"period_start_date":"start date field is required"})
        if not period_end_date:
            raise serializers.ValidationError({"period_end_date":"end date field is required"})
        if not company_goal.period_start_date:
            raise serializers.ValidationError({"company_goal":"company goal does not have a start date"})
        if not company_goal.period_end_date:
            raise serializers.ValidationError({"company_goal":"company goal does not have a end date"})
        if company_goal.period_start_date > period_start_date:
            raise serializers.ValidationError({"period_start_date":f"start date must be within {company_goal.period_start_date} - {company_goal.period_end_date}"})
        if period_start_date > company_goal.period_end_date:
            raise serializers.ValidationError({"period_start_date":f"start date must lesser than {company_goal.period_end_date}"})
        if period_start_date >= period_end_date:
            raise serializers.ValidationError({"period_start_date":"start date must lesser than end date"})
        if period_end_date > company_goal.period_end_date:
            raise serializers.ValidationError({"period_end_date":f"end date must be lesser than {company_goal.period_end_date}"})
        # if assign_to:
        #     if assign_to.company.is_deleted:
        #         raise serializers.ValidationError({"assign_to":"invalid employee"})
        #     if str(assign_to.company.id) != str(company_id):
        #         raise serializers.ValidationError({"assign_to":"invalid employee"})
        if kpi_type == "COUNT_TASK_TRACKED":
            if start_count is None:
                raise serializers.ValidationError({"start_count":"Start count cannot be empty"})
            if end_count is None:
                raise serializers.ValidationError({"end_count":"End count cannot be empty"})
            if end_count <= start_count:
                raise serializers.ValidationError({"end_count":"Start count and End Count cannot be same"})
        if kpi_type  == "INCREASE_KPI":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if from_count is None:
                raise serializers.ValidationError({"from_count":"From should not be empty"})
            if to_count is None:
                raise serializers.ValidationError({"to_count":"To should not be empty"})
            if to_count == from_count:
                raise serializers.ValidationError({"to_count":"From and To should not be the same"})
            if to_count < from_count:
                raise serializers.ValidationError({"to_count":"To should not be higher than From"})
        if kpi_type  == "DECREASE_KPI":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if from_count is None:
                raise serializers.ValidationError({"from_count":"From should not be empty"})
            if to_count is None:
                raise serializers.ValidationError({"to_count":"To should not be empty"})
            if from_count == to_count:
                raise serializers.ValidationError({"to_count":"From and To should not be the same"})
            if from_count < to_count:
                raise serializers.ValidationError({"from_count":"From should be higher than To"})
        if kpi_type  == "AT_MOST":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if upper_limit is None:
                raise serializers.ValidationError({"upper_limit":"Upper limit should not be empty"})
        if kpi_type  == "AT_LEAST":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if lower_limit is None:
                raise serializers.ValidationError({"lower_limit":"Lower limit should not be empty"})
        if kpi_type  == "IN_BETWEEN":
            if not kpi_category:
                raise serializers.ValidationError({"kpi_category": "kpi category is required"})
            if kpi_category.company:
                if str(kpi_category.company.id) != str(company_id):
                    raise serializers.ValidationError({"kpi_category":"invalid kpi category"})
            if lower_limit is None:
                raise serializers.ValidationError({"lower_limit":"Lower limit should not be empty"})
            if upper_limit is None:
                raise serializers.ValidationError({"upper_limit":"Upper limit should not be empty"})
            if lower_limit == upper_limit:
                raise serializers.ValidationError({"upper_limit":"Lower limit and Upper limit should not be the same"})
            if lower_limit > upper_limit:
                raise serializers.ValidationError({"upper_limit":"Upper limit should be higher than Lower limit"})
            
        # Process the assign_to data
        counter = 0
        for entry in owner_data:
            this_counter = counter
            number_id = entry.get("id")
            type = entry.get("type")
            if type == "department":
                if not valid_uuid_check(number_id):
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
                department = CompanyDepartmentSettings.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not department:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid department"})
            elif type == "employee":
                employee = CompanyEmployeeList.objects.filter(id=number_id, company__id=company_id, is_deleted=False).first()
                if not employee:
                    raise serializers.ValidationError({f"owner_{this_counter}": "invalid employee"})
            else:
                raise serializers.ValidationError({f"owner_{this_counter}":  "invalid owner type"})
            counter += 1

        attrs["owner"] = owner_data
        attrs["check_in_frequency"] = check_in_frequency
        return attrs

class KeyResultSerializer(serializers.ModelSerializer):
    percentage = serializers.SerializerMethodField()
    company_goal = CompanyGoalPrimaryKeyRelatedField(queryset=CompanyGoals.objects.all())
    assign_to = CompanyAssignedPrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.all())
    owner = serializers.SerializerMethodField()
    class Meta:
        model = CompanyKeyResult
        fields = ("id", "company_goal", "name", "description", "period_name", "period_start_date", 
                  "kpi_type", "achieved_value", "period_end_date", "assign_to", "owner", "is_active", 
                  "start_count", "end_count", "percentage", "check_in_frequency", "status",
                  "kpi_category", "from_count", "to_count", "upper_limit", "lower_limit")

    def get_percentage(self, obj):
        percentage = round(obj.percentage, 2)
        return percentage
    
    def get_owner(self, obj):
        return obj.combined_assignees
    
    # def to_representation(self, instance):
    #     representation = super().to_representation(instance)
    #     key_feature_tasks =  Tasks.objects.filter(key_result=instance, is_deleted=False)
    #     tasks = KeyResultSerializer(key_feature_tasks, many=True)
    #     representation["tasks"] = tasks.data
    #     return representation

class SubKeyResultSerializer(serializers.ModelSerializer):
    percentage = serializers.SerializerMethodField()
    company_goal = CompanyGoalPrimaryKeyRelatedField(queryset=CompanyGoals.objects.all())
    assign_to = CompanyAssignedPrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.all())
    owner = serializers.SerializerMethodField()
    class Meta:
        model = CompanyKeyResult
        fields = ("id", "company_goal", "name", "description", "period_name", "period_start_date", 
                  "kpi_type", "achieved_value", "period_end_date", "assign_to", "owner", "is_active", 
                  "start_count", "end_count", "percentage", "check_in_frequency", "status",
                  "kpi_category", "from_count", "to_count", "upper_limit", "lower_limit")

    def get_percentage(self, obj):
        percentage = round(obj.percentage, 2)
        return percentage
    
    def get_owner(self, obj):
        return obj.combined_assignees
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["goal_name"] = instance.company_goal.name
        return representation
    
class CompanyGoalDataSerializer(serializers.ModelSerializer):
    percentage = serializers.SerializerMethodField()
    class Meta:
        model = CompanyGoals
        fields = ("id", "name", "description", "period_name", "period_start_date", "period_end_date", "is_active", "percentage", "created_by")

    def get_percentage(self, obj):
        percentage = round(obj.percentage, 2)
        return percentage
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        company_key_feature =  CompanyKeyResult.objects.filter(company_goal=instance, is_deleted=False)
        key_results = KeyResultSerializer(company_key_feature, many=True)
        representation["key_results"] = key_results.data
        employee_full_name = instance.created_by.full_name if instance.created_by else ""
        representation["owner"] = instance.combined_assignees if instance.goal_type == "COMPANY" else employee_full_name
        return representation
    
class CheckInSerializer(serializers.ModelSerializer):
    status = serializers.ChoiceField(required=True, choices=STATUS)
    key_result = CompanyKeyResultPrimaryKeyRelatedField(queryset=CompanyKeyResult.objects.all())
    achieved_value = serializers.FloatField(min_value=0, required=False, allow_null=True)
    class Meta:
        model = KPICheckIn
        fields = ("id", "key_result", "achieved_value", "comments", "check_in_date", "status", "updated_at", "created_by")

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["created_by_name"] = instance.created_by.full_name if instance.created_by else None
        return representation

    def validate(self, attrs):
        key_result = attrs.get('key_result')
        status = attrs.get('status')
        company_id = self.context.get('company_id')
        achieved_value = attrs.get('achieved_value')
        employee = self.context.get('employee')

        # Validate key result's company
        if key_result.company.is_deleted:
            raise serializers.ValidationError({"message": "Invalid key result"})
        if str(key_result.company.id) != str(company_id):
            raise serializers.ValidationError({"message": "Invalid key result: mismatched company ID."})

        # Validate KPI types
        kpi_type = key_result.kpi_type
        task_tracked_kpi_types = {"PERCENTAGE_TASK_TRACKED", "COUNT_TASK_TRACKED"}
        value_required_kpi_types = {
            "PERCENTAGE_KPI", "INCREASE_KPI", "DECREASE_KPI", "AT_MOST", "AT_LEAST", "IN_BETWEEN"
        }

        if kpi_type in task_tracked_kpi_types:
            raise serializers.ValidationError({
                "achieved_value": "The progress of this key result is based on task completion."
            })

        if kpi_type in value_required_kpi_types and achieved_value is None:
            raise serializers.ValidationError({
                "achieved_value": "Enter a valid value for this KPI type."
            })
        attrs["created_by"] = employee
        return attrs
    
    def create(self, validated_data):
        key_result = validated_data['key_result']
        check_in_date = validated_data['check_in_date']
        achieved_value = validated_data.get('achieved_value')
        status = validated_data.get('status')

        # Check if a record with the same key_result and check_in_date exists
        existing_check_in = KPICheckIn.objects.filter(
            key_result=key_result,
            check_in_date=check_in_date
        ).first()

        old_status = key_result.status
        old_achieved_value = key_result.achieved_value
        old_percentage = key_result.percentage
        old_comment = existing_check_in.comments if existing_check_in else None
        old_created_by = existing_check_in.created_by if existing_check_in else None

        new_status = status
        if old_status != new_status:
            set_update = True
            status_string = f"{old_status} - {new_status}"
        else:
            set_update = False
            status_string = f"Set as {old_status}"
        if existing_check_in:
            # Update the existing record
            for attr, value in validated_data.items():
                setattr(existing_check_in, attr, value)
            self._update_key_result_if_latest(existing_check_in, key_result, achieved_value, status)
            existing_check_in.save()

            status_update = StatusUpdate.objects.filter(check_in_date=check_in_date).first()
            if status_update:
                ActivityLog.objects.create(
                    status_update=status_update,
                    status=old_status,
                    percentage=old_percentage,
                    achieved_value=old_achieved_value,
                    comments=old_comment,
                    created_by=old_created_by
                )  
            return existing_check_in

        # Create a new record if no match is found
        new_check_in = KPICheckIn.objects.create(**validated_data)
        get_percentage = self._update_key_result_if_latest(new_check_in, key_result, achieved_value, status)
        # this_result = CompanyKeyResult.objects.get(id=key_result.id)
        StatusUpdate.objects.create(
            key_result=key_result,
            status=status_string,
            percentage=key_result.percentage,
            check_in_date=check_in_date
        )
        return new_check_in

    def _update_key_result_if_latest(self, check_in, key_result, achieved_value, status):
        """
        Updates the key_result's achieved_value and status only if the check-in
        is the most recent by date.
        """
        latest_check_in = KPICheckIn.objects.filter(key_result=key_result).order_by('-check_in_date').first()

        if latest_check_in and latest_check_in.id == check_in.id:
            key_result.achieved_value = achieved_value
            key_result.status = status
            key_result.save()      

class CompanyKeyResultTaskSerializer(serializers.ModelSerializer):
    assign_to = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.all()),
        required=False,  # Make assign_to optional
        allow_empty=True  # Allow empty lists
    )
    # company_goal = CompanyNullGoalPrimaryKeyRelatedField(queryset=CompanyGoals.objects.all())
    key_result = CompanyKeyResultPrimaryKeyRelatedField(queryset=CompanyKeyResult.objects.all())
    class Meta:
        model = Tasks
        fields = ("id", "key_result", "name", "assign_to", "priority", "start_date", "end_date", "status", "task_type")

    def validate(self, attrs):
        key_result = attrs.get('key_result')
        company_id = self.context.get('company_id')
        task_type = self.context.get('task_type')
        if key_result:
            # Validate key result's company
            if key_result.company.is_deleted:
                raise serializers.ValidationError({"message": "Invalid key result"})
            if key_result.company_goal.goal_type != task_type:
                raise serializers.ValidationError({"message": "Task and Key Result must be of the same type"})
            if str(key_result.company.id) != str(company_id):
                raise serializers.ValidationError({"message": "Invalid key result: mismatched company ID."})
        return attrs

class CompanyTaskSerializer(serializers.ModelSerializer):
    assign_to = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.all()),
        required=False,  # Make assign_to optional
        allow_empty=True  # Allow empty lists
    )
    class Meta:
        model = Tasks
        fields = ("id", "name", "assign_to", "priority", "start_date", "end_date", "status", "is_deleted")
    
class TaskSerializer(serializers.ModelSerializer):
    # company_goal = CompanyNullGoalPrimaryKeyRelatedField(queryset=CompanyGoals.objects.all())
    # key_result = CompanyKeyResultPrimaryKeyRelatedField(queryset=CompanyKeyResult.objects.all())
    class Meta:
        model = Tasks
        fields = ("id", "key_result", "name", "assign_to", "priority", "start_date", "end_date", "status")

    def to_representation(self, instance):
        serialized_data = super().to_representation(
            instance
        )
        serialized_data["assign_to"] = instance.get_assign_to
        return serialized_data
    
class CheckInFrequencySerializer(serializers.ModelSerializer):
    class Meta:
        model = CheckInFrequency
        fields = ("id", "name", "is_deleted")

class KPICategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = KPICategory
        fields = ("id", "name", "type", "is_deleted")

class AlignGoalSerializer(serializers.ModelSerializer):
    goal_to_align = CompanyGoalPrimaryKeyRelatedField(queryset=CompanyGoals.objects.all())
    aligned_goals = CompanyGoalPrimaryKeyRelatedField(
        many=True, queryset=CompanyGoals.objects.filter(is_deleted=False)
    )
    class Meta:
        model = CompanyGoals
        fields = ("id", "goal_to_align", "aligned_goals")

    def validate(self, attrs):
        # Ensure alignment is valid
        goal_to_align = attrs.get('goal_to_align')
        aligned_goals = attrs.get('aligned_goals')
        # company_id = self.context.get("company_id")

        for goal in aligned_goals:
            if goal.goal_type == "INDIVIDUAL":
                raise serializers.ValidationError(
                    {"aligned_goals": "cannot align with individual goals"} 
                )
            if str(goal.id) == str(goal_to_align.id):
                raise serializers.ValidationError(
                    {"aligned_goals": "cannot align the same goals"} 
                )
            if str(goal_to_align.company.id) != str(goal.company.id):
                raise serializers.ValidationError(
                    {"aligned_goals": f"{goal.name} must belong to the same company"} 
                )
        for goal in aligned_goals:
            goal.aligned_goals.clear()
            goal.aligned_goals.add(goal_to_align)
        return attrs

class AlignKeyResultSerializer(serializers.ModelSerializer):
    key_result_to_align = CompanyKeyResultPrimaryKeyRelatedField(queryset=CompanyKeyResult.objects.all())
    aligned_goals = CompanyGoalPrimaryKeyRelatedField(
        many=True, queryset=CompanyGoals.objects.filter(is_deleted=False)
    )
    aligned_key_results = CompanyKeyResultPrimaryKeyRelatedField(
        many=True, queryset=CompanyKeyResult.objects.filter(is_deleted=False)
    )
    class Meta:
        model = CompanyKeyResult
        fields = ("id", "key_result_to_align", "aligned_key_results", "aligned_goals")

    def validate(self, attrs):
        # Ensure alignment is valid
        key_result_to_align = attrs.get('key_result_to_align')
        aligned_goals = attrs.get('aligned_goals', [])
        aligned_key_results = attrs.get('aligned_key_results', [])
        # company_id = self.context.get("company_id")

        for goal in aligned_goals:
            if goal.goal_type == "INDIVIDUAL":
                raise serializers.ValidationError(
                    {"aligned_goals": "cannot align with individual goals"} 
                )
            if str(key_result_to_align.company.id) != str(goal.company.id):
                raise serializers.ValidationError(
                    {"aligned_goals": f"{goal.name} must belong to the same company"} 
                )
            
        for key_result in aligned_key_results:
            if key_result.company_goal.goal_type == "INDIVIDUAL":
                raise serializers.ValidationError(
                    {"aligned_key_results": "cannot align with individual key result"} 
                )
            if str(key_result.id) == str(key_result_to_align.id):
                raise serializers.ValidationError(
                    {"aligned_goals": "cannot align the same key result"} 
                )
            if str(key_result_to_align.company.id) != str(key_result_to_align.company.id):
                raise serializers.ValidationError(
                    {"aligned_goals": f"{goal.name} must belong to the same company"} 
                )
        
        # aligned_goals = CompanyGoals.objects.filter(
        #     id__in=[goal.id for goal in aligned_goals], company=key_result_to_align.company, is_deleted=False
        # )
            
        # key_result_to_align.aligned_goals.set(aligned_goals)

        for key_result in aligned_key_results:
            key_result.aligned_key_results.clear()
            key_result.aligned_key_results.add(key_result_to_align)
            
        all_company_goals = CompanyGoals.objects.filter(company=key_result_to_align.company, goal_type="COMPANY")
        for company_goals in all_company_goals:
            company_goals.aligned_key_results.remove(key_result)
        for goal in aligned_goals:
            goal.aligned_key_results.add(key_result_to_align)
        return attrs

class AlignedKeyResultSerializer(serializers.ModelSerializer):
    percentage = serializers.SerializerMethodField()
    company_goal = CompanyGoalPrimaryKeyRelatedField(queryset=CompanyGoals.objects.all())
    assign_to = CompanyAssignedPrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.all())
    owner = serializers.SerializerMethodField()
    class Meta:
        model = CompanyKeyResult
        fields = ("id", "company_goal", "name", "description", "period_name", "period_start_date", 
                  "kpi_type", "achieved_value", "period_end_date", "assign_to", "owner", "is_active", 
                  "start_count", "end_count", "percentage", "check_in_frequency", "kpi_category",
                   "from_count", "to_count", "upper_limit", "lower_limit", "aligned_key_results", "status")

    def get_percentage(self, obj):
        percentage = round(obj.percentage, 2)
        return percentage
    
    def get_owner(self, obj):
        return obj.combined_assignees
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        company_key_feature =  instance.aligned_key_results.all()
        key_results = KeyResultSerializer(company_key_feature, many=True)
        representation["aligned_key_results"] = key_results.data
        return representation
    
class CompanyGoalOverviewSerializer(serializers.ModelSerializer):
    percentage = serializers.SerializerMethodField()
    class Meta:
        model = CompanyGoals
        fields = ("id", "name", "description", "period_name", "period_start_date", "period_end_date", "is_active", "percentage", "aligned_goals", "aligned_key_results", "created_by")

    def get_percentage(self, obj):
        percentage = round(obj.percentage, 2)
        return percentage
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        company_key_feature = CompanyKeyResult.objects.filter(company_goal=instance, is_deleted=False)
        aligned_goals = CompanyGoalDataSerializer(instance.aligned_goals.all(), many=True)
        key_results = AlignedKeyResultSerializer(company_key_feature, many=True)
        aligned_key_results = AlignedKeyResultSerializer(instance.aligned_key_results.all(), many=True)
        representation["key_results"] = key_results.data
        representation["aligned_goals"] = aligned_goals.data
        representation["aligned_key_results"] = aligned_key_results.data
        employee_full_name = instance.created_by.full_name if instance.created_by else ""
        representation["owner"] = instance.combined_assignees if instance.goal_type == "COMPANY" else employee_full_name
        representation["created_by_name"] = instance.created_by.full_name if instance.created_by else None
        return representation
    
class ReviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = Review
        fields = ("id", "title", "progress", "plan", "problems", "created_by", "is_deleted", "created_at")
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["created_by_name"] = instance.created_by.full_name if instance.created_by else None
        return representation

class StatusUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = StatusUpdate
        fields = ("id", "key_result", "check_in_date", "status", "percentage")
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["name"] = instance.key_result.name if instance.key_result else ""
        return representation
    
class ActivityLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = ActivityLog
        fields = ("id", "achieved_value", "comments", "percentage", "status", "status_update", "created_by")

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["check_in_date"] = instance.status_update.check_in_date if instance.status_update else None 
        representation["created_by_name"] = instance.created_by.full_name if instance.created_by else None
        return representation

class ThisAssessmentSerializer(serializers.ModelSerializer):
    rating = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=Rating.objects.filter(is_active=True)),
        required=True,  
        allow_empty=True
    )
    rating_category = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=RatingCategory.objects.filter(is_active=True)),
        required=True,  
        allow_empty=True
    )

    class Meta:
        model = Assessment
        fields = ("id", "description", "rating", "rating_category")

        extra_kwargs = {
            "description": {"required": True},
        }

    def validate(self, attrs):
        description = attrs.get('description')
        if not description:
            raise serializers.ValidationError({"description": "Description field is required"})
        return attrs

class AppraisalSerializer(serializers.ModelSerializer):
    review_assessment = ThisAssessmentSerializer(many=True)
    access_list = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.filter(is_deleted=False, is_active=True)),
        required=True,
        allow_empty=False
    )
    peer_reviewer = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=CompanyEmployeeList.objects.filter(is_deleted=False, is_active=True)),
        required=True,
        allow_empty=False
    )
    access = DepartmentNullGoalPrimaryKeyRelatedField(
        queryset=CompanyDepartmentSettings.objects.filter(is_deleted=False, is_active=True),
    )
    class Meta:
        model = Appraisal
        fields = ("id", "period_name", "period_start_date", "period_end_date", "access", "access_list", "created_by", "peer_reviewer", "review_assessment")

        extra_kwargs = {
            "period_name": {"required": True, "allow_blank": False},
            "period_start_date": {"required": True},
            "period_end_date": {"required": True},
            "access": {"required": True},
        }
    
    def validate(self, attrs):
        company_id = self.context.get('company_id')
        period_start_date = attrs.get('period_start_date')
        period_end_date = attrs.get('period_end_date')
        access = attrs.get("access")
        access_list = attrs.pop("access_list", [])
        peer_reviewer = attrs.pop("peer_reviewer", [])

        if period_start_date >= period_end_date:
            raise serializers.ValidationError({"period_start_date":"start date must lesser than end date"})
        
        access_list_counter = 0
        for employee in access_list:
            this_counter = access_list_counter
            
            if employee.is_deleted or str(employee.company.id) != str(company_id):
                raise serializers.ValidationError({f"employee_{this_counter}": "employee does not exist"})

            if access:
                if employee.employee_department != access:
                    raise serializers.ValidationError({f"employee_{this_counter}": "employee does not belong to this department"})
                    
            this_counter += 1
        
        peer_reviewer_counter = 0  
        for employee in peer_reviewer:
            this_counter = peer_reviewer_counter
            if employee.is_deleted or str(employee.company.id) != str(company_id):
                raise serializers.ValidationError({f"employee_{this_counter}": "employee does not exist"})                   
            this_counter += 1

        attrs["access_list"] = access_list
        attrs["peer_reviewer"] = peer_reviewer
        return attrs
    
    def create(self, validated_data):
        review_assessment_data = validated_data.pop('review_assessment', [])
        access_list = validated_data.pop('access_list', [])
        peer_reviewer = validated_data.pop('peer_reviewer', [])

        appraisal = Appraisal.objects.create(**validated_data)

        # Assign access_list and peer_reviewer if they are ManyToMany fields
        appraisal.access_list.set(access_list)
        appraisal.peer_reviewer.set(peer_reviewer)

        for reviewer in peer_reviewer:
            # For each peer reviewer, create all assessments
            assessments = []
            for assessment_data in review_assessment_data:
                assessment_data["created_by"] = appraisal.created_by
                assessment_data["company"] = appraisal.company
                rating_data = assessment_data.pop("rating", [])
                rating_category_data = assessment_data.pop("rating_category", [])
                assessment = Assessment.objects.create(**assessment_data)
                assessment.rating.set(rating_data)
                assessment.rating_category.set(rating_category_data)
                assessments.append(assessment)

            for access in access_list:
                assessment_review_list = []
                for assessment in assessments:
                    assessment_review = AssessmentReview.objects.create(
                        appraisal=appraisal,
                        assessment=assessment,
                        employee=reviewer,
                        appraisal_for=access
                    )
                    assessment_review_list.append(assessment_review)

                # Create one AppraisalAssessment per (reviewer, access)
                appraisal_assessment = AppraisalAssessment.objects.create(
                    appraisal=appraisal,
                    company=appraisal.company,
                    employee=reviewer,
                    appraisal_for=access
                )
                appraisal_assessment.assessment_review.set(assessment_review_list)
        # Create related review_assessment entries
        # for assessment_data in review_assessment_data:
        #     assessment_data["created_by"] = appraisal.created_by
        #     assessment_data["company"] = appraisal.company
        #     rating_data = assessment_data.pop("rating", [])
        #     rating_category_data = assessment_data.pop("rating_category", [])
        #     assessment = Assessment.objects.create(**assessment_data)
        #     assessment.rating.set(rating_data)
        #     assessment.rating_category.set(rating_category_data)
        #     appraisal.review_assessment.add(assessment)
        #     appraisal.save()

        #     for access in access_list:
        #         for employee in peer_reviewer:
        #             AppraisalAssessment.objects.create(
        #                 appraisal=appraisal,
        #                 assessment=assessment,
        #                 company=appraisal.company,
        #                 employee=employee,
        #                 appraisal_for=access
        #             )
        return appraisal
    
    def update(self, instance, validated_data):
        review_assessment_data = validated_data.pop('review_assessment', [])
        access_list = validated_data.pop('access_list', [])
        peer_reviewer = validated_data.pop('peer_reviewer', [])

        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update M2M relationships
        instance.access_list.set(access_list)
        instance.peer_reviewer.set(peer_reviewer)

        # Keep track of assessment IDs that are processed
        existing_assessments = {str(a.id): a for a in instance.review_assessment.all()}
        processed_assessment_ids = []

        for assessment_data in review_assessment_data:
            rating_data = assessment_data.pop("rating", [])
            rating_category_data = assessment_data.pop("rating_category", [])
            assessment_id = str(assessment_data.get("id", ""))

            if assessment_id and assessment_id in existing_assessments:
                # Update existing assessment
                assessment = existing_assessments[assessment_id]
                for attr, value in assessment_data.items():
                    setattr(assessment, attr, value)
                assessment.save()
                assessment.rating.set(rating_data)
                assessment.rating_category.set(rating_category_data)
                processed_assessment_ids.append(assessment.id)
            else:
                # Create new assessment
                assessment_data["created_by"] = instance.created_by
                assessment_data["company"] = instance.company
                assessment = Assessment.objects.create(**assessment_data)
                assessment.rating.set(rating_data)
                assessment.rating_category.set(rating_category_data)
                instance.review_assessment.add(assessment)
                processed_assessment_ids.append(assessment.id)

        # Remove assessments that were not included in the update
        for assessment in instance.review_assessment.all():
            if assessment.id not in processed_assessment_ids:
                instance.review_assessment.remove(assessment)
                assessment.delete()

        return instance
        
class AppraisalOverviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = Appraisal
        fields = ("id", "period_name", "period_start_date", "period_end_date", "access", "access_list", "created_by", "peer_reviewer", "review_assessment")
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["access_name"] = instance.access.department_name if instance.access else ""
        representation["access_list"] = instance.all_access_asignees
        representation["peer_reviewer"] = instance.all_peer_reviewers
        representation["review_assessment"] = instance.all_review_assessments
        representation["created_by_name"] = instance.created_by.full_name if instance.created_by else None
        return representation
    
class AppraisalAssessmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppraisalAssessment
        fields = ("id", "assessment_review", "appraisal", "status", "employee", "appraisal_for", "created_at")
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["period_name"] = instance.appraisal.period_name if instance.appraisal else ""
        representation["period_start_date"] = instance.appraisal.period_start_date if instance.appraisal else ""
        representation["period_end_date"] = instance.appraisal.period_end_date if instance.appraisal else ""
        representation["employee_name"] = instance.employee.full_name if instance.employee else ""
        representation["appraisal_for_name"] = instance.appraisal_for.full_name if instance.appraisal_for else ""
        representation["created_by_name"] = instance.appraisal.created_by.full_name

        accessment_review = []
        all_reviews = instance.assessment_review.all()
        for reviews in all_reviews:
            assessment_attributes = []
            for attributes in reviews.assessment.rating_category.all():
                
                assessment_attributes.append(
                    {
                        "rating_category_name": attributes.name,
                        "rating_category": attributes.id,
                        "ratings": [
                            {
                                "id": rating.id,
                                "name": rating.name
                            } for rating in reviews.assessment.rating.filter(rating_category=attributes)
                        ]
                    }
                      
                )
            accessment_review.append(
                {
                    "id": reviews.id,
                    "core_value": reviews.assessment.description if reviews.assessment else "",
                    "rating": reviews.rating.name if reviews.rating else "",
                    "rating_category": reviews.rating_category.name if reviews.rating_category else "",
                    "assessment_attributes": assessment_attributes

                }
            )
        representation["assessment_review"] = accessment_review
        
        return representation


class ThisAssessmentReviewSerializer(serializers.ModelSerializer):
    id = serializers.PrimaryKeyRelatedField(queryset=AssessmentReview.objects.all())
    rating = serializers.PrimaryKeyRelatedField(queryset=Rating.objects.all())
    rating_category = serializers.PrimaryKeyRelatedField(queryset=RatingCategory.objects.all())

    class Meta:
        model = AssessmentReview
        fields = ("id", "rating", "rating_category")

    def validate(self, attrs):
        appraisal_assessment = self.context.get('appraisal_assessment')

        rating_category = attrs.get('rating_category')
        rating = attrs.get('rating')

        for assessment_review in appraisal_assessment.assessment_review.all():

            if not assessment_review.assessment.rating_category.filter(id=rating_category.id).exists():
                raise serializers.ValidationError({"rating_category": "Invalid rating category for this assessment."})
            if assessment_review.assessment.rating.filter(rating_category=rating_category).exists():
                if not assessment_review.assessment.rating.filter(id=rating.id).exists():
                    raise serializers.ValidationError({"rating": "Rating does not match the rating category."})
                            
        return attrs

class SubmitAppraisalAssessmentSerializer(serializers.ModelSerializer):
    assessment_review = ThisAssessmentReviewSerializer(many=True)
    class Meta:
        model = AppraisalAssessment
        fields = ("id", "assessment_review")

    def validate(self, attrs):
        appraisal_assessment = self.context.get('appraisal_assessment')
        assessment_review_data = attrs.get('assessment_review')

        if not appraisal_assessment:
            raise serializers.ValidationError({"appraisal_assessment": "Appraisal assessment is required."})

        if not assessment_review_data:
            raise serializers.ValidationError({"assessment_review": "At least one assessment review is required."})

        for assessment_review in appraisal_assessment.assessment_review.all():
            review_id = str(assessment_review.id)
            if not any(str(review.get("id").id) == review_id for review in assessment_review_data):
                raise serializers.ValidationError({
                    "assessment_review": f"Review with ID {review_id} is missing."
                })

        return attrs

