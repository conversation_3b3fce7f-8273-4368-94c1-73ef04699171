from import_export import resources, fields
from performance_management.models import ActivityLog, Appraisal, AppraisalAssessment, Assessment, AssessmentReview, CheckInFrequency, CompanyGoals, CompanyKeyResult, KPICategory, KPICheckIn, Rating, RatingCategory, Review, Tasks, StatusUpdate

class CompanyGoalsResource(resources.ModelResource):
    class Meta:
        model = CompanyGoals

class CompanyKeyResultResource(resources.ModelResource):
    class Meta:
        model = CompanyKeyResult

class KPICheckInResource(resources.ModelResource):
    class Meta:
        model = KPICheckIn

class TasksResource(resources.ModelResource):
    class Meta:
        model = Tasks

class CheckInFrequencyResource(resources.ModelResource):
    class Meta:
        model = CheckInFrequency

class KPICategoryResource(resources.ModelResource):
    class Meta:
        model = KPICategory

class ReviewResource(resources.ModelResource):
    class Meta:
        model = Review

class StatusUpdateResource(resources.ModelResource):
    class Meta:
        model = StatusUpdate

class ActivityLogResource(resources.ModelResource):
    class Meta:
        model = ActivityLog

class RatingCategoryResource(resources.ModelResource):
    class Meta:
        model = RatingCategory

class RatingResource(resources.ModelResource):
    class Meta:
        model = Rating

class AssessmentResource(resources.ModelResource):
    class Meta:
        model = Assessment

class AppraisalResource(resources.ModelResource):
    class Meta:
        model = Appraisal

class AppraisalAssessmentResource(resources.ModelResource):
    class Meta:
        model = AppraisalAssessment

class AssessmentReviewResource(resources.ModelResource):
    class Meta:
        model = AssessmentReview
