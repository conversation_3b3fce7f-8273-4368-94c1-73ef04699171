from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
import pytz

from account.models import Transaction
from sales_app.models import SalesTransactionItem
from stock_inventory.utils import get_gross_profit


class Command(BaseCommand):
    help = "UPDATE EXISTING SALES TRANSACTION(S)."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        sales_transactions = SalesTransactionItem.objects.all()
        for sales_transaction in sales_transactions:
            gross_profit = get_gross_profit(
                cost_price=sales_transaction.item.product_price,
                selling_price=sales_transaction.item.selling_price,
                quantity=sales_transaction.quantity,
            )
            sales_transaction.gross_profit = gross_profit
            sales_transaction.save()
            print("DONE     !!!")
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        print(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
