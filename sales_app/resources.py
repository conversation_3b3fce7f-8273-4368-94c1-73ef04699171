from import_export import resources

from sales_app import models


# Create your resource(s) here.
class ConstantVariableResource(resources.ModelResource):
    class Meta:
        model = models.ConstantVariable


class CustomerResource(resources.ModelResource):
    class Meta:
        model = models.Customer


class CustomerLocationResource(resources.ModelResource):
    class Meta:
        model = models.CustomerLocation


class SalesTransactionResource(resources.ModelResource):
    class Meta:
        model = models.SalesTransaction
    
    def dehydrate_updated_by(self, obj):
        return obj.updated_by.email if obj.updated_by else ""
    
    def dehydrate_created_by(self, obj):
        return obj.created_by.email if obj.created_by else ""
    
    def dehydrate_customer(self, obj):
        return obj.customer.name if obj.customer else ""
    
    def dehydrate_invoice(self, obj):
        return obj.invoice.reference if obj.invoice else ""
    
    def dehydrate_branch(self, obj):
        return obj.branch.name if obj.branch else ""
    
    def dehydrate_company(self, obj):
        return obj.company.company_name if obj.company else ""


class SalesTransactionItemResource(resources.ModelResource):
    class Meta:
        model = models.SalesTransactionItem


class CashBookResource(resources.ModelResource):
    class Meta:
        model = models.CashBook


class ReturnRefundResource(resources.ModelResource):
    class Meta:
        model = models.ReturnRefund


class ReturnRefundItemResource(resources.ModelResource):
    class Meta:
        model = models.ReturnRefundItem


class POSDeviceResource(resources.ModelResource):
    class Meta:
        model = models.POSDevice


class CardTransactionsResource(resources.ModelResource):
    class Meta:
        model = models.CardTransaction


class APILogResource(resources.ModelResource):
    class Meta:
        model = models.APILog


class SalesTransactionBackorderResource(resources.ModelResource):
    class Meta:
        model = models.SalesTransactionBackorder


class OfflineVirtualAccountResource(resources.ModelResource):
    class Meta:
        model = models.OfflineVirtualAccount


class WalletWithdrawalResource(resources.ModelResource):
    class Meta:
        model = models.WalletWithdrawal


class CreditRecordResource(resources.ModelResource):
    class Meta:
        model = models.CreditRecord


class MerchantAccountDetailsResource(resources.ModelResource):
    class Meta:
        model = models.MerchantAccountDetails
