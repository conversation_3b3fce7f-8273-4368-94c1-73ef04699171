from datetime import date, datetime
import json
import os
from io import BytesIO

from django.conf import settings
from django.db import transaction
from django.db.models import Avg, Case, Count, F, Min, Q, Sum, When
from django.db.models.query import QuerySet
from django.http import FileResponse, HttpResponse
import pandas as pd
import pytz
from rest_framework import serializers as drf_serializers
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

# NOTE: DRFResponse is used to cater for MOBILE/POS error handling.
from rest_framework.response import Response as DRFResponse
from rest_framework.views import APIView

from account.enums import AccountType
from account.helpers.core_banking import CoreBankingService
from account.models import (
    AccountServiceProvider,
    AccountSystem,
    CoreBankingCallback,
    Transaction,
)
from core.auth.custom_auth import CustomUserAuthentication
from core.models import User, PaystackPayment
from core.permissions import (
    IpWhiteListPermission,
    IsPipelineUserRole,
)
from core.services import Paystack
from helpers.ai import generate_sales_items
from helpers.custom_filters import QuerysetCustomFilter
from helpers.custom_permissions import (
    BranchRequiredPermission,
    CompanyOwnerPermission,
    CompanyRequiredPermission,
)
from helpers.custom_response import Response
from helpers.enums import ChannelTypes, FilterTypes
from helpers.reusable_functions import (
    Paginator,
    is_valid_string,
    is_valid_uuid,
)
from requisition.models import Company, Team
from sales_app import models, serializers
from sales_app.helper import enums
from stock_inventory.helper import enums as stock_enums
from stock_inventory.models import Branch, PriceList, StockHistory
from stock_inventory.utils import (
    deplete_branch_quantity_available,
    register_stock_history,
)
from subscription_and_invoicing.reuseable import SubscriptionService


# Create your view(s) here.
class CustomerAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.CustomerSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        customer = models.Customer.create_record(
            created_by=request.user, **serializer.validated_data
        )
        if not customer.get("status"):
            return Response(
                errors={"message": customer.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.serializer_class(customer.get("customer"))
        return Response(
            data=serializer.data, status_code=201, status=status.HTTP_201_CREATED
        )

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        customer_id = request.query_params.get("customer")
        search = request.query_params.get("search")

        company_customers = models.Customer.objects.filter(company=company_id)
        if search:
            company_customers = company_customers.filter(
                Q(name__icontains=search)
                | Q(email__icontains=search)
                | Q(phone__icontains=search)
            )
        if customer_id:
            if (
                not is_valid_uuid(customer_id)
                or not company_customers.filter(id=customer_id).exists()
            ):
                return Response(
                    errors={"message": "provide a valid customer ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            company_customers = company_customers.filter(id=customer_id)
        paginated_response = Paginator.paginate(
            request=request, queryset=company_customers
        )
        context = {"branch": branch_id if branch_id else None}
        serializer = self.serializer_class(
            instance=paginated_response, many=True, context=context
        )
        data = {
            "message": "successfully fetched customer record(s).",
            "customers": serializer.data,
            "count": len(serializer.data),
            "total_customers": company_customers.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def patch(self, request, *args, **kwargs):
        serializer = serializers.CustomerUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        customer = models.Customer.update_profile(
            updated_by=request.user, **serializer.validated_data
        )
        serializer = self.serializer_class(instance=customer)
        data = {
            "message": "customer / client profile updated successfully.",
            "customer": serializer.data,
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CustomerLocationAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.AddLocationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        models.CustomerLocation.add_location(
            created_by=request.user, **serializer.validated_data
        )
        return Response(
            data=serializer.data, status_code=201, status=status.HTTP_201_CREATED
        )


class CustomerFileUpload(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.CustomerFileUpload

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        models.Customer.upload_file_record(
            created_by=request.user,
            **serializer.validated_data,
        )
        return Response(
            data={"message": "success"}, status_code=200, status=status.HTTP_200_OK
        )


class CustomerSampleSheetAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        file_path = os.path.join(settings.BASE_DIR, "media/documents")
        return FileResponse(
            open(f"{file_path}/customer_bulk_record.xlsx", "rb"), as_attachment=True
        )


class CustomerSummaryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")

        company_branches = Branch.objects.filter(company=company_id).count()
        company_customers = models.Customer.objects.filter(company=company_id)
        company_total_customers = company_customers.count()
        company_active_customers = company_customers.filter(
            status=enums.CustomerStatus.ACTIVE
        ).count()
        company_inactive_customers = company_customers.filter(
            status=enums.CustomerStatus.INACTIVE
        ).count()

        if branch_id:
            if (
                not is_valid_uuid(branch_id)
                or Branch.retrieve_company_branch(id=branch_id, company=company_id)
                is None
            ):
                return Response(
                    errors={"message": "provide a valid branch ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            branch_total_customers = company_customers.filter(branch=branch_id).count()
            branch_active_customers = company_customers.filter(
                branch=branch_id, status=enums.CustomerStatus.ACTIVE
            ).count()
            branch_inactive_customers = company_customers.filter(
                branch=branch_id, status=enums.CustomerStatus.INACTIVE
            ).count()
            data = {
                "branches": company_branches,
                "all_customers": branch_total_customers,
                "active_customers": branch_active_customers,
                "inactive_customers": branch_inactive_customers,
            }
        else:
            data = {
                "branches": company_branches,
                "all_customers": company_total_customers,
                "active_customers": company_active_customers,
                "inactive_customers": company_inactive_customers,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class TopActiveCustomerAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.CustomerDetailsSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        queryset = models.Customer.get_top_five_active(company=company_id)
        paginated_data = Paginator.paginate(request=request, queryset=queryset)
        serializer = self.serializer_class(instance=paginated_data, many=True)
        data = {
            "message": "success",
            "customers": serializer.data,
            "count": len(serializer.data),
            "total_customers": queryset.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class TopInactiveCustomerAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.CustomerDetailsSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        queryset = models.Customer.get_top_five_inactive(company=company_id)
        paginated_data = Paginator.paginate(request=request, queryset=queryset)
        serializer = self.serializer_class(instance=paginated_data, many=True)
        data = {
            "message": "success",
            "customers": serializer.data,
            "count": len(serializer.data),
            "total_customers": queryset.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CustomerHistoryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.CustomerDetailsSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        active = request.query_params.get("active")
        inactive = request.query_params.get("inactive")
        search = request.query_params.get("search")

        if active:
            if active != "true" and active != "false":
                return Response(
                    errors={"message": "invalid value for active."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            active = True if active == "true" else False
        if inactive:
            if inactive != "true" and inactive != "false":
                return Response(
                    errors={"message": "invalid value for inactive."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            inactive = True if inactive == "true" else False
        customers_transactions = models.Customer.fetch_customers_transactions(
            company=company_id, active=active, inactive=inactive
        )
        if search and is_valid_string(search):
            customers_transactions = customers_transactions.filter(
                Q(name__icontains=search)
                | Q(email__icontains=search)
                | Q(phone__icontains=search)
            )
        paginated_data = Paginator.paginate(
            request=request, queryset=customers_transactions
        )
        serializer = self.serializer_class(instance=paginated_data, many=True)
        data = {
            "message": "success",
            "customers": serializer.data,
            "count": len(serializer.data),
            "total_customers": customers_transactions.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CustomerTransactionAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.CustomerTransactionSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        customer_id = request.query_params.get("customer")

        if not is_valid_uuid(customer_id):
            return Response(
                errors={"message": "provide a valid customer ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        customer = models.Customer.objects.filter(
            company=company_id, id=customer_id
        ).first()
        if customer is None:
            return Response(
                data={"message": "provide a valid customer ID."},
                status_code=404,
                status=status.HTTP_200_OK,
            )
        customer_details = {
            "name": customer.name,
            "email": customer.email,
            "phone": customer.phone,
            "branch": customer.branch.name,
            "status": customer.status,
        }
        if branch_id:
            if (
                not is_valid_uuid(branch_id)
                or Branch.retrieve_company_branch(id=branch_id, company=company_id)
                is None
            ):
                return Response(
                    errors={"message": "provide a valid branch ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            customer_branch_transactions = models.Customer.fetch_customer_invoices(
                company=company_id, customer=customer, branch=branch_id
            )
            paginated_data = Paginator.paginate(
                request=request, queryset=customer_branch_transactions
            )
            serializer = self.serializer_class(instance=paginated_data, many=True)
            data = {
                "message": "success",
                "customer": customer_details,
                "transactions": serializer.data,
                "count": len(serializer.data),
                "total_transactions": customer_branch_transactions.count(),
            }
        else:
            customer_company_transactions = models.Customer.fetch_customer_invoices(
                company=company_id, customer=customer
            )
            paginated_data = Paginator.paginate(
                request=request, queryset=customer_company_transactions
            )
            serializer = self.serializer_class(instance=paginated_data, many=True)
            data = {
                "message": "success",
                "customer": customer_details,
                "transactions": serializer.data,
                "count": len(serializer.data),
                "total_transactions": customer_company_transactions.count(),
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class RegisterSalesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.RegisterSalesSerializer

    def post(self, request, *args, **kwargs):
        ############################################################################
        #                       MONITOR USER API REQUESTS                          #
        #                                                                          #
        ############################################################################
        api_log = models.APILog.objects.create(
            user=request.user,
            method=request.method,
            path="/api/v1/sales/register/",
            request_headers=request.headers,
            request_body=request.data,
        )
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales = models.SalesTransaction.manage_sales(
            user=request.user, register=True, **serializer.validated_data
        )
        api_log.response_body = str(sales)
        api_log.sales_batch_id = sales.get("batch_id")
        api_log.status_code = "201"
        api_log.save()
        return Response(data=sales, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        device = request.query_params.get("device")
        transaction_status = request.query_params.get("transaction_status")
        search = request.query_params.get("search")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        # New time-based parameters
        start_hour = request.query_params.get("start_hour")
        end_hour = request.query_params.get("end_hour")

        company_sales_transaction = models.SalesTransaction.objects.filter(
            company=company_id,
            branch=branch_id,
            batch_id__isnull=False,
        )
        if (device and is_valid_string(device)) and device in [
            ChannelTypes.MOBILE,
            ChannelTypes.POS,
            ChannelTypes.WEB_POS,
        ]:
            company_sales_transaction = company_sales_transaction.filter(
                created_by=request.user
            )
        if transaction_status and is_valid_string(transaction_status):
            company_sales_transaction = company_sales_transaction.filter(
                status=transaction_status.upper()
            )
        if search and is_valid_string(search):
            company_sales_transaction = company_sales_transaction.filter(
                Q(batch_id__icontains=search)
                | Q(invoice__reference__icontains=search)
                | Q(sales_tag__icontains=search)
            )
        if result_type and is_valid_string((result_type)):
            company_sales_transaction = QuerysetCustomFilter.date_range_filter(
                queryset=company_sales_transaction,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
                start_hour=start_hour,
                end_hour=end_hour,
            )
            if not isinstance(company_sales_transaction, QuerySet):
                return Response(
                    errors=company_sales_transaction,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        total_sales = (
            company_sales_transaction.aggregate(sales_value=Sum("total_cost"))[
                "sales_value"
            ]
            or 0.0
        )
        paginated_data = Paginator.paginate(
            request=request, queryset=company_sales_transaction
        )
        serializer = serializers.SalesTransactionSerializer(
            instance=paginated_data, many=True
        )
        data = {
            "message": "success",
            "total_sales": total_sales,
            "sales_transactions": serializer.data,
            "count": len(serializer.data),
            "total_transactions": company_sales_transaction.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def put(self, request, *args, **kwargs):
        serializer = serializers.UpdateSalesTransaction(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales_transaction = models.SalesTransaction.decline_order(
            user=request.user, **serializer.validated_data
        )
        if not sales_transaction.get("status"):
            return Response(
                errors={"message": "sales transaction not found"},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = serializers.SalesTransactionSerializer(
            instance=sales_transaction.get("data")
        )
        return Response(
            data=serializer.data, status_code=200, status=status.HTTP_200_OK
        )

    def patch(self, request, *args, **kwargs):
        serializer = serializers.ManageSavedSalesSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales_transaction = models.SalesTransaction.manage_sales(
            user=request.user,
            **serializer.validated_data,
        )
        if sales_transaction is None:
            return Response(
                errors={"message": "invalid sales transaction"},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = serializers.SalesTransactionSerializer(
            instance=sales_transaction.get("data")
        )
        return Response(
            data=sales_transaction, status_code=200, status=status.HTTP_200_OK
        )


class ConfirmSalesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.ConfirmSalesSerializer

    @transaction.atomic
    def patch(self, request, *args, **kwargs):
        ############################################################################
        #                       MONITOR USER API REQUESTS                          #
        #                                                                          #
        ############################################################################
        api_log = models.APILog.objects.create(
            user=request.user,
            method=request.method,
            path="/api/v1/sales/confirm_sales/",
            request_headers=request.headers,
            request_body=request.data,
        )
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales_transaction = models.SalesTransaction.confirm_sales(
            updated_by=request.user,
            **serializer.validated_data,
        )
        if sales_transaction is None:
            data = ({"message": "invalid sales transaction."},)
            api_log.sales_batch_id = serializer.validated_data.get("batch_id")
            api_log.response_body = data
            api_log.status_code = 400
            api_log.save()
            return DRFResponse(
                data=data,
                status=status.HTTP_400_BAD_REQUEST,
            )
        items = models.SalesTransactionItem.objects.filter(
            sales_transaction=sales_transaction,
            is_active=True,
        )
        count = 1
        items__depletion_history = {}
        for data in items:
            if data.bar_alert:
                raise drf_serializers.ValidationError(
                    {"message": f"{data.item.name} requires manager approval."}
                )
            # 2024-10-02 act on confirmation.
            deplete_response = deplete_branch_quantity_available(
                company=data.company,
                branch=data.branch,
                category=data.category,
                item=data.item,
                quantity=data.quantity,
                sales_transaction=sales_transaction,
                on_hold=False,
            )
            if not isinstance(deplete_response, bool):
                data.quantity_before = deplete_response.get("quantity_before")
                data.quantity_after = deplete_response.get("quantity_after")
                data.updated_by = request.user
                data.status = enums.OrderStatus.completed
                data.save()
                register_stock_history(
                    company=data.company,
                    branch=data.branch,
                    category=data.category,
                    item=data.item,
                    price=data.amount,
                    quantity_before=deplete_response.get("quantity_before"),
                    quantity=data.quantity,
                    quantity_after=deplete_response.get("quantity_after"),
                    transaction_type=stock_enums.StockHistoryChoices.SALES,
                    status=stock_enums.StockHistoryStatusChoices.OUTGOING,
                    created_by=request.user,
                )
                item_deplete_history = {
                    "company": data.company.company_name,
                    "branch": data.branch.name,
                    "category": data.category.name,
                    "category_id": str(data.category.id),
                    "product": data.item.name,
                    "product_id": str(data.item.id),
                    "deplete_quantity": deplete_response.get("deplete_quantity"),
                    "quantity_before": deplete_response.get("quantity_before"),
                    "quantity_after": deplete_response.get("quantity_after"),
                    "deplete_message": deplete_response.get("message"),
                }
                items__depletion_history[f"item{count}"] = item_deplete_history
            count += 1
        serializer = serializers.SalesItemSerializer(instance=items, many=True)
        if sales_transaction.customer is not None:
            customer = {
                "name": sales_transaction.customer.name,
                "phone": sales_transaction.customer.phone,
                "email": sales_transaction.customer.email,
                "address": sales_transaction.customer.address,
            }
        else:
            customer = {
                "name": None,
                "phone": None,
                "email": None,
                "address": None,
            }
        data = {
            "message": "success",
            "company_name": sales_transaction.company.company_name,
            "branch_name": sales_transaction.branch.name,
            "branch_address": sales_transaction.branch.address,
            "branch_phone": sales_transaction.branch.phone,
            "cashier": sales_transaction.created_by.full_name,
            "updated_at": sales_transaction.updated_at,
            "invoice_id": (
                sales_transaction.invoice.reference
                if sales_transaction.invoice is not None
                else ""
            ),
            "batch_id": sales_transaction.batch_id,
            "sub_total": sales_transaction.total_sales_amount,
            "discount_value": sales_transaction.discount_value,
            "delivery_fee": sales_transaction.delivery_fee,
            "vat": sales_transaction.vat_amount,
            "charges": sales_transaction.charges,
            "total": sales_transaction.total_cost,
            "items": serializer.data,
            "means_of_payment": sales_transaction.means_of_payment,
            "customer": customer,
        }
        api_log.sales_batch_id = sales_transaction.batch_id
        api_log.response_body = str(data)
        api_log.items_depletion_history = items__depletion_history
        api_log.status_code = 200
        api_log.save()
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class SwapItemsTransactionAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.SwapItemsSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        swap_items = models.ReturnRefundItem.swap_items(
            created_by=request.user, **serializer.validated_data
        )
        if swap_items is None:
            return Response(
                errors={"message": "invalid sales transaction."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        data = {
            "message": "sales transaction updated successfully.",
            "details": swap_items,
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)


class BranchDashboardTodayAPIView(APIView):
    """
    Displays today's Branch sales details on sales registration page.
    """

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")

        branch_sales = models.CashBook.objects.filter(
            company=company,
            branch=branch,
            created_at__date=date.today(),
        )
        total_sales = (
            branch_sales.aggregate(total_sales=Sum("amount"))["total_sales"] or 0.0
        )
        branch_cash_in_bank = (
            branch_sales.filter(
                cash_book_type=enums.CashBookChoices.CASH_IN_BANK
            ).aggregate(cash_in_bank=Sum("amount"))["cash_in_bank"]
            or 0.0
        )
        branch_cash_in_hand = (
            branch_sales.filter(
                cash_book_type=enums.CashBookChoices.CASH_IN_HAND
            ).aggregate(cash_in_hand=Sum("amount"))["cash_in_hand"]
            or 0.0
        )
        data = {
            "total_sales": total_sales,
            "branch_cash_in_bank": branch_cash_in_bank,
            "branch_cash_in_hand": branch_cash_in_hand,
            "count": branch_sales.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyOverviewAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company_id")
        company_branches = models.Branch.objects.filter(company=company)
        company_teams = Team.objects.filter(company=company, is_active=True)
        company_sales = models.SalesTransaction.objects.filter(
            company=company,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            batch_id__isnull=False,
        )
        company_sales_items = models.SalesTransactionItem.objects.filter(
            company=company,
            sales_transaction__status=enums.TransactionStatusChoices.SUCCESSFUL,
        )
        company_customers = models.Customer.objects.filter(company=company).count()
        total_products_sold = company_sales_items.values("item").distinct().count()
        company_sales_value = (
            company_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.0
        )
        average_order_value = round(
            (
                company_sales.aggregate(total_amount=Avg("total_sales_amount"))[
                    "total_amount"
                ]
                or 0.0
            ),
            2,
        )
        returning_customers = (
            company_sales.values("customer")
            .annotate(count=Count("customer"))
            .filter(count__gt=1)
            .count()
        )
        if company_customers <= 0:
            returning_customer_rate = 0
        else:
            returning_customer_rate = round(
                ((returning_customers / company_customers) * 100), 2
            )
        # filtered company's sales result(s).
        today_sales = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales,
            result_type=FilterTypes.TODAY,
        )
        today_sales_value = (
            today_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.0
        )
        yesterday_sales = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales,
            result_type=FilterTypes.YESTERDAY,
        )
        yesterday_sales_value = (
            yesterday_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.0
        )
        this_week_sales = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales,
            result_type=FilterTypes.CURRENT_WEEK,
        )
        this_week_sales_value = (
            this_week_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.0
        )
        last_week_sales = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales,
            result_type=FilterTypes.LAST_WEEK,
        )
        last_week_sales_value = (
            last_week_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.0
        )
        this_month_sales = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales,
            result_type=FilterTypes.THIS_MONTH,
        )
        this_month_sales_value = (
            this_month_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.0
        )
        last_month_sales = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales,
            result_type=FilterTypes.LAST_MONTH,
        )
        last_month_sales_value = (
            last_month_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.0
        )
        this_year_sales = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales,
            result_type=FilterTypes.THIS_YEAR,
        )
        this_year_sales_value = (
            this_year_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.0
        )
        last_year_sales = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales,
            result_type=FilterTypes.LAST_YEAR,
        )
        last_year_sales_value = (
            last_year_sales.aggregate(total_amount=Sum("total_sales_amount"))[
                "total_amount"
            ]
            or 0.0
        )
        # filtered company's gross profit.
        today_gross_profit = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales_items,
            result_type=FilterTypes.TODAY,
        )
        today_gross_profit_value = (
            today_gross_profit.aggregate(gross_profit=Sum("gross_profit"))[
                "gross_profit"
            ]
            or 0.0
        )
        yesterday_gross_profit = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales_items,
            result_type=FilterTypes.YESTERDAY,
        )
        yesterday_gross_profit_value = (
            yesterday_gross_profit.aggregate(gross_profit=Sum("gross_profit"))[
                "gross_profit"
            ]
            or 0.0
        )
        this_week_gross_profit = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales_items,
            result_type=FilterTypes.CURRENT_WEEK,
        )
        this_week_gross_profit_value = (
            this_week_gross_profit.aggregate(gross_profit=Sum("gross_profit"))[
                "gross_profit"
            ]
            or 0.0
        )
        last_week_gross_profit = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales_items,
            result_type=FilterTypes.LAST_WEEK,
        )
        last_week_gross_profit_value = (
            last_week_gross_profit.aggregate(gross_profit=Sum("gross_profit"))[
                "gross_profit"
            ]
            or 0.0
        )
        this_month_gross_profit = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales_items,
            result_type=FilterTypes.THIS_MONTH,
        )
        this_month_gross_profit_value = (
            this_month_gross_profit.aggregate(gross_profit=Sum("gross_profit"))[
                "gross_profit"
            ]
            or 0.0
        )
        last_month_gross_profit = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales_items,
            result_type=FilterTypes.LAST_MONTH,
        )
        last_month_gross_profit_value = (
            last_month_gross_profit.aggregate(gross_profit=Sum("gross_profit"))[
                "gross_profit"
            ]
            or 0.0
        )
        this_year_gross_profit = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales_items,
            result_type=FilterTypes.THIS_YEAR,
        )
        this_year_gross_profit_value = (
            this_year_gross_profit.aggregate(gross_profit=Sum("gross_profit"))[
                "gross_profit"
            ]
            or 0.0
        )
        last_year_gross_profit = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales_items,
            result_type=FilterTypes.LAST_YEAR,
        )
        last_year_gross_profit_value = (
            last_year_gross_profit.aggregate(gross_profit=Sum("gross_profit"))[
                "gross_profit"
            ]
            or 0.0
        )

        data = {
            "branches": company_branches.count(),
            "teams": company_teams.count(),
            "total_sales_value": company_sales_value,
            "total_sales_count": company_sales.count(),
            "total_products_sold": total_products_sold,
            "average_order_value": average_order_value,
            "returning_customer_rate": returning_customer_rate,
            "global_sales_value": {
                "today": today_sales_value,
                "yesterday": yesterday_sales_value,
                "this_week": this_week_sales_value,
                "last_week": last_week_sales_value,
                "this_month": this_month_sales_value,
                "last_month": last_month_sales_value,
                "this_year": this_year_sales_value,
                "last_year": last_year_sales_value,
            },
            "global_gross_profit": {
                "today": today_gross_profit_value,
                "yesterday": yesterday_gross_profit_value,
                "this_week": this_week_gross_profit_value,
                "last_week": last_week_gross_profit_value,
                "this_month": this_month_gross_profit_value,
                "last_month": last_month_gross_profit_value,
                "this_year": this_year_gross_profit_value,
                "last_year": last_year_gross_profit_value,
            },
            "global_transaction_count": {
                "today": today_sales.count(),
                "yesterday": yesterday_sales.count(),
                "this_week": this_week_sales.count(),
                "last_week": last_week_sales.count(),
                "this_month": this_month_sales.count(),
                "last_month": last_month_sales.count(),
                "this_year": this_year_sales.count(),
                "last_year": last_year_sales.count(),
            },
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanySalesTableAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company_id")
        company_sales = models.SalesTransaction.objects.filter(
            company=company,
            batch_id__isnull=False,
        )
        if company_sales.exists():
            aggregated_data = (
                company_sales.values("batch_id")
                .annotate(
                    company=F("company__company_name"),
                    created_at=Min("created_at"),
                    branch=F("branch__name"),
                    amount=Sum("total_sales_amount"),
                    invoice=F("invoice__reference"),
                    payment_method=F("means_of_payment"),
                    transaction_id=F("batch_id"),
                    status=F("status"),
                )
                .order_by("-created_at")
            )
            paginated_response = Paginator.paginate(
                request=request, queryset=aggregated_data
            )
            serializer = serializers.SalesTableSerializer(
                instance=paginated_response, many=True
            )
            data = {
                "message": "success",
                "sales": serializer.data,
                "count": len(serializer.data),
                "total_sales": aggregated_data.count(),
            }
        else:
            data = {
                "message": "no available transaction(s).",
                "sales": [],
                "count": 0,
                "total_sales": 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanySalesBranchAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.SalesBranchSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")

        company_branches = Branch.objects.filter(company=company_id)
        if branch_id:
            if (
                not is_valid_uuid(branch_id)
                or not company_branches.filter(id=branch_id).exists()
            ):
                return Response(
                    errors={"message": "provide a valid branch ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            else:
                branch = company_branches.filter(id=branch_id).first()
                serializer = self.serializer_class(instance=branch)
                data = {
                    "message": "successfully fetched company's branch.",
                    "branch": serializer.data,
                }
        else:
            paginated_data = Paginator.paginate(
                request=request, queryset=company_branches
            )
            serializer = self.serializer_class(instance=paginated_data, many=True)
            data = {
                "message": "successfully fetched company's branch(es).",
                "branches": serializer.data,
                "count": len(serializer.data),
                "total_branches": company_branches.count(),
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyBranchComparisonAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        company_branches = Branch.objects.filter(company=company).count()
        branches_comparison = models.SalesTransaction.get_branches_comparison(
            company=company,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not branches_comparison.get("status"):
            return Response(
                errors={"message": branches_comparison.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.BranchComparisonSerializer(
                instance=branches_comparison.get("details"), many=True
            )
            data = {
                "message": "success",
                "global_transactions": branches_comparison.get("global_transactions"),
                "total_branches": company_branches,
                "sales_comparison": serializer.data,
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyBranchRefundComparisonAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        query_parameters = request.GET
        company = query_parameters.get("company")
        result_type = query_parameters.get("result_type")
        start_date = query_parameters.get("start_date")
        end_date = query_parameters.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        company_branches = Branch.objects.filter(company=company).count()
        branches_comparison = models.ReturnRefund.get_branches_comparison(
            company=company,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not branches_comparison.get("status"):
            return Response(
                errors={"message": branches_comparison.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.BranchComparisonSerializer(
                instance=branches_comparison.get("details"), many=True
            )
            data = {
                "message": "success",
                "total_branches": company_branches,
                "refund_comparison": serializer.data,
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyCategoriesAboveAvgAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.GET.get("company")
        categories_comparison = (
            models.SalesTransactionItem.categories_above_average_comparison(
                company=company
            )
        )
        data = {"message": "success", "categories": categories_comparison}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyCategoriesBelowAvgAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.GET.get("company")
        categories_comparison = (
            models.SalesTransactionItem.categories_below_average_comparison(
                company=company
            )
        )
        data = {"message": "success", "categories": categories_comparison}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchDashboardAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        company = Company.objects.filter(id=company).last()
        branch_analytics = models.SalesTransactionItem.get_branch_analytics(
            company=company,
            branch=branch,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not branch_analytics.get("status"):
            return Response(
                errors={"message": branch_analytics.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            data=branch_analytics.get("details"),
            status_code=200,
            status=status.HTTP_200_OK,
        )


class BranchSalesTableAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        transaction_status = request.query_params.get("transaction_status")
        search = request.query_params.get("search")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if Branch.retrieve_company_branch(id=branch_id, company=company_id) is None:
            return Response(
                errors={"message": "provide a valid branch ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_sales_transaction = models.SalesTransaction.objects.filter(
            company=company_id,
            branch=branch_id,
            batch_id__isnull=False,
        )
        if transaction_status and is_valid_string(transaction_status):
            branch_sales_transaction = branch_sales_transaction.filter(
                status=transaction_status.upper()
            )
        if search and is_valid_string(search):
            branch_sales_transaction = branch_sales_transaction.filter(
                Q(batch_id=search) | Q(invoice__reference=search)
            )
        if result_type and is_valid_string((result_type)):
            branch_sales_transaction = QuerysetCustomFilter.date_range_filter(
                queryset=branch_sales_transaction,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
            )
            if not isinstance(branch_sales_transaction, QuerySet):
                return Response(
                    errors=branch_sales_transaction,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        if not transaction_status:
            branch_sales_transaction = branch_sales_transaction.filter(
                Q(status=enums.TransactionStatusChoices.SAVED)
                | Q(status=enums.TransactionStatusChoices.SUCCESSFUL)
            )
        if branch_sales_transaction.exists():
            aggregated_data = (
                branch_sales_transaction.values("batch_id")
                .annotate(
                    company=F("company__company_name"),
                    branch=F("branch__name"),
                    created_at=Min("created_at"),
                    cashier=F("created_by__first_name"),
                    amount=Sum("total_sales_amount"),
                    invoice=F("invoice__reference"),
                    payment_method=F("means_of_payment"),
                    transaction_id=F("batch_id"),
                    status=F("status"),
                )
                .order_by("-created_at")
            )
            paginated_response = Paginator.paginate(
                request=request, queryset=aggregated_data
            )
            serializer = serializers.SalesTableSerializer(
                instance=paginated_response, many=True
            )
            data = {
                "message": "success",
                "sales": serializer.data,
                "count": len(serializer.data),
                "total_sales": aggregated_data.count(),
            }
        else:
            data = {
                "message": "no available transaction(s).",
                "sales": [],
                "count": 0,
                "total_sales": 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchTotalSalesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_total_sales = models.SalesTransaction.get_branch_total_sales(
            company=company,
            branch=branch,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not branch_total_sales.get("status"):
            return Response(
                errors={"message": branch_total_sales.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.BranchComparisonSerializer(
                instance=branch_total_sales.get("details"), many=True
            )
            data = {"message": "success", "total_sales": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchAverageSalesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_average_sales = models.SalesTransaction.get_branch_average_sales(
            company=company,
            branch=branch,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not branch_average_sales.get("status"):
            return Response(
                errors={"message": branch_average_sales.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.BranchComparisonSerializer(
                instance=branch_average_sales.get("details"), many=True
            )
            data = {"message": "success", "total_sales": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchGrossProfitAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_gross_profit = models.SalesTransactionItem.profit_tracking(
            company=company,
            branch=branch,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not branch_gross_profit.get("status"):
            return Response(
                errors={"message": branch_gross_profit.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.BranchComparisonSerializer(
                instance=branch_gross_profit.get("details"), many=True
            )
            data = {"message": "success", "total_sales": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchProductsSoldAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_products_sold = models.SalesTransactionItem.get_branch_products_sold(
            company=company,
            branch=branch,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not branch_products_sold.get("status"):
            return Response(
                errors={"message": branch_products_sold.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.BranchComparisonSerializer(
                instance=branch_products_sold.get("details"), many=True
            )
            data = {"message": "success", "total_sales": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchTopProductsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.TopProductsSerializer

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        branch_top_products = models.SalesTransactionItem.top_ten_products(
            company=company,
            branch=branch_id,
        )
        serializer = self.serializer_class(instance=branch_top_products, many=True)
        data = {"message": "success", "products": serializer.data}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class PaymentLinkAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.PaymentLinkRequestSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        payment_link = PaystackPayment.generate_payment_link(
            email=serializer.validated_data.get("email"),
            amount=serializer.validated_data.get("amount"),
            reference=serializer.validated_data.get("sales_batch_id"),
        )
        if serializer.validated_data.get("is_new_customer"):
            models.Customer.create_record(
                company=serializer.validated_data.get("company"),
                branch=serializer.validated_data.get("branch"),
                created_by=request.user,
                name=serializer.validated_data.get("name"),
                email=serializer.validated_data.get("email"),
                phone=serializer.validated_data.get("phone"),
                address=serializer.validated_data.get("address"),
            )
        return Response(data=payment_link, status_code=200, status=status.HTTP_200_OK)


class ReturnRefundAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.RefundItemsSerializer
    summary_serializer = serializers.ReturnRefundSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        refund_items = models.ReturnRefundItem.refund_items(
            created_by=request.user, **serializer.validated_data
        )
        if not refund_items:
            return Response(
                errors={"message": "Items already swapped or refunded."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if refund_items is None:
            return Response(
                errors={"message": "invalid sales transaction."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        data = {
            "message": "sales transaction updated successfully.",
            "details": refund_items,
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        branch = request.query_params.get("branch")
        branch_requests = models.ReturnRefund.objects.filter(branch=branch)
        paginated_response = Paginator.paginate(
            request=request, queryset=branch_requests
        )
        serializer = self.summary_serializer(instance=paginated_response, many=True)
        data = {
            "message": "success",
            "requests": serializer.data,
            "count": len(serializer.data),
            "total_requests": branch_requests.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class RefundApproveDeclineAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def patch(self, request, *args, **kwargs):
        serializer = serializers.RefundApproveDeclineSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        refund_request = models.ReturnRefund.approve_decline_refund(
            approved_by=request.user, **serializer.validated_data
        )
        if refund_request is None:
            data = {"message": "invalid refund request."}
            return Response(
                data=data, status_code=400, status=status.HTTP_400_BAD_REQUEST
            )
        else:
            data = {"message": "successfully approved request."}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchTopRefundsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.RefundsProductSerializer

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        branch_top_products = models.ReturnRefund.get_branch_top_products(
            company=company, branch=branch
        )
        serializer = self.serializer_class(instance=branch_top_products, many=True)
        data = {"message": "success", "refunds": serializer.data}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class SalesTransactionsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.CustomerSalesTransactionSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        search = request.query_params.get("search")

        branch_transactions = models.SalesTransaction.objects.filter(
            company=company_id,
            branch=branch_id,
            batch_id__isnull=False,
        )
        if search:
            search_result = branch_transactions.filter(
                Q(invoice__reference__icontains=search)
                | Q(batch_id__icontains=search)
                | Q(customer__name__icontains=search)
                | Q(customer__email__icontains=search)
                | Q(customer__phone__icontains=search)
            )
            paginated_response = Paginator.paginate(
                request=request, queryset=search_result
            )
            serializer = self.serializer_class(instance=paginated_response, many=True)
            data = {
                "message": "success",
                "transactions": serializer.data,
                "count": len(serializer.data),
                "total_transactions": search_result.count(),
            }
        else:
            paginated_response = Paginator.paginate(
                request=request, queryset=branch_transactions
            )
            serializer = self.serializer_class(instance=paginated_response, many=True)
            data = {
                "message": "success",
                "transactions": serializer.data,
                "count": len(serializer.data),
                "total_transactions": branch_transactions.count(),
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class SalesTransactionItemAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.SalesTransactionItemsSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        transaction_status = request.query_params.get("transaction_status")
        search = request.query_params.get("search")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        # New time-based parameters
        start_hour = request.query_params.get("start_hour")
        end_hour = request.query_params.get("end_hour")

        transaction_details = {}
        transaction_items = models.SalesTransactionItem.objects.filter(
            sales_transaction__company=company_id,
            sales_transaction__branch=branch_id,
            sales_transaction__batch_id__isnull=False,
        )
        if transaction_status and is_valid_string(transaction_status):
            transaction_items = transaction_items.filter(
                sales_transaction__status=transaction_status.upper()
            )
        if not transaction_status or not is_valid_string(transaction_status):
            transaction_items = transaction_items.filter(
                Q(sales_transaction__status=enums.TransactionStatusChoices.SUCCESSFUL)
                | Q(sales_transaction__status=enums.TransactionStatusChoices.SAVED)
            )
        if result_type and is_valid_string((result_type)):
            transaction_items = QuerysetCustomFilter.date_range_filter(
                queryset=transaction_items,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
                start_hour=start_hour,
                end_hour=end_hour,
            )
            if not isinstance(transaction_items, QuerySet):
                return Response(
                    errors=transaction_items,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        if search and is_valid_string(search):
            transaction_items = transaction_items.filter(
                Q(sales_transaction__invoice__reference=search)
                | Q(sales_transaction__batch_id=search)
                | Q(category__name__icontains=search)
                | Q(item__name__icontains=search)
            )
            # provide sales/paymennt details for single transaction.
            sales_transaction = models.SalesTransaction.objects.filter(
                company=company_id,
                branch=branch_id,
                status=enums.TransactionStatusChoices.SUCCESSFUL,
                batch_id=search,
            ).last()
            payment_details = []
            if sales_transaction:
                if (
                    sales_transaction.means_of_payment
                    == enums.MeansOfPaymentChoices.SPLIT
                ):
                    split_payments = models.SalesTransaction.objects.filter(
                        company=company_id,
                        branch=branch_id,
                        status=enums.TransactionStatusChoices.SUCCESSFUL,
                        payment_reference=search,
                    )
                    payment_details = [
                        {
                            "method": payment.means_of_payment,
                            "amount": payment.amount_paid,
                        }
                        for payment in split_payments
                    ]
                transaction_details = {
                    "total_amount": sales_transaction.total_cost,
                    "attendant": (
                        sales_transaction.updated_by.full_name
                        if sales_transaction.updated_by is not None
                        else ""
                    ),
                    "invoice": sales_transaction.invoice.reference,
                    "payment": {
                        "method": sales_transaction.means_of_payment,
                        "amount": sales_transaction.amount_paid,
                    },
                    "payment_details": payment_details,
                }
        paginated_data = Paginator.paginate(request=request, queryset=transaction_items)
        serializer = self.serializer_class(instance=paginated_data, many=True)
        data = {
            "message": "success",
            "transaction_details": transaction_details,
            "transaction_items": serializer.data,
            "count": len(serializer.data),
            "total_count": transaction_items.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class UserDetailsAPIView(APIView):
    paybox360_login_url = "https://www.home.paybox360.com/login"
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user_details = request.user.identifier
        if user_details is not None and (
            user_details.get("company") != None and user_details.get("branch") != None
        ):

            company = user_details.get("company")

            # Initialize SubscriptionService and fetch subscription info
            subscription_service = SubscriptionService(company)
            subscription_data = subscription_service.get_subscription_info({})

            # available stock or price list indicator.
            has_stock = (
                PriceList.objects.filter(company=user_details.get("company"))
                .filter(
                    Q(all_branches=True)
                    | Q(selected_branches=user_details.get("branch"))
                )
                .order_by("item__id")
                .distinct("item")
            )
            # fetch user team members.
            branch_members = User.objects.filter(
                username__isnull=False,
                default_company=user_details.get("company"),
                default_branch=user_details.get("branch"),
            ).exclude(id=user_details.get("id"))
            if branch_members.exists():
                team_members = [
                    {
                        "user_id": member.id,
                        "username": member.username,
                        "secret_question": member.secret_question,
                        "secret_answer": member.secret_answer,
                    }
                    for member in branch_members
                ]
            else:
                team_members = []
            # transaction charges details.
            charges_details = models.ConstantVariable.objects.first()
            if charges_details is not None:
                sales_charge = charges_details.sales_charge
                sales_charge_cap = charges_details.sales_charge_cap
                sales_withdrawal_charge = charges_details.sales_withdrawal_charge
            else:
                sales_charge = 0.0
                sales_charge_cap = 0.0
                sales_withdrawal_charge = 0.0
            data = {
                "status": True,
                "user_id": user_details.get("id"),
                "username": user_details.get("username"),
                "company": user_details.get("company").company_name,
                "company_id": user_details.get("company").id,
                "branch": user_details.get("branch").name,
                "branch_id": user_details.get("branch").id,
                "has_stock": has_stock.exists(),
                "link": self.paybox360_login_url,
                "secret_question": user_details.get("secret_question"),
                "secret_answer": user_details.get("secret_answer"),
                "team_members": team_members,
                "sell_without_inventory": user_details.get(
                    "branch"
                ).sell_without_inventory,
                "transfer_charges_to_customer": user_details.get(
                    "branch"
                ).transfer_charges_to_customer,
                "use_product_vat": user_details.get("branch").use_product_vat,
                "charges": {
                    "sales_charge": sales_charge,
                    "sales_charge_cap": sales_charge_cap,
                    "sales_withdrawal_charge": sales_withdrawal_charge,
                },
                # Include subscription data
                "is_subscription": subscription_data.get("is_subscription", False),
                "subscription_info": subscription_data.get("subscription_info", None),
            }
        else:
            data = {
                "status": False,
                "user_id": None,
                "username": None,
                "company": None,
                "company_id": None,
                "branch": None,
                "branch_id": None,
                "has_stock": False,
                "link": self.paybox360_login_url,
                "secret_question": None,
                "secret_answer": None,
                "team_members": [],
                "sell_without_inventory": False,
                "transfer_charges_to_customer": False,
                "use_product_vat": False,
                "charges": {
                    "sales_charge": 0.0,
                    "sales_charge_cap": 0.0,
                    "sales_withdrawal_charge": 0.0,
                },
                "is_subscription": False,
                "subscription_info": None,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchInstantAccountAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.BranchInstantAccountSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales_transaction = models.SalesTransaction.objects.filter(
            company=serializer.validated_data.get("company"),
            branch=serializer.validated_data.get("branch"),
            batch_id=serializer.validated_data.get("batch_id"),
            status=enums.TransactionStatusChoices.PENDING,
        ).first()
        if sales_transaction is None:
            return Response(
                errors={"message": "invalid sales transaction"},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        request_handler = CoreBankingService()
        response = request_handler.get_one_time_account(
            request_reference=sales_transaction.batch_id
        )
        if response.get("status"):
            if not isinstance(response.get("data"), str):
                response_data = response.get("data").get("data").get("account_details")
                data = {
                    "amount": sales_transaction.total_cost,
                    "bank_name": response_data.get("bank_name"),
                    "bank_code": response_data.get("bank_code"),
                    "account_name": response_data.get("account_name"),
                    "account_number": response_data.get("account_number"),
                }
            else:
                data = {
                    "amount": 0.0,
                    "bank_name": None,
                    "bank_code": None,
                    "account_name": None,
                    "account_number": None,
                }
        else:
            data = {
                "amount": 0.0,
                "bank_name": None,
                "bank_code": None,
                "account_name": None,
                "account_number": None,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchVerifyTransferAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.BranchInstantAccountSerializer

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        batch_id = request.query_params.get("batch_id")
        sales_transaction = models.SalesTransaction.objects.filter(
            company=company,
            branch=branch,
            batch_id=batch_id,
        ).first()
        if sales_transaction is None:
            return DRFResponse(
                {
                    "status": False,
                    "message": "invalid sales transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        payment_received = CoreBankingCallback.objects.filter(
            request_reference=sales_transaction.batch_id
        ).first()
        if payment_received is None:
            data = {
                "status": False,
                "message": "awaiting payment.",
                "amount": 0.0,
            }
        else:
            data = {
                "status": True,
                "message": "payment received successfully.",
                "amount": float(payment_received.amount),
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class AIConverterAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def post(self, request, *args, **kwargs):
        company_id = request.data.get("company")
        branch_id = request.data.get("branch")
        is_upload = request.data.get("is_upload")
        is_snapshot = request.data.get("is_snapshot")

        if is_upload:
            image = request.FILES.get("image")
            if not image:
                return DRFResponse(
                    data={"message": "no image provided for upload."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            ai_response = generate_sales_items(
                image, is_base64=False, company_id=company_id, branch_id=branch_id
            )
        elif is_snapshot:
            image_data = request.data.get("image")
            if not image_data:
                return Response(
                    data={"message": "no image data provided for snapshot."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            ai_response = generate_sales_items(
                image_data, is_base64=True, company_id=company_id, branch_id=branch_id
            )
        else:
            return DRFResponse(
                data={
                    "message": "invalid request, must specify is_upload or is_snapshot."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        if ai_response == None:
            return DRFResponse(
                data={"message": "item(s) not available, update the price list."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if "error" in ai_response:
            return DRFResponse(
                data={"message": json.dumps(ai_response)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        return Response(
            data={"order_items": ai_response},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class RegisterRRNAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        serializer = serializers.RegisterRRNSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        response = models.SalesTransaction.register_rrn(
            user=request.user,
            payload=request.data,
            **serializer.validated_data,
        )
        if not response.get("status"):
            return Response(
                errors={"message": "invalid sales transaction."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = serializers.SalesTransactionSerializer(
            instance=response.get("data")
        )
        return Response(
            data=serializer.data, status_code=200, status=status.HTTP_200_OK
        )


class CardTransactionCallbackAPIView(APIView):
    permission_classes = [IpWhiteListPermission]

    def post(self, request, *args, **kwargs):
        card_transaction = models.CardTransaction.register(request.data)
        return Response(
            data=card_transaction, status_code=200, status=status.HTTP_200_OK
        )


class PaymentHistoryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.CashBookSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        reference = request.query_params.get("reference")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        account_details = AccountServiceProvider.objects.filter(branch=branch_id).last()
        if account_details is not None:
            account_name = account_details.account_name
            bank = account_details.bank_name
            nuban = account_details.account_number
        else:
            account_details = AccountSystem.objects.filter(
                branch=branch_id,
                account_type=AccountType.SALES,
            ).last()
            if account_details is not None:
                account_name = account_details.account_name
                bank = account_details.bank_name
                nuban = account_details.account_number
            else:
                account_name = None
                bank = None
                nuban = None
        sales_ledger_transactions = models.CashBook.objects.filter(
            company=company_id, branch=branch_id
        )
        # NOTE: card (POS/Terminal) transaction(s) value and/or balance is available @LibertyPay.
        # NOTE: transfer transaction(s) value and/or balance is available @CoreBanking (Wema Bank Plc)
        # which is also represented on Paybox360.
        # NOTE: wallet balance should only reflect today's current inflow (CARD/TRANSFER).
        today_inflow = sales_ledger_transactions.filter(
            created_at__date=date.today(),
            cash_book_type=enums.CashBookChoices.CASH_IN_BANK,
        )
        inflow_balance = today_inflow.aggregate(amount=Sum("amount"))["amount"] or 0.0

        if result_type and is_valid_string(result_type):
            # check for payment channel result type before other known filter(s).
            if result_type.upper() == enums.MeansOfPaymentChoices.CARD:
                sales_ledger_transactions = sales_ledger_transactions.filter(
                    payment_channel=enums.MeansOfPaymentChoices.CARD
                )
                if not start_date and not end_date:
                    result_type = FilterTypes.THIS_MONTH
                else:
                    result_type = FilterTypes.CUSTOM
            if result_type.upper() == enums.MeansOfPaymentChoices.TRANSFER:
                sales_ledger_transactions = sales_ledger_transactions.filter(
                    payment_channel=enums.MeansOfPaymentChoices.TRANSFER
                )
                if not start_date and not end_date:
                    result_type = FilterTypes.THIS_MONTH
                else:
                    result_type = FilterTypes.CUSTOM
            sales_ledger_transactions = QuerysetCustomFilter.date_range_filter(
                queryset=sales_ledger_transactions,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
            )
            if not isinstance(sales_ledger_transactions, QuerySet):
                return Response(
                    errors=sales_ledger_transactions,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        if reference:
            sales_ledger_transactions = sales_ledger_transactions.filter(
                Q(reference=reference) | Q(sales_transaction__batch_id=reference)
            )
        paginated_data = Paginator.paginate(
            request=request, queryset=sales_ledger_transactions
        )
        serializer = self.serializer_class(instance=paginated_data, many=True)
        data = {
            "wallet_balance": inflow_balance,
            "account_name": account_name,
            "bank": bank,
            "nuban": nuban,
            "payments": serializer.data,
            "count": len(serializer.data),
            "total_payments": sales_ledger_transactions.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class OfflineSalesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.OfflineSalesSerializer

    def post(self, request, *args, **kwargs):
        ############################################################################
        #                       MONITOR USER API REQUESTS                          #
        #                                                                          #
        ############################################################################
        api_log = models.APILog.objects.create(
            user=request.user,
            method=request.method,
            path="/api/v1/sales/register_offline/",
            request_headers=request.headers,
            request_body=request.data,
        )
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales = models.SalesTransaction.register_offline(**serializer.validated_data)
        api_log.response_body = str(sales)
        api_log.sales_batch_id = sales.get("batch_id")
        api_log.status_code = "201"
        api_log.save()
        return Response(data=sales, status_code=201, status=status.HTTP_201_CREATED)


class OfflineVirtualAccounts(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.OfflineVirtualAccountSerializer

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        virtual_accounts = models.OfflineVirtualAccount.objects.filter(
            company=company, branch=branch
        )
        paginated_data = Paginator.paginate(request=request, queryset=virtual_accounts)
        serializer = self.serializer_class(instance=paginated_data, many=True)
        return Response(
            data={"virtual_accounts": serializer.data},
            status_code=201,
            status=status.HTTP_201_CREATED,
        )


class WalletWithdrawalAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
        CompanyOwnerPermission,
    ]
    serializer_class = serializers.WalletWithdrawalSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        branch = serializer.validated_data["branch"]
        pin = serializer.validated_data["transaction_pin"]
        withdrawal_type = serializer.validated_data["withdrawal_type"]

        verified_pin = User.check_sender_payroll_pin(user=request.user, pincode=pin)
        if not verified_pin:
            return Response(
                errors={"message": "Invalid user transaction pin."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        account_details = AccountSystem.objects.filter(
            branch=branch,
            account_type=AccountType.SALES,
        ).last()
        if account_details is None:
            return Response(
                errors={"message": "Branch has no account details, contact support."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        charges = models.ConstantVariable.objects.first().sales_withdrawal_charge
        if withdrawal_type == "MAIN":
            account_name = request.user.account_name
            account_number = request.user.account_no
            if account_name is None or account_number is None:
                return Response(
                    errors={
                        "message": "Company has no main account details, contact support."
                    },
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            resolve_name = Paystack().name_enquiry("566", account_number)
            enquired_name = None
            if resolve_name.get("status"):
                enquired_name = resolve_name.get("data").get("account_name")
            withdrawal_request = models.WalletWithdrawal.objects.create(
                status=enums.TransactionStatusChoices.PENDING,
                user=request.user,
                company=serializer.validated_data["company"],
                branch=branch,
                bank_name="VFD",
                bank_code="090110",
                account_name=(
                    enquired_name if enquired_name is not None else account_name
                ),
                account_number=account_number,
                amount=serializer.validated_data["amount"],
                narration=f"{branch.__str__()} - {datetime.now()}",
                withdrawal_type=withdrawal_type,
                is_saved=serializer.validated_data["is_saved"],
            )
        if withdrawal_type == "EXTERNAL":
            withdrawal_request = models.WalletWithdrawal.objects.create(
                status=enums.TransactionStatusChoices.PENDING,
                user=request.user,
                company=serializer.validated_data["company"],
                branch=branch,
                bank_name=serializer.validated_data["bank_name"],
                bank_code=serializer.validated_data["bank_code"],
                account_name=serializer.validated_data["account_name"],
                account_number=serializer.validated_data["account_number"],
                amount=serializer.validated_data["amount"],
                narration=f"{branch.__str__()} - {datetime.now()}",
                withdrawal_type=withdrawal_type,
                is_saved=serializer.validated_data["is_saved"],
            )
        funds_transfer = Transaction.wema_funds_transfer(
            request_id=withdrawal_request.id,
            bank_code=withdrawal_request.bank_code,
            bank_name=withdrawal_request.bank_name,
            account_name=withdrawal_request.account_name,
            account_number=withdrawal_request.account_number,
            narration=withdrawal_request.narration,
            amount=int(withdrawal_request.amount),
            account=account_details,
            user=request.user,
            charges=charges,
            company_owner=withdrawal_request.company.user,
        )
        if isinstance(funds_transfer, dict):
            if not funds_transfer.get("is_chargeable"):
                withdrawal_request.status = enums.TransactionStatusChoices.DENIED
                withdrawal_request.save()
                return Response(
                    errors={"message": "insufficient funds, cannot process request."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        data = {"message": "successfully processed withdrawal"}
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        wallets = (
            models.WalletWithdrawal.objects.filter(
                user=request.user,
                is_saved=True,
            )
            .order_by("account_number")
            .distinct("account_number")
        )
        paginated_data = Paginator.paginate(request=request, queryset=wallets)
        serializer = serializers.WalletBeneficiarySerializer(
            instance=paginated_data, many=True
        )
        data = {
            "accounts": serializer.data,
            "count": len(serializer.data),
            "total": wallets.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class TopFourBranchesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        top_four_branches = models.SalesTransaction.get_top_four_branches(
            company=company,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not top_four_branches.get("status"):
            return Response(
                errors={"message": top_four_branches.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = serializers.TopFourBranchesSerializer(
            instance=top_four_branches.get("details"), many=True
        )
        return Response(
            data={"branches": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class TopFourCustomersAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        top_four_customers = models.SalesTransaction.get_top_four_customers(
            company=company,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not top_four_customers.get("status"):
            return Response(
                errors={"message": top_four_customers.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = serializers.TopFourCustomersSerializer(
            instance=top_four_customers.get("details"), many=True
        )
        return Response(
            data={"customers": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class StockAndSalesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        inventory_details = StockHistory.stocks_and_sales_comparison(
            company=company,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not inventory_details.get("status"):
            return Response(
                errors={"message": inventory_details.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = serializers.StockAndSalesSerializer(
            instance=inventory_details.get("details"), many=True
        )
        return Response(
            data={"report": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class CashAtHandAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        cash_book = models.CashBook.objects.filter(
            company=company,
            cash_book_type=enums.CashBookChoices.CASH_IN_HAND,
            created_at__date=date.today(),
        )
        cash_at_hand = (
            cash_book.aggregate(total_amount=Sum("amount"))["total_amount"] or 0.0
        )
        details = (
            cash_book.annotate(branch_name=F("branch__name"))
            .values("branch_name")
            .annotate(
                cash_balance=Sum("amount"),
                last_updated=Min("updated_at"),
            )
            .values("branch_name", "cash_balance", "last_updated")
        )
        serializer = serializers.CompanyCashBookSerializer(instance=details, many=True)
        return Response(
            data={
                "cash_at_hand": cash_at_hand,
                "report": serializer.data,
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )


class ProfitTrackingAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        profit_details = models.SalesTransactionItem.profit_tracking(
            company=company,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not profit_details.get("status"):
            return Response(
                errors={"message": profit_details.get("details")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = serializers.BranchComparisonSerializer(
                instance=profit_details.get("details"), many=True
            )
            data = {"message": "success", "total_sales": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class TopTenProductsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.TopTenProductsSerializer

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if result_type is None:
            return Response(
                errors={"message": "provide a valid result type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        top_products = models.SalesTransactionItem.top_ten_products(
            company=company,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        serializer = self.serializer_class(instance=top_products, many=True)
        data = {"message": "success", "products": serializer.data}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class ActiveClerksAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        sales_transaction = models.SalesTransaction.objects.filter(
            company=company
        ).filter(
            Q(status=enums.TransactionStatusChoices.SUCCESSFUL)
            | Q(status=enums.TransactionStatusChoices.CREDIT)
            | Q(status=enums.TransactionStatusChoices.SAVED)
        )
        if result_type and is_valid_string((result_type)):
            sales_transaction = QuerysetCustomFilter.date_range_filter(
                queryset=sales_transaction,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
            )
            if not isinstance(sales_transaction, QuerySet):
                return Response(
                    errors=sales_transaction,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        details = (
            sales_transaction.annotate(clerk=F("created_by"))
            .values("clerk")
            .annotate(
                clerk_name=Case(
                    When(
                        created_by__username__isnull=False,
                        then=F("created_by__username"),
                    ),
                    default=F("created_by__first_name"),
                ),
                branch_name=F("branch__name"),
                total_sales=Count("created_by"),
                amount_sold=Sum("total_sales_amount"),
                last_transaction=Min("updated_at"),
            )
            .order_by("-amount_sold", "-total_sales")
            .values(
                "clerk",
                "clerk_name",
                "branch_name",
                "total_sales",
                "amount_sold",
                "last_transaction",
            )
        )
        serializer = serializers.ActiveClerksSerializer(instance=details, many=True)
        return Response(
            data={"report": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class SalesOrderAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
        IsPipelineUserRole,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        batch_id = request.query_params.get("batch_id")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if batch_id and is_valid_string(batch_id):
            sales_transaction = models.SalesTransaction.objects.filter(
                company=company,
                branch=branch,
                batch_id=batch_id,
            ).last()
            serializer = serializers.SalesOrderDetailsSerializer(
                instance=sales_transaction
            )
            data = {
                "batch_id": batch_id,
                "order_details": serializer.data,
            }
        else:
            sales_transactions = models.SalesTransaction.pipeline_orders(
                company=company,
                branch=branch,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
            )
            if not sales_transactions.get("status"):
                return Response(
                    errors=sales_transactions,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            serializer = serializers.SalesOrderSerializer(
                instance=sales_transactions.get("orders"), many=True
            )
            data = {
                "total_orders": sales_transactions.get("total_orders"),
                "pipeline_stages": sales_transactions.get("stages"),
                "orders": serializer.data,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def patch(self, request, *args, **kwargs):
        serializer = serializers.ManageSavedSalesSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales_transaction = models.SalesTransaction.manage_sales(
            user=request.user, fulfilled=True, **serializer.validated_data
        )
        if sales_transaction is None:
            return Response(
                errors={"message": "invalid sales transaction"},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = serializers.SalesTransactionSerializer(
            instance=sales_transaction.get("data")
        )
        return Response(
            data=sales_transaction, status_code=200, status=status.HTTP_200_OK
        )

    def put(self, request, *args, **kwargs):
        serializer = serializers.ApproveOrderSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales_transaction = models.SalesTransaction.objects.filter(
            company=serializer.validated_data["company"],
            branch=serializer.validated_data["branch"],
            batch_id=serializer.validated_data["batch_id"],
        ).last()
        if sales_transaction is None:
            return Response(
                errors={"message": "sales transaction not found"},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        items = models.SalesTransactionItem.objects.filter(
            sales_transaction=sales_transaction, bar_alert=True
        )
        cancelled_items = items.filter(status=enums.OrderStatus.cancelled)
        cancelled_items.update(is_active=False, is_deleted=True)
        items.update(bar_alert=False, approved=True)
        active_items = models.SalesTransactionItem.objects.filter(
            sales_transaction=sales_transaction, is_active=True
        )
        sales_transaction.total_sales_amount = active_items.aggregate(
            amount=Sum("total_value")
        )["amount"]
        sales_transaction.bar_alert = False
        sales_transaction.reduction = False
        sales_transaction.removal = False
        sales_transaction.save()
        return Response(
            data={"message": "order approved successfully."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class SalesWalletHistoryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        branch_id = request.query_params.get("branch")
        account_details = AccountSystem.objects.filter(
            branch=branch_id,
            account_type=AccountType.SALES,
        ).last()
        if account_details:
            wallet_history = Transaction.objects.filter(
                Q(beneficiary_account_number=account_details.account_number)
                | Q(source_account_number=account_details.account_number)
            ).exclude(beneficiary_account_number="**********")
            total = wallet_history.count()
        else:
            wallet_history = []
            total = 0
        paginated_data = Paginator.paginate(request=request, queryset=wallet_history)
        serializer = serializers.SalesWalletHistorySerializer(
            instance=wallet_history, many=True
        )
        data = {
            "transactions": serializer.data,
            "count": len(serializer.data),
            "total": total,
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CreditRecordSummaryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        credit_summary = (
            models.CreditRecord.objects.filter(company=company, branch=branch)
            .values("branch")
            .annotate(
                company_name=F("company__company_name"),
                branch_name=F("branch__name"),
                total_credits=Count("id"),
                total_creditors=Count("customer__id", distinct=True),
                total_sales_amount=Sum("purchase_amount"),
                total_amount_paid=Sum("amount_paid"),
                total_amount_due=Sum("amount_due"),
            )
        )
        serializer = serializers.CreditRecordSummarySerializer(
            instance=credit_summary, many=True
        )
        return Response(
            data={"credit_summary": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class CreditRecordAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        search = request.query_params.get("search")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        credit_records = models.CreditRecord.objects.filter(
            company=company, branch=branch
        )
        if search and is_valid_string(search):
            credit_records = credit_records.filter(
                Q(sales_transaction__batch_id__icontains=search)
                | Q(sales_transaction__sales_tag__icontains=search)
                | Q(customer__name__icontains=search)
                | Q(customer__email__icontains=search)
                | Q(customer__phone__icontains=search)
            )
        if result_type and is_valid_string((result_type)):
            credit_records = QuerysetCustomFilter.date_range_filter(
                queryset=credit_records,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
            )
            if not isinstance(credit_records, QuerySet):
                return Response(
                    errors=credit_records,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        paginated_data = Paginator.paginate(request=request, queryset=credit_records)
        serializer = serializers.CreditRecordSerializer(
            instance=paginated_data, many=True
        )
        data = {
            "credit_records": serializer.data,
            "count": len(serializer.data),
            "total": credit_records.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CreditRecordDetailsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        batch_id = request.query_params.get("batch_id")

        if not batch_id:
            return Response(
                errors={"message": "provide a valid batch ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        credit_records = models.CreditRecord.objects.filter(
            company=company,
            branch=branch,
            sales_transaction__batch_id=batch_id,
        )
        serializer = serializers.CreditRecordDetailsSerializer(
            instance=credit_records, many=True
        )
        data = {"credit_details": serializer.data}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CreditPaymentAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def post(self, request, *args, **kwargs):
        serializer = serializers.CreditPaymentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        credit_record = models.CreditRecord.objects.filter(
            sales_transaction__company=serializer.validated_data["company"],
            sales_transaction__branch=serializer.validated_data["branch"],
            sales_transaction__batch_id=serializer.validated_data["batch_id"],
        ).last()
        if credit_record is None:
            return Response(
                errors={"message": "invalid credit record"},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if credit_record.amount_due < serializer.validated_data["amount"]:
            return Response(
                errors={"message": "amount paid is greater than amount due"},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        models.SalesTransaction.objects.create(
            company=serializer.validated_data["company"],
            branch=serializer.validated_data["branch"],
            customer=credit_record.customer,
            payment_reference=serializer.validated_data["batch_id"],
            means_of_payment=serializer.validated_data["means_of_payment"],
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            amount_paid=serializer.validated_data["amount"],
            paid=True,
            paid_at=TODAY,
            created_by=request.user,
            updated_by=request.user,
            device=credit_record.sales_transaction.device,
        )
        total_amount_paid = (
            models.SalesTransaction.objects.filter(
                payment_reference=serializer.validated_data["batch_id"]
            ).aggregate(total=Sum("amount_paid"))["total"]
            or 0.0
        )
        if total_amount_paid == credit_record.purchase_amount:
            credit_record.status = enums.CreditStatus.PAID
            credit_record.amount_paid = total_amount_paid
            credit_record.amount_due = 0
        else:
            credit_record.status = enums.CreditStatus.PART_PAYMENT
            credit_record.amount_paid = total_amount_paid
            credit_record.amount_due = float(credit_record.purchase_amount) - float(
                total_amount_paid
            )
        credit_record.save()
        return Response(
            data={"message": "payment recorded successfully."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class RegisterSalesFromPOSAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.RegisterPOSOrderSerializer

    def post(self, request, *args, **kwargs):
        ############################################################################
        #                       MONITOR USER API REQUESTS                          #
        #                                                                          #
        ############################################################################
        api_log = models.APILog.objects.create(
            user=request.user,
            method=request.method,
            path="/api/v1/sales/register_sales_from_pos/",
            request_headers=request.headers,
            request_body=request.data,
        )
        serializer = self.serializer_class(
            data=request.data, context={"user": request.user}
        )
        serializer.is_valid(raise_exception=True)
        sales = models.SalesTransaction.manage_sales(
            user=request.user, register=True, **serializer.validated_data
        )
        api_log.response_body = str(sales)
        api_log.sales_batch_id = sales.get("batch_id")
        api_log.status_code = "201"
        api_log.save()

        return Response(data=sales, status_code=201, status=status.HTTP_201_CREATED)


class ConfirmSalesFromPOSAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.ConfirmSalesFromPOSSerializer

    @transaction.atomic
    def patch(self, request, *args, **kwargs):
        ############################################################################
        #                       MONITOR USER API REQUESTS                          #
        #                                                                          #
        ############################################################################
        api_log = models.APILog.objects.create(
            user=request.user,
            method=request.method,
            path="/api/v1/sales/confirm_sales_from_pos/",
            request_headers=request.headers,
            request_body=request.data,
        )
        serializer = self.serializer_class(
            data=request.data, context={"user": request.user}
        )
        serializer.is_valid(raise_exception=True)
        sales_transaction = models.SalesTransaction.confirm_sales(
            updated_by=request.user,
            **serializer.validated_data,
        )
        if sales_transaction is None:
            data = ({"message": "invalid sales transaction."},)
            api_log.sales_batch_id = serializer.validated_data.get("batch_id")
            api_log.response_body = data
            api_log.status_code = 400
            api_log.save()
            return DRFResponse(
                data=data,
                status=status.HTTP_400_BAD_REQUEST,
            )
        items = models.SalesTransactionItem.objects.filter(
            sales_transaction=sales_transaction,
            is_active=True,
        )
        count = 1
        items__depletion_history = {}
        for data in items:
            if data.bar_alert:
                raise drf_serializers.ValidationError(
                    {"message": f"{data.item.name} requires manager approval."}
                )
            # 2024-10-02 act on confirmation.
            deplete_response = deplete_branch_quantity_available(
                company=data.company,
                branch=data.branch,
                category=data.category,
                item=data.item,
                quantity=data.quantity,
                sales_transaction=sales_transaction,
                on_hold=False,
            )
            if not isinstance(deplete_response, bool):
                data.quantity_before = deplete_response.get("quantity_before")
                data.quantity_after = deplete_response.get("quantity_after")
                data.updated_by = request.user
                data.status = enums.OrderStatus.completed
                data.save()
                register_stock_history(
                    company=data.company,
                    branch=data.branch,
                    category=data.category,
                    item=data.item,
                    price=data.amount,
                    quantity_before=deplete_response.get("quantity_before"),
                    quantity=data.quantity,
                    quantity_after=deplete_response.get("quantity_after"),
                    transaction_type=stock_enums.StockHistoryChoices.SALES,
                    status=stock_enums.StockHistoryStatusChoices.OUTGOING,
                    created_by=request.user,
                )
                item_deplete_history = {
                    "company": data.company.company_name,
                    "branch": data.branch.name,
                    "category": data.category.name,
                    "category_id": str(data.category.id),
                    "product": data.item.name,
                    "product_id": str(data.item.id),
                    "deplete_quantity": deplete_response.get("deplete_quantity"),
                    "quantity_before": deplete_response.get("quantity_before"),
                    "quantity_after": deplete_response.get("quantity_after"),
                    "deplete_message": deplete_response.get("message"),
                }
                items__depletion_history[f"item{count}"] = item_deplete_history
            count += 1
        serializer = serializers.SalesItemSerializer(instance=items, many=True)
        if sales_transaction.customer is not None:
            customer = {
                "name": sales_transaction.customer.name,
                "phone": sales_transaction.customer.phone,
                "email": sales_transaction.customer.email,
                "address": sales_transaction.customer.address,
            }
        else:
            customer = {
                "name": None,
                "phone": None,
                "email": None,
                "address": None,
            }
        data = {
            "message": "success",
            "company_name": sales_transaction.company.company_name,
            "branch_name": sales_transaction.branch.name,
            "branch_address": sales_transaction.branch.address,
            "branch_phone": sales_transaction.branch.phone,
            "cashier": sales_transaction.created_by.full_name,
            "updated_at": sales_transaction.updated_at,
            "invoice_id": (
                sales_transaction.invoice.reference
                if sales_transaction.invoice is not None
                else ""
            ),
            "batch_id": sales_transaction.batch_id,
            "sub_total": sales_transaction.total_sales_amount,
            "discount_value": sales_transaction.discount_value,
            "delivery_fee": sales_transaction.delivery_fee,
            "vat": sales_transaction.vat_amount,
            "charges": sales_transaction.charges,
            "total": sales_transaction.total_cost,
            "items": serializer.data,
            "means_of_payment": sales_transaction.means_of_payment,
            "customer": customer,
        }
        api_log.sales_batch_id = sales_transaction.batch_id
        api_log.response_body = str(data)
        api_log.items_depletion_history = items__depletion_history
        api_log.status_code = 200
        api_log.save()
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CollectMerchantAccountDetailAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.CollectMerchantAccountSerializer
    response_serializer_class = serializers.MerchantAccountSummarySerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        insstance = serializer.save()
        data = {
            "message": "successful",
            "data": self.response_serializer_class(instance=insstance).data,
        }
        return Response(
            data=data,
            status_code=201,
            status=status.HTTP_201_CREATED,
        )

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        merchant_account_details = (
            models.MerchantAccountDetails.objects.filter(
                company=company,
                branch=branch,
            )
            .order_by("bank_code")
            .distinct("bank_code")
        )
        serializer = serializers.MerchantAccountSummarySerializer(
            instance=merchant_account_details, many=True
        )
        data = {
            "message": "successful",
            "data": serializer.data,
            "count": len(serializer.data),
        }
        return Response(
            data=data,
            status_code=200,
            status=status.HTTP_200_OK,
        )


class POSBranchInstantAccountAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.POSBranchInstantAccountSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        sales_transaction = models.SalesTransaction.objects.filter(
            company=request.user.default_company,
            branch=request.user.default_branch,
            batch_id=serializer.validated_data.get("batch_id"),
            status=enums.TransactionStatusChoices.PENDING,
        ).first()
        if sales_transaction is None:
            return Response(
                errors={"message": "invalid sales transaction"},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        request_handler = CoreBankingService()
        response = request_handler.get_one_time_account(
            request_reference=sales_transaction.batch_id
        )
        if response.get("status"):
            if not isinstance(response.get("data"), str):
                response_data = response.get("data").get("data").get("account_details")
                data = {
                    "amount": sales_transaction.total_cost,
                    "bank_name": response_data.get("bank_name"),
                    "bank_code": response_data.get("bank_code"),
                    "account_name": response_data.get("account_name"),
                    "account_number": response_data.get("account_number"),
                }
            else:
                data = {
                    "amount": 0.0,
                    "bank_name": None,
                    "bank_code": None,
                    "account_name": None,
                    "account_number": None,
                }
        else:
            data = {
                "amount": 0.0,
                "bank_name": None,
                "bank_code": None,
                "account_name": None,
                "account_number": None,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class POSBranchVerifyTransferAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.POSBranchInstantAccountSerializer

    def get(self, request, *args, **kwargs):
        batch_id = request.query_params.get("batch_id")
        sales_transaction = models.SalesTransaction.objects.filter(
            company=request.user.default_company,
            branch=request.user.default_branch,
            batch_id=batch_id,
        ).first()
        if sales_transaction is None:
            return DRFResponse(
                {
                    "status": False,
                    "message": "invalid sales transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        payment_received = CoreBankingCallback.objects.filter(
            request_reference=sales_transaction.batch_id
        ).first()
        if payment_received is None:
            data = {
                "status": False,
                "message": "awaiting payment.",
                "amount": 0.0,
            }
        else:
            data = {
                "status": True,
                "message": "payment received successfully.",
                "amount": float(payment_received.amount),
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BankListAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        from account.helpers.core_banking import CoreBankingService

        handler = CoreBankingService()
        if settings.ENVIRONMENT == "prod":
            BASE_URL = "https://backend.libertypayng.com"
        else:
            BASE_URL = "https://dev.libertypayng.com"

        response = handler.request_handler(
            "GET",
            dict(
                url=f"{BASE_URL}/accounts/bank_list/",
            ),
        )
        if response.get("status"):
            return Response(
                data=response,
                status_code=200,
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                errors=response,
                status_code=500,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DownloadSalesHistoryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        transaction_status = request.query_params.get("transaction_status")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        # New time-based parameters
        start_hour = request.query_params.get("start_hour")
        end_hour = request.query_params.get("end_hour")

        if Branch.retrieve_company_branch(id=branch_id, company=company_id) is None:
            return Response(
                errors={"message": "provide a valid branch ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_sales_transaction = models.SalesTransaction.objects.filter(
            company=company_id,
            branch=branch_id,
            batch_id__isnull=False,
        )
        if transaction_status and is_valid_string(transaction_status):
            branch_sales_transaction = branch_sales_transaction.filter(
                status=transaction_status.upper()
            )
        if result_type and is_valid_string((result_type)):
            branch_sales_transaction = QuerysetCustomFilter.date_range_filter(
                queryset=branch_sales_transaction,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
                start_hour=start_hour,
            )
            if not isinstance(branch_sales_transaction, QuerySet):
                return Response(
                    errors=branch_sales_transaction,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        if branch_sales_transaction.exists():
            aggregated_data = (
                branch_sales_transaction.values("batch_id")
                .annotate(
                    created_at=Min("created_at"),
                    company=F("company__company_name"),
                    branch=F("branch__name"),
                    transaction_id=F("batch_id"),
                    cashier=F("created_by__first_name"),
                    amount=Sum("total_sales_amount"),
                    payment_method=F("means_of_payment"),
                    status=F("status"),
                    invoice=F("invoice__reference"),
                )
                .order_by("-created_at")
            )
            data_list = list(aggregated_data)
            df = pd.DataFrame(data_list)
            df.rename(
                columns={
                    "created_at": "Date",
                    "company": "Company",
                    "branch": "Branch",
                    "transaction_id": "Transaction ID",
                    "cashier": "Cashier",
                    "amount": "Amount",
                    "payment_method": "Payment Method",
                    "status": "Status",
                    "invoice": "Invoice Reference",
                },
                inplace=True,
            )
            df["Date"] = pd.to_datetime(df["Date"]).dt.strftime("%Y-%m-%d %H:%M:%S")
            # Create Excel file in memory
            excel_file = BytesIO()
            df.to_excel(excel_file, index=False, sheet_name="Sales Transaction History")
            excel_file.seek(0)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"sales_history_{timestamp}.xlsx"

            response = HttpResponse(
                excel_file.getvalue(),
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
            response["Content-Disposition"] = f"attachment; filename={filename}"

            return response
        else:
            return Response(
                errors={"message": "no available transaction(s) to download."},
                status_code=404,
                status=status.HTTP_404_NOT_FOUND,
            )


class DownloadSalesItemsHistoryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        transaction_status = request.query_params.get("transaction_status")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        # New time-based parameters
        start_hour = request.query_params.get("start_hour")
        end_hour = request.query_params.get("end_hour")

        if Branch.retrieve_company_branch(id=branch_id, company=company_id) is None:
            return Response(
                errors={"message": "provide a valid branch ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )

        transaction_items = models.SalesTransactionItem.objects.filter(
            sales_transaction__company=company_id,
            sales_transaction__branch=branch_id,
            sales_transaction__batch_id__isnull=False,
        ).select_related("sales_transaction", "sales_transaction__created_by", "item")

        if transaction_status and is_valid_string(transaction_status):
            transaction_items = transaction_items.filter(
                sales_transaction__status=transaction_status.upper()
            )
        if not transaction_status or not is_valid_string(transaction_status):
            transaction_items = transaction_items.filter(
                Q(sales_transaction__status=enums.TransactionStatusChoices.SUCCESSFUL)
                | Q(sales_transaction__status=enums.TransactionStatusChoices.SAVED)
            )
        if result_type and is_valid_string((result_type)):
            transaction_items = QuerysetCustomFilter.date_range_filter(
                queryset=transaction_items,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
                start_hour=start_hour,
                end_hour=end_hour,
            )
            if not isinstance(transaction_items, QuerySet):
                return Response(
                    errors=transaction_items,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        if transaction_items.exists():
            data_list = []
            for item in transaction_items:
                data_list.append(
                    {
                        "created_at": item.created_at,
                        "cashier": item.sales_transaction.created_by.full_name,
                        "batch_id": item.sales_transaction.batch_id,
                        "item": item.item.name,
                        "quantity": item.quantity,
                        "unit_price": float(item.amount),
                        "total": float(item.total_value),
                        "status": item.sales_transaction.status,
                        "payment_method": item.sales_transaction.means_of_payment,
                    }
                )
            df = pd.DataFrame(data_list)
            df.rename(
                columns={
                    "created_at": "Date",
                    "cashier": "Cashier",
                    "batch_id": "Batch ID",
                    "item": "Item",
                    "quantity": "Quantity",
                    "unit_price": "Unit Price",
                    "total": "Total",
                    "status": "Status",
                    "payment_method": "Payment Method",
                },
                inplace=True,
            )
            df["Date"] = pd.to_datetime(df["Date"]).dt.strftime("%Y-%m-%d %H:%M:%S")
            # Create Excel file in memory
            excel_file = BytesIO()
            df.to_excel(excel_file, index=False, sheet_name="Sales Items History")
            excel_file.seek(0)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"sales_items_history_{timestamp}.xlsx"

            response = HttpResponse(
                excel_file.getvalue(),
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
            response["Content-Disposition"] = f"attachment; filename={filename}"

            return response
        else:
            return Response(
                errors={"message": "no available transaction items to download."},
                status_code=404,
                status=status.HTTP_404_NOT_FOUND,
            )
