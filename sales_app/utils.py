from datetime import datetime
import math

from django.conf import settings
from django.db.models import Count, Q
import pytz
from rest_framework import serializers


User = settings.AUTH_USER_MODEL


# Create your utility function(s) here.
def get_vat_value(
    vat: float,
    amount: float,
):
    return math.ceil((vat / 100) * float(amount))


def validate_customer(customer_id: str):
    from sales_app.models import Customer

    """
    Validate the existence of a customer by its ID.
    Returns:
        Customer: The customer object if it exists.
    Raises:
        serializers.ValidationError: If the customer with the provided ID does not exist.
    """
    try:
        customer = Customer.objects.get(id=customer_id)
    except Customer.DoesNotExist:
        raise serializers.ValidationError({"message": "provide a valid customer ID."})
    return customer


def validate_batch_id(batch_id: str):
    from sales_app.models import SalesTransaction

    """
    Validate the existence of a sales transaction by its batch ID.
    Args:
        batch_id (str): The unique identifier of the sales transaction to validate.
    Returns:
        batch_id(str): The validated batch ID.
    Raises:
        serializers.ValidationError: If no record matches the provided ID.
    """
    sales_transactions = SalesTransaction.objects.filter(batch_id=batch_id)
    if not sales_transactions.exists():
        raise serializers.ValidationError({"message": f"invalid sales batch ID."})
    return batch_id


def validate_number(number: int):
    """
    Validate the number provided to be of positive value.
    Args:
        number (int): The number to validate.
    Returns:
        number (int): The validated number.
    Raises:
        serializers.ValidationError: If the number is invalid.
    """
    if number <= 0:
        raise serializers.ValidationError("A valid integer is required.")
    return True


def validate_amount(amount: float):
    """
    Validate the amount provided to be of positive value.
    Args:
        amount (int): The amount to validate.
    Returns:
        amount (int): The validated amount.
    Raises:
        serializers.ValidationError: If the amount is invalid.
    """
    if amount < 0:
        raise serializers.ValidationError("A valid amount is required.")
    return True


def split_payment(
    user: User,
    sales_transaction: object,
    split_method: dict,
):
    from sales_app.helper import enums
    from sales_app.models import SalesTransaction

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

    total_amount = 0
    if split_method.get("cash"):
        amount = split_method.get("cash_amount")
        total_amount += amount
        SalesTransaction.objects.create(
            company=sales_transaction.company,
            branch=sales_transaction.branch,
            customer=sales_transaction.customer,
            payment_reference=sales_transaction.batch_id,
            means_of_payment=enums.MeansOfPaymentChoices.CASH,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            amount_paid=amount,
            paid=True,
            paid_at=TODAY,
            created_by=user,
            updated_by=user,
            device=sales_transaction.device,
        )
    if split_method.get("others"):
        amount = split_method.get("others_amount")
        total_amount += amount
        SalesTransaction.objects.create(
            company=sales_transaction.company,
            branch=sales_transaction.branch,
            customer=sales_transaction.customer,
            payment_reference=sales_transaction.batch_id,
            means_of_payment=enums.MeansOfPaymentChoices.OTHERS,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            amount_paid=amount,
            paid=True,
            paid_at=TODAY,
            created_by=user,
            updated_by=user,
            device=sales_transaction.device,
        )
    if split_method.get("transfer"):
        amount = split_method.get("transfer_amount")
        total_amount += amount
        SalesTransaction.objects.create(
            company=sales_transaction.company,
            branch=sales_transaction.branch,
            customer=sales_transaction.customer,
            payment_reference=sales_transaction.batch_id,
            means_of_payment=enums.MeansOfPaymentChoices.TRANSFER,
            status=enums.TransactionStatusChoices.IN_PROGRESS,
            created_by=user,
            updated_by=user,
            device=sales_transaction.device,
        )
    if split_method.get("other_transfer"):
        amount = split_method.get("other_transfer_amount")
        total_amount += amount
        SalesTransaction.objects.create(
            company=sales_transaction.company,
            branch=sales_transaction.branch,
            customer=sales_transaction.customer,
            payment_reference=sales_transaction.batch_id,
            means_of_payment=enums.MeansOfPaymentChoices.OTHER_TRANSFER,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            amount_paid=amount,
            paid=True,
            paid_at=TODAY,
            created_by=user,
            updated_by=user,
            device=sales_transaction.device,
        )
    return total_amount


def get_company_transaction_counts_by_date(action_date: str) -> dict:
    """
    Returns a dictionary with company IDs as keys and their transaction counts as values
    for transactions with status 'SUCCESSFUL' or 'CREDIT' on a specific date.

    Args:
        date_str (str): Date in format "YYYY-MM-DD"

    Returns:
        dict: Dictionary with company_id as key and transaction count as value

    """
    from sales_app.models import SalesTransaction
    from sales_app.helper import enums

    try:
        target_date = datetime.strptime(action_date, "%Y-%m-%d").date()
    except ValueError:
        raise ValueError("Date must be in format 'YYYY-MM-DD'")

    transactions = (
        SalesTransaction.objects.filter(created_at__date=target_date)
        .filter(
            Q(status=enums.TransactionStatusChoices.SUCCESSFUL)
            | Q(status=enums.TransactionStatusChoices.CREDIT)
        )
        .values("company_id")
        .annotate(transaction_count=Count("id"))
    )
    result = {}
    for transaction in transactions:
        result[str(transaction["company_id"])] = transaction["transaction_count"]
    return result
