from django.core.management.base import BaseCommand
from django.db import transaction

from core.models import UploadedMedia


class Command(BaseCommand):
    help = "Update existing signed media URLs to permanent public URLs"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be updated without making changes",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Number of records to process in each batch (default: 100)",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]
        batch_size = options["batch_size"]

        self.stdout.write(
            self.style.SUCCESS(
                f"Starting media URL update {'(DRY RUN)' if dry_run else ''}"
            )
        )

        # Get all UploadedMedia instances
        total_media = UploadedMedia.objects.count()
        self.stdout.write(f"Found {total_media} media files to process")

        updated_count = 0
        error_count = 0

        # Process in batches
        for offset in range(0, total_media, batch_size):
            media_batch = UploadedMedia.objects.all()[offset : offset + batch_size]

            with transaction.atomic():
                for media_instance in media_batch:
                    try:
                        # Get the current URL
                        old_url = (
                            str(media_instance.media.url)
                            if media_instance.media
                            else None
                        )

                        if not old_url:
                            self.stdout.write(
                                self.style.WARNING(
                                    f"Skipping media ID {media_instance.id}: No media file"
                                )
                            )
                            continue

                        # Check if URL has query parameters (signed URL)
                        if "?" in old_url and (
                            "Signature=" in old_url or "Expires=" in old_url
                        ):
                            # Generate new permanent URL
                            new_url = media_instance.media.url

                            if dry_run:
                                self.stdout.write(
                                    f"Would update media ID {media_instance.id}:"
                                )
                                self.stdout.write(f"  Old: {old_url}")
                                self.stdout.write(f"  New: {new_url}")
                            else:
                                # Force refresh the URL by accessing the field again
                                # This will generate a new URL with the updated storage settings
                                media_instance.save()
                                updated_count += 1

                                if updated_count % 10 == 0:
                                    self.stdout.write(
                                        f"Updated {updated_count} media URLs..."
                                    )
                        else:
                            # URL is already permanent (no query parameters)
                            if not dry_run:
                                self.stdout.write(
                                    f"Media ID {media_instance.id} already has permanent URL"
                                )

                    except Exception as e:
                        error_count += 1
                        self.stdout.write(
                            self.style.ERROR(
                                f"Error processing media ID {media_instance.id}: {str(e)}"
                            )
                        )

        # Now update any Product models or other models that store media URLs
        self.update_product_media_urls(dry_run, batch_size)

        # Summary
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f"DRY RUN COMPLETE: Would update {updated_count} media URLs"
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f"COMPLETE: Updated {updated_count} media URLs with {error_count} errors"
                )
            )

    def update_product_media_urls(self, dry_run, batch_size):
        """Update Product model URLs if they exist"""
        try:
            # Import Product model from stock_inventory
            from stock_inventory.models import Product
            from django.db.models import Q

            self.stdout.write("Updating Product image URLs...")

            # Find products with signed URLs in any of the image fields
            products_with_signed_urls = Product.objects.filter(
                Q(product_image_1__isnull=False, product_image_1__contains="?")
                | Q(product_image_2__isnull=False, product_image_2__contains="?")
                | Q(product_image_3__isnull=False, product_image_3__contains="?")
                | Q(product_image_4__isnull=False, product_image_4__contains="?")
            )

            product_count = products_with_signed_urls.count()
            self.stdout.write(f"Found {product_count} products with signed image URLs")

            updated_products = 0
            image_fields = [
                "product_image_1",
                "product_image_2",
                "product_image_3",
                "product_image_4",
            ]

            for offset in range(0, product_count, batch_size):
                product_batch = products_with_signed_urls[offset : offset + batch_size]

                with transaction.atomic():
                    for product in product_batch:
                        try:
                            product_updated = False

                            # Check each image field
                            for field_name in image_fields:
                                old_url = getattr(product, field_name)

                                if (
                                    old_url
                                    and "?" in old_url
                                    and (
                                        "Signature=" in old_url or "Expires=" in old_url
                                    )
                                ):
                                    # Extract file path from URL
                                    if "media/" in old_url:
                                        # Extract the path after 'media/'
                                        file_path = old_url.split("media/")[1].split(
                                            "?"
                                        )[0]

                                        # Find corresponding UploadedMedia instance
                                        try:
                                            media_instance = UploadedMedia.objects.get(
                                                media__name=file_path
                                            )
                                            new_url = media_instance.media.url

                                            if dry_run:
                                                self.stdout.write(
                                                    f"Would update Product ID {product.id} {field_name}:"
                                                )
                                                self.stdout.write(f"  Old: {old_url}")
                                                self.stdout.write(f"  New: {new_url}")
                                            else:
                                                setattr(product, field_name, new_url)
                                                product_updated = True

                                        except UploadedMedia.DoesNotExist:
                                            self.stdout.write(
                                                self.style.WARNING(
                                                    f"No UploadedMedia found for Product ID {product.id} "
                                                    f"{field_name} with path: {file_path}"
                                                )
                                            )

                            # Save the product if any field was updated
                            if product_updated and not dry_run:
                                product.save()
                                updated_products += 1

                        except Exception as e:
                            self.stdout.write(
                                self.style.ERROR(
                                    f"Error updating Product ID {product.id}: {str(e)}"
                                )
                            )

            if not dry_run:
                self.stdout.write(
                    self.style.SUCCESS(f"Updated {updated_products} product URLs")
                )

        except ImportError:
            self.stdout.write(
                self.style.WARNING(
                    "Product model not found. Skipping product URL updates."
                )
            )
