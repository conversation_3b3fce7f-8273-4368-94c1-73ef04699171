import json
import logging
import os
from datetime import datetime
from string import Template

import pandas as pd
import pytz
import requests
from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.files import File
from django.db.models import Count
from django.db import transaction
from account.models import Transaction
from account.wallet_service import vfd_create_update_paybox_user_default_wallet
from core.helpers.apis.request_cls import LibertyPayPlus
from core.models import KycDetails, PaystackPayment, VfdMeeting, SalesAgents
from requisition.models import Budget, Expense, Requisition, AssetImage
from sales_app.models import SalesTransaction
from stock_inventory.models import SupplierProductImage
from storage_backend import StorageManager
from requisition.helpers.brevo_crm import BrevoMarketingApis
from django.utils import timezone
from datetime import timedelta
from pathlib import Path

logger = logging.getLogger(__name__)
User = get_user_model()


@shared_task
def send_email(
    recipient: str,
    subject: str,
    template_dir: str,
    use_template=True,
    body=None,
    **substitute,
):
    if use_template:
        TEMPLATE_DIR = os.path.join("templates", f"{template_dir}")
        print(TEMPLATE_DIR)
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitute)
    else:
        template = None
    try:
        requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_APIKEY}"),
            data={
                "from": "LibertyPay <<EMAIL>>",
                "to": f"<{recipient}>",
                "subject": f"{subject}",
                "html": f"""{template}""" if use_template else None,
                "text": body if not use_template else None,
            },
        )

        return "EMAIL SENT"
    except Exception as e:
        print("failed to send email", e)
        return "EMAIL FAILED"


@shared_task
def send_email_with_attachment(
    recipient: str,
    sender_email: str,
    cc_email: str,
    subject: str,
    template_dir: str,
    file_name: str,
    use_template=True,
    file=None,
    body=None,
    **substitute,
):
    if use_template:
        TEMPLATE_DIR = os.path.join("templates", f"{template_dir}")
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitute)
    else:
        template = None
    try:
        requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_APIKEY}"),
            data={
                "from": "Liberty Pay <<EMAIL>>",
                "to": f"<{recipient}>",
                "cc": f"{sender_email}",
                "bcc": f"{cc_email}",
                "subject": f"{subject}",
                "html": f"""{template}""" if use_template else None,
                "text": body if not use_template else None,
            },
            files=[(f"attachment", (f"{file_name}", file))],
        )
        # Delete the temporary attachment file (if applicable)
        if file_name and isinstance(file, pd.DataFrame):
            try:
                os.remove(f"{file_name}")
            except PermissionError:
                pass
        return "EMAIL SENT"
    except Exception as e:
        print("failed to send email", e)
        return "EMAIL FAILED"
    
@shared_task
def verify_requisition_transaction():
    logger.info(f"VERIFYING PENDING EXPENSES")
    logger.info(f"VERIFYING PENDING EXPENSES")

    all_expense = Expense.objects.filter(status="PENDING")

    # logger.info(f"PENDING EXPENSES --- {all_expense.count()}")

    for expense in all_expense:

        trans_ref = expense.transaction_ref
        logger.info(f"PENDING EXPENSES --- {expense.id}")
        # logger.info(f"Transaction reference{trans_ref} ")

        try:
            transaction = Transaction.objects.get(transaction_ref=trans_ref)
        except Transaction.DoesNotExist:
            logger.info(f"Expense Transaction ref {trans_ref} does not exist")
            continue

        logger.info(f"EXPENSE TRANSACTION STATUS --- {transaction.status}")
        expense.status = transaction.status
        expense.save()
        expense_status = expense.status
        if expense_status == "SUCCESSFUL":

            requisition = expense.requisition
            requisition.is_disbursed = True
            requisition.status = "APPROVED"
            requisition.is_inprogress = False
            requisition.save()

            budget_instance = expense.budget
            team_instance = expense.team
            budget_result = Budget.get_current_allocated_amount(
                budget_instance=budget_instance, team_instance=team_instance
            )

            purse_balance = budget_result.get("purse_balance")

            if purse_balance > 0:
                budget_instance.running_balance = float(purse_balance)
                budget_instance.save()

        elif expense_status == "FAILED":
            requisition = expense.requisition
            requisition.status = "FAILED"
            requisition.is_inprogress = False
            requisition.save()

        elif expense_status == "REVERSED":
            requisition = expense.requisition
            requisition.status = "FAILED"
            requisition.is_inprogress = False
            requisition.save()

    return "EXPENSE UPDATED"


@shared_task
def upload_file_aws_s3_bucket(model_instance_id, file, model_name):
    from clock_app.models import CompanyBranchInvite
    from leave_management.models import LeaveRequest
    from payroll_app.models import CompanyEmployeeList, CompanyPayrollSettings
    from stock_inventory.models import CompanyStore, StockRequest, StockTransfer

    if isinstance(file, File):
        file = File(file)
        file_extension = file.name.split(".")[-1].lower()
        # Define a list of allowed file extensions for different types
        image_extensions = ["jpg", "jpeg", "png", "gif", "svg"]
        excel_extensions = ["xls", "xlsx", "csv"]

        if file_extension in image_extensions or file_extension in excel_extensions:
            file_url = StorageManager().upload_file_to_storage(file=file)
            logger.info("Uploading file......")

        else:
            file_url = None

        if model_name == "Requisition" and file_url is not None:
            req = Requisition.objects.get(id=model_instance_id)
            req.invoice = file_url
            req.save()
            # return file_url

        if model_name == "Expense" and file_url is not None:
            req = Expense.objects.get(id=model_instance_id)
            req.invoice = file_url
            req.save()

        if model_name == "PayrollSettings" and file_url is not None:
            payroll_settings = CompanyPayrollSettings.objects.get(id=model_instance_id)
            payroll_settings.company_logo = file_url
            payroll_settings.save()

        if model_name == "EmployeeProfilePicture" and file_url is not None:
            payroll_settings = CompanyEmployeeList.objects.filter(
                id=model_instance_id
            ).last()
            payroll_settings.employee_profile_picture = file_url
            payroll_settings.save()

        if model_name == "StockRequest" and file_url is not None:
            StockRequest.objects.filter(request_id=model_instance_id).update(
                image_url=file_url
            )

        if model_name == "StockTransfer" and file_url is not None:
            StockTransfer.objects.filter(request_id=model_instance_id).update(
                image_url=file_url
            )

        if model_name == "CompanyBranchInvite" and file_url is not None:
            CompanyBranchInvite.objects.filter(invite_id=model_instance_id).update(
                image=file_url
            )

        if model_name == "CompanyWebStore" and file_url is not None:
            CompanyStore.objects.filter(id=model_instance_id).update(
                header_logo=file_url
            )

        if model_name == "CompanyWebStoreHeaderImage" and file_url is not None:
            CompanyStore.objects.filter(id=model_instance_id).update(
                header_image=file_url
            )

        if model_name == "LeaveRequest" and file_url is not None:
            LeaveRequest.objects.filter(id=model_instance_id).update(
                leave_document=file_url
            )

        if model_name == "AssetImage" and file_url is not None:
            AssetImage.objects.filter(id=model_instance_id).update(
                image=file_url
            )
        if model_name == "SupplierProductImage" and file_url is not None:
            SupplierProductImage.objects.filter(id=model_instance_id).update(
                image=file_url
            )

        return file_url
    else:
        return None


@shared_task
def upload_file_aws_s3_bucket_using_file_path(model_instance_id, file_path, model_name):
    from payroll_app.models import CompanyDetailsData

    file_url = StorageManager().upload_using_file_path(file_path=file_path)
    logger.info("Uploading file......")

    if model_name == "PensionSchedule" and file_url is not None:
        pension_url = CompanyDetailsData.objects.filter(id=model_instance_id).first()
        if pension_url.pension_xcel_file is None:
            pension_url.pension_xcel_file = {"1": f"{file_url}"}
            pension_url.save()
        else:
            pension_len = len(pension_url.pension_xcel_file) + 1
            pension_data = {f"{pension_len}": f"{file_url}"}

            pension_url.pension_xcel_file.update(pension_data)
            pension_url.save()
        return file_url

    if model_name == "CompanyReport" and file_url is not None:
        return file_url
    else:
        return None


@shared_task
def update_kyc_details(user_id):
    from account.models import Wallet

    """
    Updates the KYC (Know Your Customer) details for a specific user using LibertyPayPlus service.
    
    The function performs the following steps:
    1. Fetches the user by `user_id`. If the user does not exist, it returns None.
    2. Checks if KYC details already exist for the user:
        - If KYC details do not exist, it retrieves the KYC information from LibertyPay and updates the user's profile and KYC details.
        - If KYC details exist but the verification status is not 'SUCCESSFUL', it fetches the updated KYC information and updates both the user profile and KYC details.
    3. After updating the KYC information, it creates or updates the user's default wallet using the `vfd_create_update_paybox_user_default_wallet` method from the Wallet model.

    Args:
        user_id (int): The ID of the user whose KYC details need to be updated.

    Returns:
        Wallet: The user's default wallet, either created or updated.
    """

    liberty_pay = LibertyPayPlus()

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return None

    user_kyc = KycDetails.objects.filter(user_id=user_id)

    # If no KYC details exist and the user is valid, fetch KYC details and update user
    if not user_kyc.exists():
        kyc_details = liberty_pay.get_user_kyc_details(
            email=user.email, bvn_number=user.bvn_number
        )

        if kyc_details is not None:
            data = kyc_details.get("data")
            User.objects.filter(id=user_id).update(
                bvn_first_name=data.get("bvn_first_name"),
                bvn_last_name=data.get("bvn_last_name"),
                bvn_middle_name=data.get("bvn_middle_name"),
                date_of_birth=data.get("bvn_birthdate"),
                bvn_residentialAddress=data.get("bvn_residential_address"),
                bvn_lgaOfResidence=data.get("bvn_lga_of_origin"),
                bvn_stateOfResidence=data.get("bvn_state_of_origin"),
                kyc_level=data.get("kyc_level"),
                kyc_updated=True,
                is_consent=True,
            )
            KycDetails.objects.create(**data, user=user, result=kyc_details)

    # If KYC details exist, check verification status
    elif user_kyc.exists():
        kyc_instance = user_kyc.last()

        # If verification status is not 'SUCCESSFUL', update the details again
        if kyc_instance.verification_status != "SUCCESSFUL":
            kyc_details = liberty_pay.get_user_kyc_details(
                email=user.email, bvn_number=user.bvn_number
            )

            if kyc_details is not None:
                data = kyc_details.get("data")
                User.objects.filter(id=user_id).update(
                    bvn_first_name=data.get("bvn_first_name"),
                    bvn_last_name=data.get("bvn_last_name"),
                    bvn_middle_name=data.get("bvn_middle_name"),
                    date_of_birth=data.get("bvn_birthdate"),
                    bvn_residentialAddress=data.get("bvn_residential_address"),
                    bvn_lgaOfResidence=data.get("bvn_lga_of_origin"),
                    bvn_stateOfResidence=data.get("bvn_state_of_origin"),
                    kyc_level=data.get("kyc_level"),
                    kyc_updated=True,
                    is_consent=True,
                )
                user_kyc.update(**data, result=kyc_details)

    # Create or update the default wallet for the user
    return vfd_create_update_paybox_user_default_wallet(user_id=user_id)


@shared_task
def fetch_and_create_liberty_pay_users_on_requisition():
    liberty_pay = LibertyPayPlus()
    liberty_pay_users = liberty_pay.fetch_liberty_pay_users()

    for liberty_pay_user in liberty_pay_users:

        liberty_pay_id = liberty_pay_user.get("id")

        try:

            User.objects.get(liberty_pay_id=liberty_pay_id)

        except User.DoesNotExist:
            User.objects.create(
                liberty_pay_id=liberty_pay_id,
                phone_no=liberty_pay_user.get("phone_number"),
                email=liberty_pay_user.get("email"),
                first_name=liberty_pay_user.get("first_name"),
                last_name=liberty_pay_user.get("last_name"),
                bvn_number=liberty_pay_user.get("bvn"),
                bvn_first_name=liberty_pay_user.get("first_name"),
                bvn_last_name=liberty_pay_user.get("last_name"),
                account_no=liberty_pay_user.get("account_number"),
                street=liberty_pay_user.get("street"),
                state=liberty_pay_user.get("state"),
                lga=liberty_pay_user.get("lga"),
                nearest_landmark=liberty_pay_user.get("nearest_landmark"),
                gender=liberty_pay_user.get("gender"),
                is_active=True,
                liberty_pay_customer_status=True,
                liberty_pay_user_type=liberty_pay_user.get("type_of_user"),
                bank_code=liberty_pay_user.get("bank_code"),
                kyc_level=liberty_pay_user.get("kyc_level"),
                referral_code=liberty_pay_user.get("referral_code"),
            )

    return "ONBOARDED LIBERTY PAY USERS"


@shared_task
def process_sales_online_payments(event_id: str):
    from sales_app.models import SalesTransaction

    get_event = PaystackPayment.objects.filter(id=event_id).first()
    if get_event is None:
        return "EVENT DOES NOT EXISTS."

    # Verify event transaction before updating sales transaction.
    verify_event = PaystackPayment.verify_event_transaction(
        reference=get_event.reference
    )
    if verify_event.get("status"):

        if (
            verify_event.get("message") == "Verification successful"
            and verify_event.get("data").get("gateway_response") == "Approved"
        ):
            TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            get_event.confirmed = True
            get_event.confirmed_at = TODAY
            get_event.confirmation_payload = json.dumps(verify_event.get("response"))
            get_event.save()

            # Update sales transaction.
            get_sales_transaction = SalesTransaction.objects.filter(
                batch_id=get_event.reference
            )
            if not get_sales_transaction.exists():
                return "SALES TRANSACTION NOT FOUND FOR THE EVENT."

# @shared_task
# @transaction.atomic
# def tie_paid_modules_to_subscription(event_id):
#     from core.models import PaystackPayment
#     from subscription_and_invoicing.models import Invoice, CompanySubscription, ModuleSubscription, \
#         AccessPath, SubscriptionPlan
#     from django.core.exceptions import PermissionDenied
#     from decimal import Decimal
#     import json
#     """Tie paid modules to the user and company subscription."""
#
#     # Get the payment event
#     get_event = PaystackPayment.objects.filter(id=event_id, status="success").first()
#     if not get_event:
#         return {"error": "No successful payment events found"}
#
#     reference = get_event.reference
#
#     # Verify the Paystack event transaction
#     verify_event = PaystackPayment.verify_event_transaction(reference=reference)
#     if not verify_event.get("status") or verify_event.get("message") != "Verification successful" or \
#             verify_event.get("data").get("gateway_response") != "Approved":
#         return {"error": "Payment verification failed or not approved"}
#
#     # Fetch the invoice using the reference
#     latest_invoice = Invoice.objects.filter(batch_id=reference).first()
#     if not latest_invoice:
#         return {"error": "No matching invoice found for the payment reference"}
#
#     # Fetch user and company from the invoice
#     user = latest_invoice.user
#     company = latest_invoice.company
#
#     if not user:
#         raise PermissionDenied("No user provided")
#     if not company:
#         raise PermissionDenied("No company associated with user")
#
#     # Check payment amount
#     paystack_amount = Decimal(verify_event.get("data").get("amount", 0)) / 100  # Convert from kobo to currency unit
#     if paystack_amount < latest_invoice.amount:
#         return {"error": "Paid amount is less than expected"}
#     elif paystack_amount > latest_invoice.amount:
#         latest_invoice.payment_status = "paid_excess"
#     else:
#         latest_invoice.payment_status = "paid"
#
#     latest_invoice.settled_amount = paystack_amount
#     latest_invoice.save()
#
#     # Fetch paid modules
#     paid_modules = latest_invoice.subscribed_modules.all()
#     if not paid_modules.exists():
#         return {"error": "No modules were paid for in this invoice"}
#
#     # Determine subscription duration
#     duration = 6 if "biannual" in (latest_invoice.package_description or "").lower() else 12
#
#     # Create or fetch Subscription Plan
#     subscription_plan, _ = SubscriptionPlan.objects.get_or_create(
#         name=f"{company.company_name} Plan",
#         defaults={
#             "description": "Custom company subscription plan",
#             "price": latest_invoice.amount,
#             "duration_months": duration,
#             "features": {},
#             "is_active": True,
#         }
#     )
#
#     # Create or update CompanySubscription
#     company_subscription, _ = CompanySubscription.objects.update_or_create(
#         company=company,
#         defaults={
#             "plan": subscription_plan,
#             "access_type": AccessPath.PAID,
#             "status": "active",
#             "created_by": user,
#         }
#     )
#
#     # Create Module Subscriptions
#     for module in paid_modules:
#         ModuleSubscription.objects.update_or_create(
#             company_subscription=company_subscription,
#             module=module,
#             defaults={
#                 "is_auto_renewal": False,
#                 "start_date": latest_invoice.start_date,
#                 "end_date": latest_invoice.expiry_date,
#                 "is_active": True,
#             }
#         )
#
#     return {"message": "Subscription successfully tied to company"}


@shared_task
@transaction.atomic
def tie_paid_modules_to_subscription(event_id):
    from core.models import PaystackPayment
    from subscription_and_invoicing.models import (
        Invoice, CompanySubscription, ModuleSubscription,
        AccessPath, SubscriptionPlan, InvoiceTransaction
    )
    from django.core.exceptions import PermissionDenied
    from decimal import Decimal
    import json

    """Tie paid modules to the user and company subscription after payment."""

    # Fetch the successful payment event
    get_event = PaystackPayment.objects.filter(id=event_id, status="success").first()
    if not get_event:
        return {"error": "No successful payment event found"}

    reference = get_event.reference

    # Verify the Paystack transaction
    verify_event = PaystackPayment.verify_event_transaction(reference=reference)
    if not verify_event.get("status") or verify_event.get("message") != "Verification successful" or \
            verify_event.get("data").get("gateway_response") != "Approved":
        return {"error": "Payment verification failed or not approved"}

    # Fetch the corresponding invoice
    latest_invoice = Invoice.objects.filter(batch_id=reference).first()
    if not latest_invoice:
        return {"error": "No matching invoice found for the payment reference"}

    # Fetch user and company from the invoice
    user = latest_invoice.created_by
    company = latest_invoice.company

    if not user:
        raise PermissionDenied("No user associated with the invoice")
    if not company:
        raise PermissionDenied("No company associated with the invoice")

    # Validate payment amount
    paystack_amount = Decimal(verify_event.get("data").get("amount", 0)) / 100  # Convert from kobo to currency unit
    invoice_amount = latest_invoice.total_amount

    if paystack_amount < invoice_amount:
        return {"error": "Paid amount is less than expected"}
    elif paystack_amount > invoice_amount:
        latest_invoice.payment_status = "paid_excess"
        excess_payment = paystack_amount - invoice_amount
    else:
        latest_invoice.payment_status = "paid"
        excess_payment = 0

    # Update invoice settled amount
    latest_invoice.settled_amount = paystack_amount
    latest_invoice.save()

    # Record the transaction in InvoiceTransaction table
    InvoiceTransaction.objects.create(
        invoice=latest_invoice,
        amount=paystack_amount,
        payment_method="Paystack",  # Adjust based on actual method
        invoice_reference=reference,
        excess_payment=excess_payment
    )

    # Fetch and activate the CompanySubscription using batch_id or invoice_ref
    company_subscription = CompanySubscription.objects.filter(
        company=company,
        status="inactive",
        invoice_ref=latest_invoice.invoice_ref
    ).first()
    if not company_subscription:
        return {"error": "No matching inactive company subscription found for activation"}

    company_subscription.status = "active"
    company_subscription.save()

    # Fetch and activate ModuleSubscriptions using batch_id or invoice_ref
    inactive_modules = ModuleSubscription.objects.filter(
        company_subscription=company_subscription,
        is_active=False,
        invoice_ref=latest_invoice.invoice_ref
    )
    if not inactive_modules.exists():
        return {"error": "No matching inactive module subscriptions found for activation"}

    inactive_modules.update(is_active=True)

    return {"message": "Subscription successfully tied to company"}


@shared_task
def send_batch_campaign_message(
    user_id,
    company_id,
    sender_id,
    message,
    campaign_name,
    contacts,
    priority_route,
    flash_route,
    send_later,
    send_date,
    amount,
):
    from core.models import CampaignMessage, CampaignSenderId
    from requisition.models import Company

    user = User.objects.get(id=user_id)
    company = Company.objects.get(id=company_id)
    campaign_sender = CampaignSenderId.objects.get(id=sender_id)

    send_message = CampaignMessage.send_campaign_message(
        user=user,
        company=company,
        sender_id=campaign_sender,
        message=message,
        campaign_name=campaign_name,
        contacts=contacts,
        priority_route=priority_route,
        flash_route=flash_route,
        send_later=send_later,
        send_date=send_date,
        amount=amount,
    )
    return "CAMPAIGN MESSAGES SENT WITH WHISPER SMS"


@shared_task
def send_email_with_multiple_attachments(
    recipient: str,
    sender_email: str,
    cc_email: str,
    subject: str,
    template_dir: str,
    file_data: list,  # A list of tuples [(file_name, file_content), ...]
    use_template=True,
    body=None,
    **substitute,
):
    if use_template:
        TEMPLATE_DIR = os.path.join("templates", f"{template_dir}")
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitute)
    else:
        template = None

    try:
        # Prepare attachments
        attachments = [
            ("attachment", (file_name, file_content))
            for file_name, file_content in file_data
        ]

        # Send email using Mailgun
        response = requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_APIKEY}"),
            data={
                "from": "Liberty Pay <<EMAIL>>",
                "to": f"<{recipient}>",
                "cc": f"{sender_email}",
                "bcc": f"{cc_email}",
                "subject": f"{subject}",
                "html": f"""{template}""" if use_template else None,
                "text": body if not use_template else None,
            },
            files=attachments,  # Pass the list of attachments here
        )

        # Check the response status
        if response.status_code == 200:
            return "EMAIL SENT"
        else:
            print("Failed to send email:", response.text)
            return "EMAIL FAILED"

    except Exception as e:
        print("Failed to send email:", e)
        return "EMAIL FAILED"


@shared_task
def add_contact_to_brevo(full_name:str, phone_number, email, product_vertical:list):
    first_name = full_name.split(" ")[0]
    last_name = full_name.split(" ")[1]

    brevo_marketing_api_instance = BrevoMarketingApis()
    brevo_marketing_api_instance.create_contact(
        first_name=first_name,
        last_name=last_name,
        phone_number=phone_number,
        email=email,
        list_id=product_vertical,
    )


@shared_task
def send_reminder_emails(subject, recipient, meeting_date, availability:datetime, template_dir, meeting_link, sales_lead_name, sales_lead_email, product_vertical, invitee_name):
    import datetime as dt
    from datetime import datetime as dt_time
    current_timezone = dt.datetime.now().astimezone().tzinfo
    current_time = timezone.now()
    updated_scheduled_time = dt_time.strptime(meeting_date, "%Y-%m-%d %H:%M:%S").replace(tzinfo=current_timezone)
    thirty_minutes = dt.timedelta(minutes=30)
    ten_minutes = dt.timedelta(minutes=10)
    thirty_minutes_timer = updated_scheduled_time - thirty_minutes
    ten_minutes_timer = updated_scheduled_time - ten_minutes
    email_date = availability.strftime('%a %d/%m/%Y %I:%M%p') + ' WAT'

    thirty_minutes_countdown = (thirty_minutes_timer - current_time).total_seconds()
    ten_minutes_countdown = (ten_minutes_timer - current_time).total_seconds()

    try:
        send_email.apply_async(
            args=[
                recipient,
                subject,
                template_dir,
            ],
            kwargs={
                "invitee_name": invitee_name,                
                "sales_lead_name": sales_lead_name,
                "sales_lead_email": sales_lead_email,
                "product_vertical": product_vertical,
                "email_date": email_date,
                "meeting_link": meeting_link,
            },
            countdown=thirty_minutes_countdown
        )
    except Exception as e:
        print("AN ERROR OCCURRED WHILE ATTEMPTING TO SEND 30 MINUTES COUNTDOWN REMINDER EMAIL", {str(e)})
    
    try:
        send_email.apply_async(
            args=[
                recipient,
                subject,
                template_dir,
            ],
            kwargs={
                "invitee_name": invitee_name,                
                "sales_lead_name": sales_lead_name,
                "sales_lead_email": sales_lead_email,
                "product_vertical": product_vertical,
                "email_date": email_date,
                "meeting_link": meeting_link,                
            },
            countdown=ten_minutes_countdown
        )
    except Exception as e:
        print("AN ERROR OCCURRED WHILE ATTEMPTING TO SEND 10 MINUTES COUNTDOWN 10 MINUTES COUNTDOWN REMINDER EMAIL", {str(e)})
        
@shared_task
def send_new_user_onboarding_email(user_id):
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return None

    file_path = os.path.join(settings.BASE_DIR, 'core/') 
    attachment_file = f"{file_path}info_paybox.pdf"

    with open(attachment_file, "rb") as this_file:
        new_user_file = this_file.read()
        this_file.close()
            
    send_email_with_attachment(
        file = new_user_file,
        template_dir="new_onboarded_users.html",
        file_name = attachment_file,
        subject="Welcome to Paybox360 – Let’s Simplify Your Business Operations!",
        recipient=user.email,
        company_name=user.first_name if user.first_name else "User",
        company_email="",
        pension_date="",
        pension_name=f"",
        sender_email="",
        cc_email=""
    )


@shared_task
def send_new_users_follow_up_email():
    two_days_ago = timezone.now() - timedelta(days=2)
    three_days_ago = timezone.now() - timedelta(days=3)
    users_created_two_days_ago = User.objects.filter(created_at__date=two_days_ago.date())
    users_created_three_days_ago = User.objects.filter(created_at__date=three_days_ago.date())

    for user in users_created_two_days_ago:
        send_email.delay(
            recipient=user.email,
            company_name=user.first_name if user.first_name else "User",
            subject="Take the Next Step – Maximize Paybox360 for Your Business",
            template_dir="follow_up_two_new_users.html",
        )

    for user in users_created_three_days_ago:
        send_email.delay(
            recipient=user.email,
            company_name=user.first_name if user.first_name else "User",
            subject="Exploring Paybox360: A Quick Overview",
            template_dir="follow_up_three_new_users.html",
        )


@shared_task
def send_sales_user_login_email_notification(
    recipient: str,
    subject: str,
    template_dir: str,
    use_template=True,
    body=None,
    **substitute,
):
    if use_template:
        TEMPLATE_DIR = os.path.join("templates", f"{template_dir}")
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitute)
    else:
        template = None
    try:
        requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_APIKEY}"),
            data={
                "from": "Liberty Pay <<EMAIL>>",
                "to": f"<{recipient}>",
                "subject": f"{subject}",
                "html": f"""{template}""" if use_template else None,
                "text": body if not use_template else None,
            },
        )

        return "EMAIL SENT"
    except Exception as e:
        print("failed to send email", e)
        return "EMAIL FAILED"


@shared_task
def send_daily_sales_user_attendance():
    from requisition.models import Company
    from core.models import SalesTeamShift, SalesTeamBreakShift

    file_path = os.path.join(settings.BASE_DIR, 'core/media/')

    today_date = timezone.localtime().date()
    all_company = Company.objects.filter(is_active=True)
    for company_ins in all_company:
       
        all_sales_user = User.objects.filter(default_company=company_ins, username__isnull=False)
        all_sales_transactions = SalesTransaction.objects.filter(company=company_ins,  status__in=["SUCCESSFUL", "CREDIT"], created_by__isnull=False)

        all_sales_user_transactions = []
        all_attachments = []
        all_sales_user_data = []
        for sales_user in all_sales_user:
            sale_user_shifts = SalesTeamShift.objects.filter(user=sales_user, created_at__date=today_date)
            
            for sale_user_shift in sale_user_shifts:
                all_sales_user_data.append(
                    {
                        "username": sales_user.username,
                        "start_time": sale_user_shift.start_time.strftime("%Y-%m-%d %H:%M:%S") if sale_user_shift.start_time else "",
                        "end_time": sale_user_shift.end_time.strftime("%Y-%m-%d %H:%M:%S") if sale_user_shift.end_time else ""
                    }
                )
            sale_user_breaks = SalesTeamBreakShift.objects.filter(user=sales_user, created_at__date=today_date)
            for sale_user_break in sale_user_breaks:
                all_sales_user_data.append(
                    {
                        "username": sales_user.username,
                        "start_break_time": sale_user_break.start_time.strftime("%Y-%m-%d %H:%M:%S") if sale_user_break.start_time else "",
                        "end_break_time": sale_user_break.end_time.strftime("%Y-%m-%d %H:%M:%S") if sale_user_break.end_time else ""
                    }
                )
            
            this_all_sales_transactions = all_sales_transactions.filter(created_by=sales_user)
            for sales_transaction in this_all_sales_transactions:
                all_sales_user_transactions.append(
                    {
                        "user_name": sales_user.username,
                        "branch": sales_transaction.branch.name,
                        "invoice": sales_transaction.invoice,
                        "status": sales_transaction.status,
                        "total_sales_amount": sales_transaction.total_sales_amount,
                        "total_cost": sales_transaction.total_cost,
                        "amount_paid": sales_transaction.amount_paid,
                    }
                )
                    
        # Check if the attendance file already exists and delete it
        last_file = os.path.join(f"{file_path}{company_ins.company_name}_sales_users_sheet.xlsx") 
        my_file = Path(last_file) 
        if my_file.is_file(): 
            os.remove(last_file)
        try:
            os.mkdir(file_path)
        except Exception:
            pass 

        # Check if the sales transaction file already exists and delete it
        sales_last_file = os.path.join(f"{file_path}{company_ins.company_name}_sales_users_transaction_sheet.xlsx") 
        sales_my_file = Path(sales_last_file) 
        if sales_my_file.is_file(): 
            os.remove(sales_last_file)
        try:
            os.mkdir(file_path)
        except Exception:
            pass 


        if all_sales_user_data:
            # Flatten the nested data structure

            # Create a DataFrame from the flattened data
            this_attendance = pd.DataFrame(all_sales_user_data)

            # Create an Excel writer object
            excel_file = f"{file_path}{company_ins.company_name}_sales_users_sheet.xlsx"
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                this_attendance.to_excel(writer, index=False, sheet_name='Attendance_Sheet', header=True)

            this_attendance.to_excel(f"{file_path}{company_ins.company_name}_sales_users_sheet.xlsx", index=False, header=True)
            with open(last_file, "rb") as excel_file:
                # attendance = excel_file.read()
                excel_file.close()

            attendance_file_name = f"{file_path}{company_ins.company_name}_sales_users_sheet.xlsx"
            all_attachments.append((attendance_file_name, open(attendance_file_name, "rb")))
        
        if all_sales_user_transactions:
            # Flatten the nested data structure

            # Create a DataFrame from the flattened data
            this_attendance = pd.DataFrame(all_sales_user_transactions)

            # Create an Excel writer object
            excel_file = f"{file_path}{company_ins.company_name}_sales_users_transaction_sheet.xlsx"
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                this_attendance.to_excel(writer, index=False, sheet_name='Sales_Transaction_Sheet', header=True)

            this_attendance.to_excel(f"{file_path}{company_ins.company_name}_sales_users_transaction_sheet.xlsx", index=False, header=True)
            with open(sales_last_file, "rb") as excel_file:
                # transaction = excel_file.read()
                excel_file.close()

            transaction_file_name = f"{file_path}{company_ins.company_name}_sales_users_transaction_sheet.xlsx"
            all_attachments.append((transaction_file_name, open(transaction_file_name, "rb")))

        if all_attachments:
            send_email_with_multiple_attachments(
                file_data = all_attachments,
                template_dir="sales_user_attendance.html",
                subject=f"{company_ins.company_name} Sales Attendance and Transaction",
                recipient=company_ins.user.email,
                company_name=company_ins.company_name,
                today_date=today_date,
                sender_email="",
                cc_email=""
            )

            try:
                os.remove(f"{last_file}") ## Delete Sales User Attendance
            except (PermissionError, FileNotFoundError):
                pass
            try:
                os.remove(f"{sales_last_file}") ## Delete Sales User Transaction
            except (PermissionError, FileNotFoundError):
                pass
    
    return "ATTENDANCE AND TRANSACTION DATA HAS BEEN SENT TO COMPANY OWNER"


@shared_task
def money_notification_event(notification_id: str):
    from core.models import Notification
    from helpers.emqx_mqtt import EMQXHandler

    notification = Notification.objects.filter(id=notification_id).last()
    if notification is not None:
        handler = EMQXHandler()
        handler.connect()
        message = {
            "subject": notification.title,
            "body": notification.message,
        }
        handler.publish(
            topic=f"branch/alert/{notification.branch.id}",
            message=json.dumps(message),
        )
        return "NOTIFICATION EVENT SENT SUCCESSFULLY."
    return "NOTIFICATION EVENT NOT FOUND."
