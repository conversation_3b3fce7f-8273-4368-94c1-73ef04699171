from datetime import timedelta
from decouple import config
import os
import re
from sys import argv
import requests
from django.http import HttpResponse
from django.utils.timezone import now
from rest_framework.decorators import (
    api_view,
    authentication_classes,
    permission_classes,
)

from datetime import timedelta, datetime
import os, json, pytz, random

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password
from django.contrib.admin.views.decorators import staff_member_required
from django.db.models import Q, Sum
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
import pytz
from rest_framework import generics, filters, status
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.models import AccountSystem, Wallet
from core import models, serializers
from core.auth.custom_auth import CustomUserAuthentication
from core.helpers.assign_brevo_vertical import chosen_vertical
from core.helpers.enums import SecretQuestionChoices, SupportedAppChoices
from core.helpers.func import (
    calculate_sms_charges,
    encrypt_string,
    save_to_brevo,
)
from core.pagenator import CustomPagination
from core.services import SalesUser, WhisperSms
from core.tasks import (
    send_batch_campaign_message,
    add_contact_to_brevo,
    send_email,
    send_reminder_emails,
    send_sales_user_login_email_notification,
)
from helpers.custom_response import Response as CustomResponse
from helpers.reusable_functions import Paginator, is_valid_uuid
from payroll_app.apis.func import round_amount
from payroll_app.services import valid_uuid_check
from requisition.helpers.enums import UserRole, UserStatus
from requisition.models import Company, TeamMember
from stock_inventory.models import Branch
from storage_backend import StorageManager
from core.models import ConstantTable, VfdMeeting, SalesAgents
from performance_sales_metrics_dashboard.utils.parse_dates import date_parser
from core.calendly_integration import (
    assign_meeting_to_sales_agent,
)  # , book_vfd_zoom_meeting
from core.helpers.assigned_sales_person import AssignedSalesPerson, SlackWaitList
from performance_sales_metrics_dashboard.models import BookADemo, Lead
from performance_sales_metrics_dashboard.utils import crm_calendly_demo
from subscription_and_invoicing.models import (
    CompanySubscription,
    AccessPath,
    ModuleSubscription,
    TokenStatus,
)

from .serializers import (
    ProfilePictureSerializer,
    ResetPasswordSerializer,
    UpdateNotificationSerializer,
    UpdatePreferenceSerializer,
    VerifyBVNSerializer,
    GuarantorFormSerializer,
    VerifyBVNSerializer,
    ImageSerializer,
    ConfirmationSerializer,
    GuarantorFormSerializer,
)
from .models import Guarantor, User
from collections import defaultdict


User = get_user_model()


# Create your view(s) here.
class UpLoadMediaDocs(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request, format=None):
        image_file = request.FILES.get("file")

        if image_file:
            file_url = StorageManager().upload_file_to_storage(file=image_file)
            return Response({"file_url": file_url}, status=201)
        else:
            return Response({"error": "No image file provided."}, status=400)


class SearchUsers(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = serializers.SearchUsersSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    search_fields = ("email", "phone_no", "first_name", "last_name")

    def get_queryset(self):
        users = User.objects.exclude(Q(is_staff=True) | Q(is_superuser=True))
        return users


class MirrorLibertyPaySignIn(APIView):
    def post(self, request):
        serializer = serializers.MirrorLibertyPaySignInSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        email = data.get("email")
        password = data.get("password")

        try:

            user = User.objects.get(email=email)
            encrypt_passkey = encrypt_string(pass_code=password)
            user.liberty_pay_user_hash_passkey = encrypt_passkey
            user.save()

        except User.DoesNotExist:
            pass

        return Response({"message": "Success"}, status=200)


class ListAdminCategories(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = serializers.CategoriesSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    search_fields = ("title",)

    def get_queryset(self):
        # return CategoryList.objects.filter().order_by("title")
        return models.CategoryList.objects.filter()


class WaitListApiView(APIView):

    def post(self, request):
        serializer = serializers.WaitListSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # save_to_brevo(data=serializer.validated_data)

        # serialized_data = serializer.data
        serializer.save()

        try:
            data = serializer.validated_data
            email = data.get("email")
            first_name = data.get("first_name")
            last_name = data.get("last_name")
            phone_number = data.get("phone_number")
            industry = data.get("industry")
            company_name = data.get("company_name")
            company_size = data.get("company_size")
            product_interest = data.get("product_interest")
            lead_source = data.get("lead_source")
            sales_agent = models.WaitList.assign_demo_to_sales_agent(
                email=email,
                first_name=first_name,
                last_name=last_name,
                phone_number=phone_number,
            )
        except Exception as e:
            return (
                Response(
                    {
                        "message": f"An error occurred while attempting to assign demo to sales agent.",
                        "error": str(e),
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                ),
            )

        agent_name = sales_agent.get("paybox_agent_name")
        agent_email = sales_agent.get("paybox_agent_email").lower()

        paybox_demo_details = {
            "first_name": first_name,
            "last_name": last_name,
            "email": email,
            "phone_number": phone_number,
            "industry": industry,
            "company_name": company_name,
            "company_size": company_size,
            "product_interest": product_interest,
            "lead_source": lead_source,
            "agent_name": agent_name,
            "agent_email": agent_email,
        }

        try:
            data = serializer.validated_data
            sales_agent_data = SlackWaitList()
            sales_agent_data.send_waitlist_to_slack(**paybox_demo_details)
        except Exception as e:
            return Response(
                {
                    "message": f"An error occurred while attempting to assign demo to sales agent.",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        return Response(data={"status": "Success", "data": serializer.data}, status=200)


class NotificationAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.NotificationSerializer

    def get(self, request, *args, **kwargs):
        query_parameters = request.GET
        company_id = query_parameters.get("company")
        branch_id = query_parameters.get("branch")
        notification_id = query_parameters.get("notification")

        if company_id is None:
            return CustomResponse(
                errors={"messages": "provide a valid company ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(company_id):
            return CustomResponse(
                errors={"details": "invalid ID type for company."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        company_notifications = models.Notification.objects.filter(company=company_id)

        if company_notifications.exists:

            if notification_id:
                if not is_valid_uuid(notification_id):
                    return CustomResponse(
                        errors={"details": "invalid ID type for notification."},
                        status_code=400,
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                company_notification = company_notifications.filter(
                    id=notification_id
                ).first()
                if company_notification is None:
                    data = {
                        "details": "invalid notification ID supplied.",
                        "notifications": {},
                    }
                else:
                    serializer = self.serializer_class(instance=company_notification)
                    data = {
                        "details": "successfully fetched notification(s).",
                        "notifications": serializer.data,
                    }
            else:
                paginated_response = Paginator.paginate(
                    request=request, queryset=company_notifications
                )
                serializer = self.serializer_class(
                    instance=paginated_response, many=True
                )
                data = {
                    "details": "successfully fetched notification(s).",
                    "notifications": serializer.data,
                    "count": len(serializer.data),
                    "total_notifications": company_notifications.count(),
                    "total_unread": company_notifications.filter(is_read=False).count(),
                }
        else:
            data = {
                "details": "no notification(s) yet.",
                "notifications": [],
                "count": 0,
                "total_notifications": 0,
                "total_unread": 0,
            }
        return CustomResponse(data=data, status_code=200, status=status.HTTP_200_OK)

    def patch(self, request, *args, **kwargs):
        serializer = serializers.NotificationUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        notification = models.Notification.mark_as_read(**serializer.validated_data)
        if notification is None:
            return CustomResponse(
                errors={"details": "notification not found."},
                status_code=404,
                status=status.HTTP_200_OK,
            )
        return CustomResponse(
            data={"details": "success"}, status_code=200, status=status.HTTP_200_OK
        )


class PaystackAPIView(APIView):

    def post(self, request, *args, **kwargs):
        models.PaystackPayment.create_event_transaction(data=request.data)
        return CustomResponse(
            data={"details": "success"},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class CreateSenderIdAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.CreateSenderIdSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company = serializer.validated_data.get("company")
        sender_id = serializer.validated_data.get("sender_id")
        sample_message = serializer.validated_data.get("sample_message")

        sender_response = WhisperSms.create_sender_id(
            company_name=company.company_name,
            sender_id=sender_id,
            sample_message=sample_message,
        )
        if not sender_response.get("error"):
            models.CampaignSenderId.create_sender_id(
                company, sender_id, sample_message, request.user
            )
            return Response(sender_response, status=status.HTTP_200_OK)
        else:
            return Response(sender_response, status=status.HTTP_400_BAD_REQUEST)


class SendCampaignMessageAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):

        request_user = request.user

        serializer = serializers.SendCampaignMessageSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        company = serializer.validated_data.get("company")
        sender_id = serializer.validated_data.get("sender_id")
        contacts = serializer.validated_data.get("contacts")
        priority_route = serializer.validated_data.get("priority_route")
        page_number = serializer.validated_data.get("page_number")
        message = serializer.validated_data.get("message")
        flash_route = serializer.validated_data.get("flash_route")
        send_later = serializer.validated_data.get("send_later")
        send_date = serializer.validated_data.get("send_date")
        campaign_name = serializer.validated_data.get("campaign_name")

        try:
            account_ins = AccountSystem.objects.get(company=company, account_type="SMS")
        except AccountSystem.DoesNotExist:
            account_ins = None

        # check if company has wallet
        try:
            wallet_ins = Wallet.objects.get(
                account=account_ins, wallet_type="SMS", is_active=True
            )
        except Wallet.DoesNotExist:
            wallet_ins = None

        # get wallet balance
        if wallet_ins:
            wallet_balance = wallet_ins.balance
        else:
            wallet_balance = 0

        # get pay amount
        receiver_count = len(contacts)
        get_total_charges = calculate_sms_charges(
            priority_route, page_number, receiver_count
        )

        # get total payout amount with charges
        get_total_charged_amount = round_amount(get_total_charges)
        amount_to_charge = get_total_charged_amount

        # check wallet balance
        if wallet_balance < amount_to_charge:
            response = {
                "error": "161",
                "message": "You do not have sufficient balance to make this transaction",
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        # charge company account

        charge_account = Wallet.charge_company_for_sms_campaign(
            request_user, amount_to_charge, wallet_ins
        )

        if charge_account.get("success"):
            send_message = send_batch_campaign_message.delay(
                user_id=request_user.id,
                company_id=company.id,
                sender_id=sender_id.id,
                message=message,
                campaign_name=campaign_name,
                contacts=contacts,
                priority_route=priority_route,
                flash_route=flash_route,
                send_later=send_later,
                send_date=send_date,
                amount=amount_to_charge,
            )
            CONST = ConstantTable.get_constant_instance()

            if priority_route:
                sms_unit = amount_to_charge * CONST.priority_campaign_sms_charge

            else:
                sms_unit = amount_to_charge * CONST.normal_campaign_sms_charge

            return Response(
                {
                    "message": f"you have successfully sent campaign to {receiver_count} recipient and {sms_unit} sms unit charged"
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(charge_account, status=status.HTTP_400_BAD_REQUEST)


class ConfirmCampaignMessageAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):

        request_user = request.user

        serializer = serializers.SendCampaignMessageSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        company = serializer.validated_data.get("company")
        sender_id = serializer.validated_data.get("sender_id")
        contacts = serializer.validated_data.get("contacts")
        priority_route = serializer.validated_data.get("priority_route")
        page_number = serializer.validated_data.get("page_number")
        message = serializer.validated_data.get("message")
        flash_route = serializer.validated_data.get("flash_route")
        send_later = serializer.validated_data.get("send_later")
        send_date = serializer.validated_data.get("send_date")
        campaign_name = serializer.validated_data.get("campaign_name")

        # get pay amount
        receiver_count = len(contacts)
        get_total_charges = calculate_sms_charges(
            priority_route, page_number, receiver_count
        )

        # get total payout amount with charges
        get_total_charged_amount = round_amount(get_total_charges)
        amount_to_charge = get_total_charged_amount

        response = {
            "campaign_name": campaign_name,
            "number_of_recipient": receiver_count,
            "total_charges": amount_to_charge,
            "number_of_pages": page_number,
        }
        return Response(response, status=status.HTTP_200_OK)


class CampaignDashboardAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):

        request_user = request.user
        company_id = request.query_params.get("company_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})

        sms_campaign_qs = models.CampaignBatch.objects.filter(company__id=company_id)
        sms_campaign = sms_campaign_qs.count() or 0
        email_campaign = 0
        total_campaign = sms_campaign + email_campaign
        total_receivers = (
            models.CampaignMessage.objects.filter(company__id=company_id).count() or 0
        )
        response = {
            "sms_campaign": sms_campaign,
            "email_campaign": email_campaign,
            "total_campaign": total_campaign,
            "total_received": total_receivers,
        }
        return Response(response, status=status.HTTP_200_OK)


class CampaignAccountDashboardAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company_id = request.query_params.get("company_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})
        try:
            company_ins = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            company_ins = None

        try:
            account_ins = AccountSystem.objects.get(
                company=company_ins, account_type="SMS"
            )
        except AccountSystem.DoesNotExist:
            account_ins = None

        # check if company has wallet
        try:
            wallet_ins = Wallet.objects.get(
                account=account_ins, wallet_type="SMS", is_active=True
            )
        except Wallet.DoesNotExist:
            wallet_ins = None

        # get wallet balance
        if wallet_ins:
            wallet_balance = wallet_ins.balance
        else:
            wallet_balance = 0

        CONST = ConstantTable.get_constant_instance()

        normal = CONST.normal_campaign_sms_charge
        try:
            sms_unit = int(wallet_balance / normal)
        except ZeroDivisionError:
            sms_unit = 0
        data = {
            "wallet_balance": wallet_balance,
            "sms_unit": sms_unit,
            "account_details": {
                "account_name": account_ins.account_name if account_ins else "",
                "account_number": account_ins.account_number if account_ins else "",
                "bank_name": account_ins.bank_name if account_ins else "",
            },
        }
        return Response(data, status=status.HTTP_200_OK)


class GetAllFilterSenderId(generics.ListAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = serializers.ListCampaignSenderId

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("id",)
    search_fields = ("sender_id", "sample_message")

    def get_queryset(self):
        request_user = self.request.user
        company_id = self.request.query_params.get("company_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})

        # Get all sender ID for a company
        all_sender_id = models.CampaignSenderId.objects.filter(company__id=company_id)
        return all_sender_id

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        request_user = request.user
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetCampaignMessageHistory(generics.ListAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = serializers.ListCampaignBatch

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "id",
        "priority_route",
        "flash_route",
        "send_later",
        "send_later",
        "send_date",
    )
    search_fields = ("message", "campaign_name", "sender__sender_id")

    def get_queryset(self):
        request_user = self.request.user
        company_id = self.request.query_params.get("company_id")

        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})

        # Get all sender ID for a company
        all_sender_id = models.CampaignBatch.objects.filter(company__id=company_id)
        return all_sender_id

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class SetDefaultCompanyAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def patch(self, request, *args, **kwargs):
        serializer = serializers.SetDefaultCompanySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user
        user.default_company = serializer.validated_data.get("company")
        user.save()
        return CustomResponse(
            data={"message": "successfully set default company."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class SetDefaultBranchAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def patch(self, request, *args, **kwargs):
        serializer = serializers.SetDefaultBranchSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user
        user.default_branch = serializer.validated_data.get("branch")
        user.save()
        return CustomResponse(
            data={"message": "successfully set default branch."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class SalesLoginAPIView(APIView):
    def post(self, request):
        import base64

        serializer = serializers.SalesLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        username = serializer.validated_data.get("username")
        password = serializer.validated_data.get("password")
        try:
            user = User.objects.get(username=username, sales_is_deleted=False)
            if not user.default_company:
                return Response(
                    {
                        "status_code": "105",
                        "message": "User does not belong to a company",
                    },
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            if not user.default_branch:
                return Response(
                    {
                        "status_code": "106",
                        "message": "User does not belong to a branch",
                    },
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            if user.sales_is_suspended:
                return Response(
                    {"status_code": "101", "message": "User suspended, contact admin"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            if not user.password:
                return Response(
                    {"status_code": "102", "message": "User should reset password"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            if not user.check_password(password):
                return Response(
                    {"status_code": "103", "message": "Invalid username/password"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )
        except User.DoesNotExist:
            return Response(
                {"status_code": "104", "message": "Invalid username/password"},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        credentials = f"{username}:{password}"
        token = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
        this_time = timezone.localtime()
        user.last_login = this_time
        user.save()
        return Response(
            {
                "status_code": "100",
                "token": token,
                "branch": user.default_branch.id,
                "company": user.default_company.id,
                "username": username,
                "first_name": user.first_name,
                "last_name": user.last_name,
            },
            status=status.HTTP_200_OK,
        )


class SalesUserRegistrationAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.SalesUserRegistrationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        username = serializer.validated_data.get("username")
        first_name = serializer.validated_data.get("first_name")
        last_name = serializer.validated_data.get("last_name")
        # email = serializer.validated_data.get("email")
        company = serializer.validated_data.get("company")
        branch = serializer.validated_data.get("branch")
        sales_user_role = serializer.validated_data.get("sales_user_role")

        passcode = SalesUser.generate_passcode()

        # get_user_name = search_liberty_pay_username(token=request.headers.get("Authorization"), username=username)
        # if not get_user_name:
        #     return CustomResponse(
        #     data={"message": "Username already exists"},
        #     status_code=400,
        #     status=status.HTTP_400_BAD_REQUEST,
        # )
        print(request.user.email, "REQUEST USER EMAIL")
        print(company.user.email, "COMPANY USER EMAIL")
        print(request.user, "REQUEST USER")
        print(company.user, "COMPANY USERS")
        print(company.user == request.user, "COMPARE USERS")
        if request.user != company.user:

            return CustomResponse(
                data={
                    "message": "unauthorized user",
                },
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )

        models.SalesUserRegistrationDump.objects.create(
            username=username,
            first_name=first_name,
            last_name=last_name,
            email=f"{company.user.email}-{username}",
            default_company=company,
            default_branch=branch,
        )
        User.objects.create(
            username=username,
            first_name=first_name,
            last_name=last_name,
            email=f"{company.user.email}-{username}",
            default_company=company,
            default_branch=branch,
            sales_passcode=passcode,
            sales_user_role=sales_user_role,
        )
        return CustomResponse(
            data={
                "message": "User created successfully.",
                "first_name": first_name,
                "last_name": last_name,
                "username": username,
                "passcode": passcode,
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )


class UpdateSalesUserRoleAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request):
        sales_user_id = self.request.query_params.get("sales_user_id")
        if not valid_uuid_check(sales_user_id):
            return Response(
                {"success": False, "message": "invalid Sales User ID parameter"},
                status=status.HTTP_404_NOT_FOUND,
            )
        user = User.objects.filter(id=sales_user_id, sales_is_deleted=False).first()
        if not user:
            return Response(
                {"success": False, "message": "Sales user not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        serializer = serializers.UpdateSalesUserRoleSerializer(
            user, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        response = {
            "success": True,
            "message": "sales User role updated successfully",
        }
        return Response(response, status=status.HTTP_200_OK)


class ResetSalesDefaultPasscodeAPIView(APIView):
    def post(self, request):
        print(f"\n\n\n{request.data}\n\n\n")
        serializer = serializers.ResetSalesDefaultPasscodeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        username = serializer.validated_data.get("username")
        default_password = serializer.validated_data.get("default_password")
        password = serializer.validated_data.get("password")
        try:
            user = User.objects.get(username=username, sales_is_deleted=False)
            if user.sales_is_suspended:
                return Response(
                    {"message": "User suspended, contact admin"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            if user.password:
                return Response(
                    {"message": "User password already set"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            if user.sales_passcode != default_password:
                return Response(
                    {"message": "Invalid default passcode"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            user.password = make_password(password)
            user.save()
        except User.DoesNotExist:
            return Response(
                {"message": "User not found"}, status=status.HTTP_401_UNAUTHORIZED
            )
        return Response(
            {"message": "user password set successfully"}, status=status.HTTP_200_OK
        )


class GetAllFilterSalesUser(generics.ListAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = serializers.ListSalesUserSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("sales_is_suspended", "sales_is_deleted", "sales_user_role")
    search_fields = ("email", "username", "first_name", "last_name")

    def get_queryset(self):
        company_id = self.request.query_params.get("company_id")
        branch_id = self.request.query_params.get("branch_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})
        if not valid_uuid_check(branch_id):
            raise ValidationError({"message": "invalid Branch ID parameter"})

        get_branch = Branch.objects.filter(id=branch_id, company__id=company_id).first()
        if not get_branch:
            raise ValidationError({"message": "Branch does not exist"})

        owner_email = f"{self.request.user.email}-"
        # Get all sender ID for a company
        all_sales_user = User.objects.filter(
            default_branch=get_branch,
            default_company=get_branch.company,
            sales_is_deleted=False,
            email__startswith=owner_email,
        )
        return all_sales_user

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class StartSalesShiftAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        this_datetime = timezone.localtime()
        serializer = serializers.StartSalesShiftSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        cash_in_hand = serializer.validated_data.get("cash_in_hand")
        if not request.user.default_company:
            return Response(
                {"message": "user does not belong to a company"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not request.user.default_branch:
            return Response(
                {"message": "user does not belong to a branch"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        user_shift = models.SalesTeamShift.objects.filter(
            user=request.user,
            company=request.user.default_company,
            branch=request.user.default_branch,
            end_time__isnull=True,
        ).first()
        if user_shift:
            return Response(
                {"message": "user shift has not ended"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        models.SalesTeamShift.objects.create(
            company=request.user.default_company,
            branch=request.user.default_branch,
            user=request.user,
            start_cash_in_hand=cash_in_hand,
            start_time=this_datetime,
        )

        user_name = request.user.username if request.user.username else "Unknown User"
        send_sales_user_login_email_notification.delay(
            recipient=request.user.default_company.user.email,
            subject=f"Sales User Start Shift Notification for {user_name}",
            template_dir="sales_user_start_shift_notification.html",
            name=user_name,
            company_name=request.user.default_company.company_name,
            time_login=timezone.localtime().strftime("%Y-%m-%d %H:%M:%S"),
        )

        return Response(
            {"message": "user shift started successfully"}, status=status.HTTP_200_OK
        )


class EndSalesShiftAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        this_datetime = timezone.localtime()
        serializer = serializers.EndSalesShiftSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        cash_in_hand = serializer.validated_data.get("cash_in_hand")
        if not request.user.default_company:
            return Response(
                {"message": "user does not belong to a company"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not request.user.default_branch:
            return Response(
                {"message": "user does not belong to a branch"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        user_shift = models.SalesTeamShift.objects.filter(
            user=request.user,
            company=request.user.default_company,
            branch=request.user.default_branch,
            end_time__isnull=True,
        ).first()
        if not user_shift:
            return Response(
                {"message": "you cannot end a shift that has not started"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        get_break_qs = models.SalesTeamBreakShift.objects.filter(
            company=request.user.default_company,
            branch=request.user.default_branch,
            user=request.user,
            user_shift=user_shift,
        )
        if get_break_qs.filter(end_time__isnull=True).first():
            return Response(
                {"message": "you cannot end shift during an ongoing break"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        no_duration = timedelta(hours=0, minutes=0, seconds=0)

        total_break_duration = (
            get_break_qs.aggregate(total_duration=Sum("break_duration"))[
                "total_duration"
            ]
            or no_duration
        )

        end_shift_diff = this_datetime - user_shift.start_time
        end_shift_duration = end_shift_diff.total_seconds()

        hours = int(end_shift_duration // 3600)
        minutes = int((end_shift_duration % 3600) // 60)
        seconds = int(end_shift_duration % 60)

        total_shift_duration = timedelta(hours=hours, minutes=minutes, seconds=seconds)

        user_shift.end_cash_in_hand = cash_in_hand
        user_shift.end_time = this_datetime
        user_shift.work_duration = total_shift_duration - total_break_duration
        user_shift.save()

        user_name = request.user.username if request.user.username else "Unknown User"
        send_sales_user_login_email_notification.delay(
            recipient=request.user.default_company.user.email,
            subject=f"Sales User End Shift Notification for {user_name}",
            template_dir="sales_user_end_shift_notification.html",
            name=user_name,
            company_name=request.user.default_company.company_name,
            time_login=timezone.localtime().strftime("%Y-%m-%d %H:%M:%S"),
        )

        return Response(
            {"message": "user shift ended successfully"}, status=status.HTTP_200_OK
        )


class StartSalesShiftBreakAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        this_datetime = timezone.localtime()
        if not request.user.default_company:
            return Response(
                {"message": "user does not belong to a company"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not request.user.default_branch:
            return Response(
                {"message": "user does not belong to a branch"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        user_shift = models.SalesTeamShift.objects.filter(
            user=request.user,
            company=request.user.default_company,
            branch=request.user.default_branch,
            end_time__isnull=True,
        ).first()
        if not user_shift:
            return Response(
                {"message": "user has no running shift"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        get_break = models.SalesTeamBreakShift.objects.filter(
            company=request.user.default_company,
            branch=request.user.default_branch,
            user=request.user,
            user_shift=user_shift,
            end_time__isnull=True,
        ).first()
        if get_break:
            return Response(
                {"message": "user break is already running"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        models.SalesTeamBreakShift.objects.create(
            company=request.user.default_company,
            branch=request.user.default_branch,
            user=request.user,
            user_shift=user_shift,
            start_time=this_datetime,
        )

        user_name = request.user.username if request.user.username else "Unknown User"
        send_sales_user_login_email_notification.delay(
            recipient=request.user.default_company.user.email,
            subject=f"Sales User Start Shift Notification for {user_name}",
            template_dir="sales_user_start_shift_break_notification.html",
            name=user_name,
            company_name=request.user.default_company.company_name,
            time_login=timezone.localtime().strftime("%Y-%m-%d %H:%M:%S"),
        )

        return Response(
            {"message": "user started break successfully"}, status=status.HTTP_200_OK
        )


class EndSalesShiftBreakAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        this_datetime = timezone.localtime()
        if not request.user.default_company:
            return Response(
                {"message": "user does not belong to a company"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not request.user.default_branch:
            return Response(
                {"message": "user does not belong to a company"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        user_shift = models.SalesTeamShift.objects.filter(
            user=request.user,
            company=request.user.default_company,
            branch=request.user.default_branch,
            end_time__isnull=True,
        ).first()

        if not user_shift:
            return Response(
                {"message": "you can only end break for a running shift"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        get_break = models.SalesTeamBreakShift.objects.filter(
            company=request.user.default_company,
            branch=request.user.default_branch,
            user=request.user,
            user_shift=user_shift,
            end_time__isnull=True,
        ).first()
        if not get_break:
            return Response(
                {"message": "user does not have a running break"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        end_break_diff = this_datetime - get_break.start_time
        end_break_duration = end_break_diff.total_seconds()

        hours = int(end_break_duration // 3600)
        minutes = int((end_break_duration % 3600) // 60)
        seconds = int(end_break_duration % 60)

        total_break_duration = timedelta(hours=hours, minutes=minutes, seconds=seconds)

        get_break.end_time = this_datetime
        get_break.break_duration = total_break_duration
        get_break.save()

        user_name = request.user.username if request.user.username else "Unknown User"
        send_sales_user_login_email_notification.delay(
            recipient=request.user.default_company.user.email,
            subject=f"Sales User End Shift Notification for {user_name}",
            template_dir="sales_user_end_shift_break_notification.html",
            name=user_name,
            company_name=request.user.default_company.company_name,
            time_login=timezone.localtime().strftime("%Y-%m-%d %H:%M:%S"),
        )

        return Response(
            {"message": "user break ended successfully"}, status=status.HTTP_200_OK
        )


class GetAllFilterSalesShift(generics.ListAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = serializers.ListSalesAttendanceSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = ("sales_is_suspended", "sales_is_deleted")
    search_fields = ("branch__name", "company__company_name")

    def get_queryset(self):
        company_id = self.request.query_params.get("company_id")
        branch_id = self.request.query_params.get("branch_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})
        if not valid_uuid_check(branch_id):
            raise ValidationError({"message": "invalid Branch ID parameter"})

        get_branch = Branch.objects.filter(id=branch_id, company__id=company_id).first()
        if not get_branch:
            raise ValidationError({"message": "Branch does not exist"})

        owner_email = f"{self.request.user.email}-"
        # Get all sender ID for a company
        all_sales_attendance = models.SalesTeamShift.objects.filter(
            branch=get_branch,
            company=get_branch.company,
            user__email__startswith=owner_email,
        )
        return all_sales_attendance

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetAllFilterSalesShiftBreak(generics.ListAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = serializers.ListSalesBreakSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = ()
    search_fields = ("branch__name", "company__company_name")

    def get_queryset(self):
        company_id = self.request.query_params.get("company_id")
        branch_id = self.request.query_params.get("branch_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})
        if not valid_uuid_check(branch_id):
            raise ValidationError({"message": "invalid Branch ID parameter"})

        get_branch = Branch.objects.filter(id=branch_id, company__id=company_id).first()
        if not get_branch:
            raise ValidationError({"message": "Branch does not exist"})

        owner_email = f"{self.request.user.email}"
        # Get all sender ID for a company
        all_sales_attendance = models.SalesTeamBreakShift.objects.filter(
            branch=get_branch,
            company=get_branch.company,
            user__email__startswith=owner_email,
        )
        return all_sales_attendance

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetAllFilterSalesShiftUserBreak(generics.ListAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = serializers.ListSalesBreakSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = ()
    search_fields = ("branch__name", "company__company_name")

    def get_queryset(self):
        if not self.request.user.default_company:
            return Response(
                {"message": "user does not belong to a company"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not self.request.user.default_branch:
            return Response(
                {"message": "user does not belong to a branch"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get all sender ID for a company
        all_sales_attendance = models.SalesTeamBreakShift.objects.filter(
            user=self.request.user,
            branch=self.request.user.default_branch,
            company=self.request.user.default_company,
        )
        return all_sales_attendance

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetAllFilterSalesUserShift(generics.ListAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = serializers.ListSalesAttendanceSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = ("sales_is_suspended", "sales_is_deleted")
    search_fields = ("branch__name", "company__company_name")

    def get_queryset(self):
        if not self.request.user.default_company:
            return Response(
                {"message": "user does not belong to a company"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not self.request.user.default_branch:
            return Response(
                {"message": "user does not belong to a branch"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get all sender ID for a company
        all_sales_attendance = models.SalesTeamShift.objects.filter(
            user=self.request.user,
            branch=self.request.user.default_branch,
            company=self.request.user.default_company,
        )
        return all_sales_attendance

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class SalesUserShiftStatusAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        this_datetime = timezone.localtime()
        if not request.user.default_company:
            return Response(
                {"message": "user does not belong to a company"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not request.user.default_branch:
            return Response(
                {"message": "user does not belong to a branch"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        user_shift = models.SalesTeamShift.objects.filter(
            user=request.user,
            branch=request.user.default_branch,
            company=request.user.default_company,
            end_time__isnull=True,
        ).first()
        shift_started = False
        shift_ended = False

        if user_shift:
            if user_shift.start_time:
                shift_started = True

        user_break = models.SalesTeamBreakShift.objects.filter(
            user=request.user,
            branch=request.user.default_branch,
            company=request.user.default_company,
            user_shift=user_shift,
            end_time__isnull=True,
        ).last()

        ongoing_break = False
        if user_break:
            ongoing_break = True

        response = {
            "shift_started": shift_started,
            "shift_ended": shift_ended,
            "ongoing_break": ongoing_break,
        }
        return Response(response, status=status.HTTP_200_OK)


class AdminResetSalesDefaultPasscodeAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        company_id = self.request.query_params.get("company_id")
        branch_id = self.request.query_params.get("branch_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})
        if not valid_uuid_check(branch_id):
            raise ValidationError({"message": "invalid Branch ID parameter"})

        passcode = SalesUser.generate_passcode()
        serializer = serializers.AdminResetSalesDefaultPasscodeSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        username = serializer.validated_data.get("username")
        try:
            user = User.objects.get(
                username=username,
                default_company__id=company_id,
                default_branch__id=branch_id,
            )
            user.sales_passcode = passcode
            user.password = None
            user.save()
        except User.DoesNotExist:
            return Response(
                {"message": "User not found"}, status=status.HTTP_401_UNAUTHORIZED
            )
        return Response(
            {"passcode": f"{passcode}", "message": "user password set successfully"},
            status=status.HTTP_200_OK,
        )


class AdminRestrictSalesUserAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        company_id = self.request.query_params.get("company_id")
        branch_id = self.request.query_params.get("branch_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})
        if not valid_uuid_check(branch_id):
            raise ValidationError({"message": "invalid Branch ID parameter"})

        serializer = serializers.AdminResetSalesDefaultPasscodeSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        username = serializer.validated_data.get("username")
        try:
            user = User.objects.get(
                username=username,
                default_company__id=company_id,
                default_branch__id=branch_id,
            )
            user.sales_is_suspended = True
            user.save()
        except User.DoesNotExist:
            return Response(
                {"message": "User not found"}, status=status.HTTP_401_UNAUTHORIZED
            )
        return Response(
            {"message": "user restricted successfully"}, status=status.HTTP_200_OK
        )


class AdminUnRestrictSalesUserAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        company_id = self.request.query_params.get("company_id")
        branch_id = self.request.query_params.get("branch_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})
        if not valid_uuid_check(branch_id):
            raise ValidationError({"message": "invalid Branch ID parameter"})

        serializer = serializers.AdminResetSalesDefaultPasscodeSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        username = serializer.validated_data.get("username")
        try:
            user = User.objects.get(
                username=username,
                default_company__id=company_id,
                default_branch__id=branch_id,
            )
            user.sales_is_suspended = False
            user.save()
        except User.DoesNotExist:
            return Response(
                {"message": "User not found"}, status=status.HTTP_401_UNAUTHORIZED
            )
        return Response(
            {"message": "user unrestricted successfully"}, status=status.HTTP_200_OK
        )


class AdminDeleteSalesUserAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        company_id = self.request.query_params.get("company_id")
        branch_id = self.request.query_params.get("branch_id")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID parameter"})
        if not valid_uuid_check(branch_id):
            raise ValidationError({"message": "invalid Branch ID parameter"})

        serializer = serializers.AdminResetSalesDefaultPasscodeSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        username = serializer.validated_data.get("username")
        try:
            user = User.objects.get(
                username=username,
                default_company__id=company_id,
                default_branch__id=branch_id,
            )
            user.sales_is_deleted = True
            user.save()
        except User.DoesNotExist:
            return Response(
                {"message": "User not found"}, status=status.HTTP_401_UNAUTHORIZED
            )
        return Response(
            {"message": "user deleted successfully"}, status=status.HTTP_200_OK
        )


class SecretQuestionAPIView(APIView):
    def get(self, request, *args, **kwargs):
        secret_questions = [question[0] for question in SecretQuestionChoices.choices]
        return CustomResponse(
            data={"questions": secret_questions},
            status_code=200,
            status=status.HTTP_200_OK,
        )

    def put(self, request, *args, **kwargs):
        serializer = serializers.SecretQuestionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        username = serializer.validated_data["username"]
        user = User.objects.get(username=username, sales_is_deleted=False)
        if user.sales_is_suspended:
            return CustomResponse(
                error={"message": "User suspended, contact admin"},
                status=status.HTTP_401_UNAUTHORIZED,
                status_code=401,
            )
        user.secret_question = serializer.validated_data["secret_question"]
        user.secret_answer = serializer.validated_data["secret_answer"]
        user.save()
        return CustomResponse(
            data={"message": "successfully set secret question."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class VfdMeetingView(APIView):

    def post(self, request):
        from core.helpers.send_sms import vfd_sms

        serializer = serializers.VfdMeetingSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        work_email = serializer_data.get("work_email")
        full_name = serializer_data.get("full_name")
        phone_number = serializer_data.get("phone_number")
        country = serializer_data.get("country")
        employee_headcount = serializer_data.get("employee_headcount")
        availability = serializer_data.get("availability")
        product_vertical = serializer_data.get("product_vertical")
        meeting_link = serializer_data.get("meeting_link")
        company_name = serializer_data.get("company_name")

        timezone = pytz.timezone("Africa/Lagos")
        availability = timezone.localize(
            datetime.strptime(availability, "%Y-%m-%dT%H:%M:%S.%fZ")
        )
        parsed_date = availability.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        email_reminder_date = availability.strftime("%Y-%m-%d %H:%M:%S")
        email_date = availability.strftime("%a %d/%m/%Y %I:%M%p") + " WAT"
        email_reminder_date = availability.strftime("%Y-%m-%d %H:%M:%S")

        try:
            vfd_schedule = models.VfdMeeting.objects.create(
                work_email=work_email,
                full_name=full_name,
                phone_number=phone_number,
                country=country,
                employee_headcount=employee_headcount,
                availability=parsed_date,
                product_vertical=product_vertical,
                meeting_link=meeting_link,
                company_name=company_name,
            )
            meeting_id = vfd_schedule.pk
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while attempting to create a meeting!",
                    "error": str(e),
                }
            )

        serialized_meeting_schedule = serializers.VfdMeetingSerializer(vfd_schedule)

        validated_data = {
            "full_name": full_name,
            "work_email": work_email,
            "phone_number": phone_number,
            "company_name": company_name,
            "country": country,
            "employee_head_count": employee_headcount,
        }

        try:
            demo = crm_calendly_demo.create_demo(
                product_verticals=product_vertical,
                start_time=availability,
                validated_data=validated_data,
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while attempting to retrieve verticals!",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        if vfd_schedule:
            try:
                sales_agent = assign_meeting_to_sales_agent(leads_email=work_email)
            except Exception as e:
                return Response(
                    {
                        "message": "An error occured while attempting to assign a meeting to a sales agent!",
                        "error": str(e),
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            agent_email = sales_agent.get("agent_email").lower()
            agent_name = sales_agent.get("agent_name")

            vfd_meeting_details = {
                "sales_lead_name": agent_name,
                "sales_lead_email": agent_email,
                "invitee_name": full_name,
                "invitee_email": work_email,
                "invitee_phone_number": phone_number,
                "product_vertical": product_vertical,
                "meeting_date": str(email_date),
                "meeting_link": str(meeting_link),
            }

            # Notify agent of meeting
            notify_sales_agent.delay(
                subject="PayBox360 Demo",
                agent_email=agent_email,
                invitee_email=vfd_meeting_details["invitee_email"],
                invitee_name=vfd_meeting_details["invitee_name"],
                invitee_phone_number=vfd_meeting_details["invitee_phone_number"],
                product_vertical=", ".join(vfd_meeting_details["product_vertical"]),
                meeting_date=vfd_meeting_details["meeting_date"],
                meeting_link=vfd_meeting_details["meeting_link"],
                agent_name=agent_name,
                company_name=company_name,
                meeting_id=meeting_id,
            )

            notify_sales_agent.delay(
                subject="Sales Lead Update Email: New PayBox360 Demo Sheduled",
                agent_email=config("SALES_LEAD_EMAIL"),
                invitee_email=vfd_meeting_details["invitee_email"],
                invitee_name=vfd_meeting_details["invitee_name"],
                invitee_phone_number=vfd_meeting_details["invitee_phone_number"],
                product_vertical=", ".join(vfd_meeting_details["product_vertical"]),
                meeting_date=vfd_meeting_details["meeting_date"],
                meeting_link=vfd_meeting_details["meeting_link"],
                agent_name="Ayomide",
                company_name=company_name,
                meeting_id=meeting_id,
            )

            send_email.delay(
                subject="Sales Lead Update Email: New PayBox360 Demo Sheduled",
                recipient="<EMAIL>",
                invitee_email=vfd_meeting_details["invitee_email"],
                invitee_name=vfd_meeting_details["invitee_name"],
                invitee_phone_number=vfd_meeting_details["invitee_phone_number"],
                product_vertical=", ".join(vfd_meeting_details["product_vertical"]),
                meeting_date=vfd_meeting_details["meeting_date"],
                meeting_link=vfd_meeting_details["meeting_link"],
                template_dir="vfd_meeting_details.html",
                name=agent_name,
                company_name=company_name,
            )

            try:
                chosen_vertical(
                    full_name=full_name,
                    phone_number=phone_number,
                    email=work_email,
                    product_vertical=product_vertical,
                )
            except Exception as e:
                return Response(
                    {
                        "message": "An error occured while attempting to add a contact to brevo!",
                        "error": str(e),
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            try:
                vfd_sms(
                    phone_number=phone_number,
                    product_vertical=product_vertical,
                )
            except Exception as e:
                return Response(
                    {
                        "message": "An error occurred while attempting to send an sms to your line.",
                        "error": str(e),
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            try:
                sales_person_data = AssignedSalesPerson()
                sales_person_data.send_assigned_sales_person_data(
                    **vfd_meeting_details,
                )
            except Exception as e:
                return Response(
                    {
                        "message": "An error occurred while attempting to send sales person's data.",
                        "error": str(e),
                    }
                )

            send_reminder_emails(
                subject="PayBox360 Demo Reminder",
                recipient=vfd_meeting_details["invitee_email"],
                meeting_date=email_reminder_date,
                availability=availability,
                meeting_link=vfd_meeting_details["meeting_link"],
                template_dir="meeting_reminder.html",
                sales_lead_name=vfd_meeting_details["sales_lead_name"],
                sales_lead_email=vfd_meeting_details["sales_lead_email"],
                product_vertical=vfd_meeting_details["product_vertical"],
                invitee_name=vfd_meeting_details["invitee_name"],
            )
        return Response(
            {
                "message": "Meeting Scheduled successfully",
                "schedule_data": serialized_meeting_schedule.data,
                "schedule_id": vfd_schedule.id,
                "crm_demo_details": demo,
            },
            status=status.HTTP_201_CREATED,
        )


class SalesAgentView(APIView):
    def post(self, request):
        serializer = serializers.SalesAgentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        name = serializer_data.get("name")
        email = serializer_data.get("email")
        phone_number = serializer_data.get("phone_number")

        try:
            sales_agent = models.SalesAgents.objects.create(
                name=name,
                email=email,
                phone_number=phone_number,
            )

            serialized_sales_agent = serializers.SalesAgentSerializer(sales_agent)

            return Response(
                {
                    "message": "Sales agent created successfully!",
                    "sales_agent_data": serialized_sales_agent.data,
                    "sales_agent_id": sales_agent.id,
                },
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {
                    "message": "Failed to create sales agent",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def delete(self, request):
        serializer = serializers.DeleteSalesAgentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        name = serializer_data.get("name")

        try:
            sales_agent_instance = models.SalesAgents.objects.get(name=name)
            sales_agent_instance.soft_delete()
            return Response(
                {
                    "message": "Sales agent deleted successfully!",
                },
                status=status.HTTP_200_OK,
            )
        except models.SalesAgents.DoesNotExist:
            return Response(
                {"message": "Sales agent does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class DownloadOfflineAppAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        app_type = request.query_params.get("app_type")
        app_choices = [
            SupportedAppChoices.ANDROID,
            SupportedAppChoices.MACOS,
            SupportedAppChoices.WINDOWS,
        ]
        if not app_type or app_type.upper() not in app_choices:
            return CustomResponse(
                errors={"message": "Provide a valid application type."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        application = models.OfflineApplication.objects.filter(
            app_type=app_type.upper()
        ).first()
        if application is not None:
            return CustomResponse(
                data={"message": application.app.url},
                status_code=200,
                status=status.HTTP_200_OK,
            )
        else:
            return CustomResponse(
                data={"message": "Application not found."},
                status_code=404,
                status=status.HTTP_404_NOT_FOUND,
            )


class CreateCampaignLeadView(generics.CreateAPIView):
    # authentication_classes = [CustomUserAuthentication]
    # permission_classes = [IsAuthenticated]
    serializer_class = serializers.CampaignLeadSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return CustomResponse(
            data={"message": "successfully created campaign lead."},
            status_code=201,
            status=status.HTTP_201_CREATED,
        )

    def put(self, request):
        data = request.data

        try:
            lead_instance = models.CampaignLead.objects.get(
                unique_id=data.get("unique_id")
            )
        except models.CampaignLead.DoesNotExist:
            raise ValidationError({"message": "Lead does not exist"})

        serializer = self.serializer_class(lead_instance, data=data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return CustomResponse(
            data={"message": "successfully updated campaign lead."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


############# PROFILE AND SETTINGS  ###############

BASE_URL = "https://kyc.libertypayng.com/"


def check_authorization_header(auth_header):
    if not auth_header or not auth_header.startswith("Bearer "):
        return Response(
            {"error": "Authorization token is required"},
            status=status.HTTP_401_UNAUTHORIZED,
        )


@api_view(["POST"])
@authentication_classes([CustomUserAuthentication])
@permission_classes([IsAuthenticated])
def reset_transactional_pin(request):

    auth_header = request.headers.get("Authorization")
    check_authorization_header(auth_header=auth_header)

    serializer = ResetPasswordSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    url = "https://dev.libertypayng.com/agency/user/reset_login_pin_in_app/"
    headers = {"Authorization": auth_header, "Content-Type": "application/json"}
    payload = serializer.data

    try:
        response = requests.post(url, headers=headers, json=payload)
        return Response(response.json(), status=response.status_code)

    except requests.RequestException as e:
        return Response(
            {"error": "The password reset no work o", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@authentication_classes([CustomUserAuthentication])
@permission_classes([IsAuthenticated])
def verify_bvn_liberty(request):
    user = request.user
    # print(f'user::::::::::::::::::::{user}')
    # print(f'id::::::::::::::::::::::::{user.id}')
    serializer = VerifyBVNSerializer(data=request.data)

    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    url = f"{BASE_URL}kyc/verify_bvn_liberty/"
    headers = {"Content-Type": "application/json"}
    payload = serializer.data
    try:
        response = requests.post(url, headers=headers, json=payload)
        data = response.json()
        verified = data.get("verified")
        if verified == True:
            update_user = User.objects.get(id=user.id)
            update_user.kyc_level = 1
            update_user.save()

        return Response(response.json(), status=response.status_code)

    except requests.RequestException as e:
        return Response(
            {"error": "Failed", "details": str(e)}, status=status.HTTP_404_NOT_FOUND
        )


@api_view(["POST"])
@authentication_classes([CustomUserAuthentication])
@permission_classes([IsAuthenticated])
def match_face_liberty(request):
    user = request.user
    serializer = ImageSerializer(data=request.data)

    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    email = serializer.validated_data["email"]
    user_photo = serializer.validated_data["user_photo"]

    try:
        url = f"{BASE_URL}kyc/match_face_liberty/"
        files = {
            "user_photo": (user_photo.name, user_photo.read(), user_photo.content_type)
        }
        data = {"email": email}

        response = requests.post(url, files=files, data=data)

        if response.status_code == 200:
            response_data = response.json()
            bvn_image_verified = response_data.get("bvn_image_verified")

            if bvn_image_verified == True:
                update_user = User.objects.get(id=user.id)
                update_user.kyc_level = 2
                update_user.save()
                return Response(response_data, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "Face verification failed", "details": response_data},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {
                    "message": "External service returned an error",
                    "details": response.text,
                },
                status=response.status_code,
            )

    except requests.RequestException as e:
        return Response(
            {"error": "Request to verification service failed", "details": str(e)},
            status=status.HTTP_408_REQUEST_TIMEOUT,
        )


@api_view(["POST"])
@authentication_classes([CustomUserAuthentication])
@permission_classes([IsAuthenticated])
def submit_guarantor_form(request):

    user = request.user
    serializer = GuarantorFormSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    guarantor = Guarantor.objects.create(
        user=user,
        email=serializer.validated_data["email"],
        guarantor_name=serializer.validated_data["guarantor_name"],
        guarantor_phone_number=serializer.validated_data["guarantor_phone_number"],
        guarantor_email=serializer.validated_data["guarantor_email"],
        guarantor_occupation=serializer.validated_data["guarantor_occupation"],
        guarantor_address=serializer.validated_data["guarantor_address"],
        next_of_kin_name=serializer.validated_data["next_of_kin_name"],
        next_of_kin_relationship=serializer.validated_data["next_of_kin_relationship"],
        next_of_kin_phone_number=serializer.validated_data["next_of_kin_phone_number"],
        next_of_kin_address=serializer.validated_data["next_of_kin_address"],
    )

    return Response(
        {"message": "Guarantor submitted", "id": guarantor.id},
        status=status.HTTP_201_CREATED,
    )


@api_view(["GET"])
@authentication_classes([CustomUserAuthentication])
@permission_classes([IsAuthenticated])
def confirm_kyc_verification(request):

    user = request.user
    try:
        if user.kyc_level == str(1):
            return Response({"kyc_1": True, "kyc_2": False, "kyc_3": False})
        elif user.kyc_level == str(2):
            return Response({"kyc_1": True, "kyc_2": True, "kyc_3": False})
        elif user.kyc_level == str(3):
            return Response({"kyc_1": True, "kyc_2": True, "kyc_3": True})
        return Response({"Level": user.kyc_level, "Updated": user.kyc_updated})
    except User.DoesNotExist as err:
        return Response({"message": "User not found", "details": str({err})})


class UploadOfflineAppAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.OfflineApplicationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(uploaded_by=request.user)
        return CustomResponse(
            data={"message": "Application uploaded successfully."},
            status_code=201,
            status=status.HTTP_201_CREATED,
        )


class ConstantTableSalesTeamView(APIView):
    def post(self, request):
        serializer = serializers.ConstantTableSalesTeamViewSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {
                "message": "Sales team emails saved successfully!",
            },
            status=status.HTTP_201_CREATED,
        )


class UpdatePreferenceAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        serializer = UpdatePreferenceSerializer(user)
        response = {
            "success": True,
            "data": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        user = request.user
        serializer = UpdatePreferenceSerializer(user, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        response = {
            "success": True,
            "message": "preference updated successfully",
        }
        return Response(response, status=status.HTTP_200_OK)


class UpdateNotificationAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        serializer = UpdateNotificationSerializer(user)
        response = {
            "success": True,
            "data": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        user = request.user
        serializer = UpdateNotificationSerializer(user, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        response = {
            "success": True,
            "message": "notification updated successfully",
        }
        return Response(response, status=status.HTTP_200_OK)


@staff_member_required
def view_logs(
    request,
):
    """View logs dynamically based on the file name with color highlighting."""
    logs_dir = os.path.join(settings.BASE_DIR, "logs")  # Fixed logs directory

    file_name = request.GET.get("file_name")
    if not file_name:

        file_name = f"errors_{datetime.now().strftime('%Y-%m-%d')}.log"

    log_file = os.path.join(logs_dir, file_name)

    if not os.path.exists(log_file):
        return HttpResponse(
            "<h2 style='color: red; text-align: center;'>Log file not found</h2>",
            status=404,
        )

    with open(log_file, "r") as file:
        log_content = file.read()

    # Apply color highlighting
    def colorize_logs(log_text):
        log_text = re.sub(
            r"(ERROR)",
            r"<span style='color: red; font-weight: bold;'>\1</span>",
            log_text,
        )
        log_text = re.sub(
            r"(WARNING)",
            r"<span style='color: yellow; font-weight: bold;'>\1</span>",
            log_text,
        )
        log_text = re.sub(
            r"(INFO)",
            r"<span style='color: limegreen; font-weight: bold;'>\1</span>",
            log_text,
        )
        return log_text

    log_content = colorize_logs(log_content)

    # HTML template with Dark Mode and wide layout
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Logs - {file_name}</title>
        <style>
            body {{
                background-color: #1e1e1e;
                color: #c7c7c7;
                font-family: Arial, sans-serif;
                padding: 20px;
            }}
            .container {{
                max-width: 90%;
                margin: auto;
                background: #2e2e2e;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }}
            pre {{
                background: #333;
                color: #e6e6e6;
                padding: 15px;
                overflow-x: auto;
                border-radius: 5px;
                font-size: 14px;
                white-space: pre-wrap;
            }}
            h1 {{
                text-align: center;
                color: #fff;
            }}
            .back-link {{
                display: block;
                text-align: center;
                margin-top: 20px;
                color: #00aaff;
                text-decoration: none;
            }}
            .back-link:hover {{
                text-decoration: underline;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>📜 Logs - {file_name}</h1>
            <pre>{log_content}</pre>
            <a href="/admin/" class="back-link">🔙 Back to Admin</a>
        </div>
    </body>
    </html>
    """

    return HttpResponse(html_template)


# class PlatformAccessVerificationView(APIView):
#     authentication_classes = [CustomUserAuthentication]
#     permission_classes = [IsAuthenticated]
#
#     def get(self, request, *args, **kwargs):
#         """Handle GET request to verify platform access for the current user."""
#         # Check if user has an associated company
#
#         if not request.user.default_company:
#             return Response(
#                 {"access": False, "reason": "No company associated"},
#                 status=status.HTTP_403_FORBIDDEN,
#             )
#
#         # Get all subscriptions for the company, ordered by creation date
#         subscriptions = CompanySubscription.objects.filter(
#             company=request.user.default_company
#         ).order_by("-created_at")
#
#         if not subscriptions.exists():
#             return Response(
#                 {"access": False, "reason": "No active subscription"},
#                 status=status.HTTP_200_OK,
#             )
#
#         # Find the active subscription if any
#         active_subscription = subscriptions.filter(
#             status__in=["active", "trial"]
#         ).first()
#         access_type = active_subscription.access_type if active_subscription else None
#
#         # Generate access response data
#         response_data = self._generate_access_response(
#             active_subscription, access_type, subscriptions
#         )
#         return Response(response_data)
#
#     def _generate_access_response(self, subscription, access_type, subscriptions):
#         """Generate the access response data structure based on subscription status."""
#         if not subscription:
#             return {"access": False, "reason": "No active subscription"}
#
#         # Get all modules grouped by access type
#         modules_by_access = self._get_modules_by_access_type(subscriptions)
#
#         # Build the base access details
#         access_details = {
#             "access": True,
#             "type": access_type,
#             "status": subscription.status,
#             "access_path": subscription.access_type,
#             "modules_by_access": modules_by_access,
#             "subscription_details": self._get_subscription_details(subscription),
#         }
#
#         # Add trial-specific information if applicable
#         if access_type == AccessPath.TRIAL:
#             trial_days = getattr(settings, "SUBSCRIPTION_FREE_TRIAL", 14)
#             trial_end_date = subscription.created_at + timedelta(days=trial_days)
#             trial_days_remaining = max(0, (trial_end_date - timezone.now()).days)
#             access_details["trial_days_remaining"] = trial_days_remaining
#
#         return access_details
#
#         # Add token-specific information if applicable
#         if access_type == AccessPath.TOKEN:
#             access_details["token_details"] = self._get_token_details(subscription)
#
#         return access_details
#
#     def _get_subscription_details(self, subscription):
#         """Extract subscription details including dates and plan information."""
#         start_date = subscription.created_at
#         end_date = None
#
#         # For token subscriptions, end_date is None (unlimited until tokens exhausted)
#         if subscription.access_type == AccessPath.TOKEN:
#             end_date = None
#         else:
#             # Calculate end date if overall_sub_days is available
#             if (
#                 subscription.overall_sub_days
#                 and subscription.overall_sub_days.isdigit()
#             ):
#                 try:
#                     end_date = start_date + timedelta(
#                         days=int(subscription.overall_sub_days)
#                     )
#                 except ValueError:
#                     end_date = None
#
#         # Prepare subscription details
#         subscription_details = {
#             "id": subscription.id,
#             "start_date": start_date,
#             "end_date": end_date,
#             "plan": subscription.plan.name if subscription.plan else None,
#             "access_type": subscription.access_type,
#         }
#
#         # Add price information if plan has a price attribute
#         if subscription.plan and hasattr(subscription.plan, "price"):
#             subscription_details["price"] = subscription.plan.price
#
#         return subscription_details
#
#     def _get_token_details(self, subscription):
#         """Extract token-specific details for token-based subscriptions."""
#         if subscription.access_type != AccessPath.TOKEN:
#             return None
#
#         # Calculate token usage percentage
#         total_tokens = (
#             subscription.token_previous_balance + subscription.token_current_balance
#         )
#         if total_tokens > 0:
#             usage_percentage = (
#                 (total_tokens - subscription.token_current_balance) / total_tokens
#             ) * 100
#         else:
#             usage_percentage = 0
#
#         # Determine token status message
#         status_messages = {
#             TokenStatus.ACTIVE: "Token balance is sufficient for continued access",
#             TokenStatus.INSUFFICIENT: "Token balance is low or insufficient - please top up",
#             TokenStatus.INACTIVE: "Token subscription is inactive - payment required",
#         }
#
#         token_details = {
#             "current_balance": float(subscription.token_current_balance),
#             "previous_balance": float(subscription.token_previous_balance),
#             "token_status": subscription.token_status,
#             "token_status_display": dict(TokenStatus.CHOICES).get(
#                 subscription.token_status, "Unknown"
#             ),
#             "status_message": status_messages.get(
#                 subscription.token_status, "Unknown status"
#             ),
#             "usage_percentage": round(usage_percentage, 2),
#             "is_low_balance": subscription.token_current_balance
#             < 10,  # Warning threshold
#             "is_critical_balance": subscription.token_current_balance
#             < 5,  # Critical threshold
#             "access_unlimited": True,  # Token subscriptions provide unlimited time access
#             "payment_required": subscription.token_status == TokenStatus.INACTIVE,
#         }
#
#         return token_details
#
#     def _get_modules_by_access_type(self, subscriptions):
#         """Group modules by their access types (PAID, TRIAL, BYPASS)."""
#         # Initialize result structure with valid access types
#
#         modules_by_access = {
#             AccessPath.PAID: [],
#             AccessPath.TRIAL: [],
#             AccessPath.BYPASS: [],
#             AccessPath.TOKEN: [],
#         }
#
#         # Get all module subscriptions related to the company subscriptions
#         for subscription in subscriptions:
#             # Ensure we're dealing with a valid access_type
#             access_type = subscription.access_type
#             if access_type not in [
#                 AccessPath.PAID,
#                 AccessPath.TRIAL,
#                 AccessPath.BYPASS,
#                 AccessPath.TOKEN,
#             ]:
#                 # Skip if access_type is not valid or handle accordingly
#                 continue
#
#             # Get module subscriptions for this company subscription
#             module_subscriptions = ModuleSubscription.objects.filter(
#                 company_subscription=subscription
#             ).select_related("module", "plan")
#
#             # Process each module subscription
#             for mod in module_subscriptions:
#                 module_info = self._create_module_info(mod)
#                 modules_by_access[access_type].append(module_info)
#
#         return modules_by_access
#
#     def _create_module_info(self, module_subscription):
#         """Create module information dictionary with all required fields."""
#         access_type = module_subscription.company_subscription.access_type
#
#         module_info = {
#             "module_id": module_subscription.module.id,
#             "module_name": module_subscription.module.name,
#             "plan": module_subscription.plan.name if module_subscription.plan else None,
#             "status": module_subscription.status,
#             "days_remaining": module_subscription.days_remaining(),
#             "is_active": module_subscription.is_active,
#             "start_date": module_subscription.start_date,
#             "end_date": module_subscription.end_date,
#             "access_type": access_type,
#         }
#
#         if access_type == AccessPath.TOKEN:
#             # For token subscriptions, access is unlimited until tokens are exhausted
#             module_info["days_remaining"] = None  # Unlimited
#             module_info["access_duration"] = "unlimited"
#             module_info["expires_on_token_exhaustion"] = True
#         else:
#             # For other subscription types, calculate days remaining
#             module_info["days_remaining"] = module_subscription.days_remaining()
#             module_info["access_duration"] = "limited"
#             module_info["expires_on_token_exhaustion"] = False
#
#         # Add price information if available
#         if module_subscription.plan and hasattr(module_subscription.plan, "price"):
#             module_info["price"] = module_subscription.plan.price
#
#         elif access_type == AccessPath.TOKEN:
#             # For token subscriptions, show token-based pricing
#             module_info["price_model"] = "token_based"
#             module_info["price_per_token"] = 10  # From TOKEN_PRICE_PER_UNIT constant
#
#         return module_info


class PlatformAccessVerificationView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """Handle GET request to verify platform access for the current user."""
        # Get company_id from query params or fall back to default company
        company_id = request.query_params.get("company_id")

        if company_id:
            try:
                # Get all companies the user has access to
                user_companies = self._get_user_companies(request.user)
                company = user_companies.get(id=company_id)
            except Company.DoesNotExist:
                return Response(
                    {"access": False, "reason": "Company not found or access denied"},
                    status=status.HTTP_404_NOT_FOUND,
                )
        else:
            # Fall back to default company
            company = request.user.default_company

        # Check if user has an associated company
        if not company:
            return Response(
                {"access": False, "reason": "No company associated"},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Get all subscriptions for the company, ordered by creation date
        subscriptions = CompanySubscription.objects.filter(company=company).order_by(
            "-created_at"
        )

        if not subscriptions.exists():
            return Response(
                {"access": False, "reason": "No active subscription"},
                status=status.HTTP_200_OK,
            )

        # Find the active subscription based on access type
        active_subscription = self._find_active_subscription(subscriptions)

        if not active_subscription:
            return Response(
                {"access": False, "reason": "No active subscription"},
                status=status.HTTP_200_OK,
            )

        access_type = active_subscription.access_type

        # Generate access response data
        response_data = self._generate_access_response(
            active_subscription, access_type, subscriptions
        )
        return Response(response_data)

    def _find_active_subscription(self, subscriptions):
        """
        Find the active subscription based on access type and specific conditions.
        This handles different access types properly, especially token subscriptions.
        """
        # For token subscriptions, check token_status field first
        token_subscription = subscriptions.filter(
            access_type=AccessPath.TOKEN,
            token_status__in=[TokenStatus.ACTIVE, TokenStatus.INSUFFICIENT],
        ).first()

        if token_subscription:
            return token_subscription

        # For standard subscriptions (paid/trial), check status
        standard_active = subscriptions.filter(status__in=["active", "trial"]).first()

        if standard_active:
            return standard_active

        # Check for bypass subscriptions (these should always be active)
        bypass_subscription = subscriptions.filter(
            access_type=AccessPath.BYPASS
        ).first()

        if bypass_subscription:
            return bypass_subscription

        # Check for paid subscriptions that might be active based on module subscriptions
        paid_subscriptions = subscriptions.filter(access_type=AccessPath.PAID)

        for paid_sub in paid_subscriptions:
            # Check if any module subscription is active
            active_modules = paid_sub.modulesubscription_set.filter(
                status="active", is_active=True, end_date__gt=timezone.now()
            )
            if active_modules.exists():
                return paid_sub

        return None

    def _get_user_companies(self, user):
        """
        Get all companies the user has access to.
        This includes companies they own and companies they're a team member of.
        """
        from requisition.models import Company, TeamMember
        from requisition.helpers.enums import UserStatus
        from django.db.models import Q

        # Get company IDs the user owns
        owned_company_ids = Company.objects.filter(user=user).values_list(
            "id", flat=True
        )

        # Get company IDs the user is a team member of
        team_memberships = TeamMember.objects.filter(
            member=user, status=UserStatus.ACTIVE, is_active=True
        ).select_related("team__company")

        team_company_ids = [
            membership.team.company.id
            for membership in team_memberships
            if membership.team.company
        ]

        # Combine both lists and remove duplicates
        all_company_ids = list(set(list(owned_company_ids) + team_company_ids))

        # Return a queryset that can be filtered
        return Company.objects.filter(id__in=all_company_ids)

    def _generate_access_response(self, subscription, access_type, subscriptions):
        """Generate the access response data structure based on subscription status."""
        if not subscription:
            return {"access": False, "reason": "No active subscription"}

        # For token subscriptions, check token_status for access validation
        if access_type == AccessPath.TOKEN:
            if subscription.token_status == TokenStatus.INACTIVE:
                return {
                    "access": False,
                    "reason": "Token subscription inactive - payment required",
                }

        # Get all modules grouped by access type
        modules_by_access = self._get_modules_by_access_type(subscriptions)

        # Build the base access details
        access_details = {
            "access": True,
            "type": access_type,
            "status": subscription.status,
            "access_path": subscription.access_type,
            "modules_by_access": modules_by_access,
            "subscription_details": self._get_subscription_details(subscription),
        }

        # Add token balance for all subscription types (or conditionally for token types only)
        if access_type == AccessPath.TOKEN:
            access_details["token_current_balance"] = float(
                subscription.token_current_balance
            )
        else:
            # Optional: include for all types showing 0.00 for non-token subscriptions
            access_details["token_current_balance"] = float(
                subscription.token_current_balance
            )

        # Add trial-specific information if applicable
        if access_type == AccessPath.TRIAL:
            trial_days = getattr(settings, "SUBSCRIPTION_FREE_TRIAL", 14)
            trial_end_date = subscription.created_at + timedelta(days=trial_days)
            trial_days_remaining = max(0, (trial_end_date - timezone.now()).days)
            access_details["trial_days_remaining"] = trial_days_remaining

        # Add token-specific information if applicable
        if access_type == AccessPath.TOKEN:
            access_details["token_details"] = self._get_token_details(subscription)

        return access_details

    def _get_subscription_details(self, subscription):
        """Extract subscription details including dates and plan information."""
        start_date = subscription.created_at
        end_date = None

        # For token subscriptions, end_date is None (unlimited until tokens exhausted)
        if subscription.access_type == AccessPath.TOKEN:
            end_date = None
        else:
            # Calculate end date if overall_sub_days is available
            if (
                subscription.overall_sub_days
                and subscription.overall_sub_days.isdigit()
            ):
                try:
                    end_date = start_date + timedelta(
                        days=int(subscription.overall_sub_days)
                    )
                except ValueError:
                    end_date = None

        # Prepare subscription details
        subscription_details = {
            "id": subscription.id,
            "start_date": start_date,
            "end_date": end_date,
            "plan": subscription.plan.name if subscription.plan else None,
            "access_type": subscription.access_type,
        }

        # Add price information if plan has a price attribute
        if subscription.plan and hasattr(subscription.plan, "price"):
            subscription_details["price"] = subscription.plan.price

        return subscription_details

    def _get_token_details(self, subscription):
        """Extract token-specific details for token-based subscriptions."""
        if subscription.access_type != AccessPath.TOKEN:
            return None

        # Calculate token usage percentage
        total_tokens = (
            subscription.token_previous_balance + subscription.token_current_balance
        )
        if total_tokens > 0:
            usage_percentage = (
                (total_tokens - subscription.token_current_balance) / total_tokens
            ) * 100
        else:
            usage_percentage = 0

        # Determine token status message
        status_messages = {
            TokenStatus.ACTIVE: "Token balance is sufficient for continued access",
            TokenStatus.INSUFFICIENT: "Token balance is low or insufficient - please top up",
            TokenStatus.INACTIVE: "Token subscription is inactive - payment required",
        }

        token_details = {
            "current_balance": float(subscription.token_current_balance),
            "previous_balance": float(subscription.token_previous_balance),
            "token_status": subscription.token_status,
            "token_status_display": dict(TokenStatus.CHOICES).get(
                subscription.token_status, "Unknown"
            ),
            "status_message": status_messages.get(
                subscription.token_status, "Unknown status"
            ),
            "usage_percentage": round(usage_percentage, 2),
            "is_low_balance": subscription.token_current_balance
            < 10,  # Warning threshold
            "is_critical_balance": subscription.token_current_balance
            < 5,  # Critical threshold
            "access_unlimited": True,  # Token subscriptions provide unlimited time access
            "payment_required": subscription.token_status == TokenStatus.INACTIVE,
        }

        return token_details

    def _get_modules_by_access_type(self, subscriptions):
        """Group modules by their access types (PAID, TRIAL, BYPASS)."""
        # Initialize result structure with valid access types
        modules_by_access = {
            AccessPath.PAID: [],
            AccessPath.TRIAL: [],
            AccessPath.BYPASS: [],
            AccessPath.TOKEN: [],
        }

        # Get all module subscriptions related to the company subscriptions
        for subscription in subscriptions:
            # Ensure we're dealing with a valid access_type
            access_type = subscription.access_type
            if access_type not in [
                AccessPath.PAID,
                AccessPath.TRIAL,
                AccessPath.BYPASS,
                AccessPath.TOKEN,
            ]:
                # Skip if access_type is not valid or handle accordingly
                continue

            # Get module subscriptions for this company subscription
            module_subscriptions = ModuleSubscription.objects.filter(
                company_subscription=subscription
            ).select_related("module", "plan")

            # Process each module subscription
            for mod in module_subscriptions:
                module_info = self._create_module_info(mod)
                modules_by_access[access_type].append(module_info)

        return modules_by_access

    def _create_module_info(self, module_subscription):
        """Create module information dictionary with all required fields."""
        access_type = module_subscription.company_subscription.access_type

        module_info = {
            "module_id": module_subscription.module.id,
            "module_name": module_subscription.module.name,
            "plan": module_subscription.plan.name if module_subscription.plan else None,
            "status": module_subscription.status,
            "days_remaining": module_subscription.days_remaining(),
            "is_active": module_subscription.is_active,
            "start_date": module_subscription.start_date,
            "end_date": module_subscription.end_date,
            "access_type": access_type,
        }

        if access_type == AccessPath.TOKEN:
            # For token subscriptions, access is unlimited until tokens are exhausted
            module_info["days_remaining"] = None  # Unlimited
            module_info["access_duration"] = "unlimited"
            module_info["expires_on_token_exhaustion"] = True
        else:
            # For other subscription types, calculate days remaining
            module_info["days_remaining"] = module_subscription.days_remaining()
            module_info["access_duration"] = "limited"
            module_info["expires_on_token_exhaustion"] = False

        # Add price information if available
        if module_subscription.plan and hasattr(module_subscription.plan, "price"):
            module_info["price"] = module_subscription.plan.price

        elif access_type == AccessPath.TOKEN:
            # For token subscriptions, show token-based pricing
            module_info["price_model"] = "token_based"
            module_info["price_per_token"] = 10  # From TOKEN_PRICE_PER_UNIT constant

        return module_info


class UpLoadMediaAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request, format=None):
        uploaded_media = request.FILES.get("file")

        if uploaded_media:
            file_url = models.UploadedMedia.objects.create(
                uploaded_by=request.user, media=uploaded_media
            ).media.url
            return CustomResponse(
                data={"message": "File uploaded successfully.", "file_url": file_url},
                status_code=200,
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": "No media file provided."}, status=400)
