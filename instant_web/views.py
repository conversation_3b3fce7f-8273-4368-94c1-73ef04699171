from datetime import datetime, timedelta
from dateutil.parser import parse as parse_date
from django.db.models import F, Q, Sum, Case, When, Count
from django.db.models.functions import TruncMonth
from django.http import Http404, HttpResponseRedirect
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.filters import SearchFilter
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from cart_management.models import Order, Buyer, OrderProduct
from cart_management.serializers import BuyerSerializer
from cart_management.views import CustomDashboardPagination
from core.auth.custom_auth import CustomUserAuthentication
from helpers.custom_response import Response as CustomResponse
from helpers.custom_permissions import (
    BranchRequiredPermission,
    CompanyRequiredPermission,
)
from helpers.reusable_functions import (
    Paginator,
    is_valid_string,
    is_valid_uuid,
)
from instant_web import models, serializers
from instant_web.models import InstantWeb, QRCode, Table
from instant_web.serializers import (
    CreateTableSerializer,
    InstantWebRetrieveSerializer,
    TableSerializer,
    TransactionAnalysisSerializer,
    UpdateTableSerializer,
)
from requisition.models import Company
from sales_app.models import ReturnRefund, SalesTransaction
from stock_inventory.models import (
    Branch,
    Category,
    Product,
    StockDetail,
)


# Create your view(s) here.
class InstantWebAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = serializers.InstantWebSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company = serializer.validated_data.get("company")
        branch = serializer.validated_data.get("branch")
        store_description = serializer.validated_data.get("store_description")
        store_url = serializer.validated_data.get("store_url")

        instant_web_store = models.InstantWeb.objects.filter(
            company=company, is_deleted=False
        )
        if branch is not None:
            instant_web_store = instant_web_store.filter(branch=branch)
            if instant_web_store.exists():
                return Response(
                    {"message": f"store already exists for this branch: {branch.name}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        if instant_web_store.exists():
            return Response(
                {
                    "message": f"store already exists for this company: {company.company_name}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        instant_web_store = models.InstantWeb.objects.create(
            company=company,
            branch=branch,
            store_description=store_description,
            store_url=store_url,
            created_by=request.user,
        )

        instant_web_store_serializer = serializers.InstantWebSerializer(
            instant_web_store
        )

        return Response(
            {
                "message": "store created successfully",
                "store": instant_web_store_serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class FetchInstantWebByDomainAPIView(RetrieveAPIView):
    queryset = InstantWeb.objects.all()

    def get(self, request, *args, **kwargs):
        instant_web_url = request.GET.get("instant_web_url", "").lower()

        if not instant_web_url:
            return Response(
                {"error": "instant_web_url parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        instant_web = self.queryset.filter(store_url__icontains=instant_web_url).first()

        if not instant_web:
            return Response(
                {"error": "InstantWeb object not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        return Response(
            {
                "company": instant_web.company.company_name,
                "company_id": instant_web.company.id,
                "branch": instant_web.branch.name if instant_web.branch else None,
                "store_id": instant_web.id,
                "store_brand_color": instant_web.store_brand_color,
                "store_description": instant_web.store_description,
                "header_description": instant_web.header_description,
                "header_images": instant_web.header_images,
                "header_logos": instant_web.header_logos,
                "header_logo_text": instant_web.header_logo_text,
                "navigation_visible": instant_web.navigation_visible,
                "navigation_set_menu": [
                    {"id": category.id, "name": category.name}
                    for category in instant_web.navigation_set_menu.all()
                ],
                "navigation_alignment": instant_web.navigation_alignment,
                "logo_alignment": instant_web.logo_alignment,
                "header_banner_type": instant_web.header_banner_type,
                "banner_color": instant_web.banner_color,
                "contact_visible": instant_web.contact_visible,
                "contact_phone_number": instant_web.contact_phone_number,
                "whatsapp_phone_number": instant_web.whatsapp_phone_number,
                "x_link": instant_web.x_link,
                "instagram_link": instant_web.instagram_link,
                "facebook_link": instant_web.facebook_link,
                "contact_email": instant_web.contact_email,
                "contact_address": instant_web.contact_address,
                "contact_description": instant_web.contact_description,
                "redirect_phone_number": instant_web.redirect_phone_number,
                "interface_theme": instant_web.interface_theme,
                "order_completion_message": instant_web.order_completion_message,
                "redirect_after_payment_url": instant_web.redirect_after_payment_url,
                "success_message": instant_web.success_message,
                "store_url": instant_web.store_url,
                "notification": instant_web.notification,
                "whatsapp_url": instant_web.whatsapp_url,
            }
        )


class ViewInstantWebAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company = request.GET.get("company_id")
        branch = request.GET.get("branch_id")

        if not is_valid_uuid(company):
            return Response(
                {"error": f"Invalid company ID was supplied: {company}"}, status=400
            )

        if branch and not is_valid_uuid(branch):
            return Response(
                {"error": f"Invalid branch ID was supplied: {branch}"}, status=400
            )

        if branch:
            instant_web_store = InstantWeb.objects.filter(
                company_id=company, branch_id=branch
            ).first()
        else:
            instant_web_store = InstantWeb.objects.filter(
                company_id=company, branch_id__isnull=True
            ).first()

        if instant_web_store is None:
            if branch:
                return Response(
                    {"message": f"No store found for this company and branch"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            else:
                return Response(
                    {"message": f"No store found for this company"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        serializer = serializers.ViewInstantWebStoreSerializer(instant_web_store)
        return Response(serializer.data, status=status.HTTP_200_OK)


class EditStoreAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request):
        instant_web = request.GET.get("id")
        try:
            instant_web_store = InstantWeb.objects.get(id=instant_web)
        except InstantWeb.DoesNotExist:
            return Response(
                {"message": "Instant web not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if "store_url" in request.data:
            return Response(
                {"message": "You cannot change your store_url"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Transform the navigation_set_menu to extract only IDs
        if "navigation_set_menu" in request.data:
            try:
                request.data["navigation_set_menu"] = [
                    item["id"] for item in request.data["navigation_set_menu"]
                ]
            except KeyError:
                return Response(
                    {"message": "Invalid data format for navigation_set_menu"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        serializer = serializers.EditInstantWebStoreSerializer(
            instant_web_store, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {
                "message": "Store details updated successfully",
                "store": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class BuyersByCompanyView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        company_id = request.query_params.get("company_id")
        branch_id = request.query_params.get("branch_id")

        if not company_id:
            return Response({"error": "company_id is required"}, status=400)

        if not is_valid_uuid(company_id):
            return Response({"error": f"Invalid company_id: {company_id}"}, status=400)

        if branch_id and not is_valid_uuid(branch_id):
            return Response({"error": f"Invalid branch_id: {branch_id}"}, status=400)

        orders = Order.objects.filter(company_id=company_id)
        if branch_id:
            orders = orders.filter(branch_id=branch_id)

        buyer_ids = orders.values_list("buyer_id", flat=True).distinct()
        buyers = Buyer.objects.filter(id__in=buyer_ids)

        total_count = buyers.count()

        today = timezone.now().date()
        new_buyers = buyers.filter(created_at__date=today)
        new_buyers_count = new_buyers.count()
        new_buyers_data = BuyerSerializer(new_buyers, many=True).data

        five_days_ago = today - timedelta(days=5)
        recent_orders = Order.objects.filter(
            company_id=company_id, buyer_id__in=buyer_ids, order_date__gte=five_days_ago
        )
        active_buyer_ids = recent_orders.values_list("buyer_id", flat=True).distinct()
        active_buyers = Buyer.objects.filter(id__in=active_buyer_ids)
        active_buyers_count = active_buyers.count()
        active_buyers_data = BuyerSerializer(active_buyers, many=True).data

        return_orders = Order.objects.filter(
            company_id=company_id, buyer_id__in=buyer_ids, order_date=today
        )
        return_buyer_ids = return_orders.values_list("buyer_id", flat=True).distinct()
        return_buyers = Buyer.objects.filter(id__in=return_buyer_ids)
        return_buyers_count = return_buyers.count()
        return_buyers_data = BuyerSerializer(return_buyers, many=True).data

        all_buyers_data = BuyerSerializer(buyers, many=True).data

        response_data = {
            "total_count": total_count,
            "new_buyers_count": new_buyers_count,
            "new_buyers": new_buyers_data,
            "active_buyers_count": active_buyers_count,
            "active_buyers": active_buyers_data,
            "return_buyers_count": return_buyers_count,
            "return_buyers": return_buyers_data,
            "buyers": all_buyers_data,
        }

        paginator = CustomDashboardPagination()
        paginated_data = paginator.paginate_queryset(response_data["buyers"], request)
        response_data["buyers"] = paginated_data

        return paginator.get_paginated_response(response_data)


class DeleteInstantWebAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def delete(self, request):
        company = request.GET.get("company_id")
        branch = request.GET.get("branch_id")

        if not is_valid_uuid(company):
            return Response(
                {"error": f"Invalid company ID was supplied: {company}"}, status=400
            )

        if branch and not is_valid_uuid(branch):
            return Response(
                {"error": f"Invalid branch ID was supplied: {branch}"}, status=400
            )

        if branch:
            instant_web_store = InstantWeb.objects.filter(
                company_id=company, branch_id=branch
            ).first()
            if instant_web_store is None:
                return Response(
                    {"message": f"No store found for this company and branch"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            instant_web_store = InstantWeb.objects.filter(
                company_id=company, branch_id__isnull=True
            ).first()
            if instant_web_store is None:
                return Response(
                    {"message": f"No store found for this company"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        instant_web_store.delete()
        return Response(
            {"message": "Instant web store deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )


class CompanyInstantWebsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    paginator = CustomDashboardPagination()

    def get(self, request):
        company_id = request.GET.get("company_id")
        branch_name = request.GET.get("branch_name")

        if not is_valid_uuid(company_id):
            return Response(
                {"error": f"Invalid company ID was supplied: {company_id}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {"detail": "Company not found."}, status=status.HTTP_404_NOT_FOUND
            )

        instant_webs = InstantWeb.objects.filter(company=company)

        if branch_name:
            branch_name = branch_name.lower()
            instant_webs = instant_webs.filter(branch__name__istartswith=branch_name)

        page = self.paginator.paginate_queryset(instant_webs, request)
        if page is not None:
            serializer = InstantWebRetrieveSerializer(page, many=True)
            return self.paginator.get_paginated_response(serializer.data)

        serializer = InstantWebRetrieveSerializer(instant_webs, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class CompanyAvailableStockAPIView(APIView):
    permission_classes = [CompanyRequiredPermission]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company_id")
        branch_id = request.query_params.get("branch_id")
        category_id = request.query_params.get("category_id")
        product_id = request.query_params.get("item_id")
        name_search = request.query_params.get("search")

        company_stock_details = StockDetail.objects.filter(
            company=company_id,
            item__is_active=True,
        )
        if branch_id:
            if (
                not is_valid_uuid(branch_id)
                or Branch.retrieve_company_branch(id=branch_id, company=company_id)
                is None
            ):
                return CustomResponse(
                    errors={"message": "provide a valid branch ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            company_stock_details = company_stock_details.filter(branch=branch_id)
        if name_search and is_valid_string(name_search):
            company_stock_details = company_stock_details.filter(
                Q(item__name__icontains=name_search)
                | Q(category__name__icontains=name_search)
            )
        if category_id:
            if (
                not is_valid_uuid(category_id)
                or not Category.objects.filter(id=category_id).exists()
            ):
                return CustomResponse(
                    errors={"message": "provide a valid category ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            company_stock_details = company_stock_details.filter(category=category_id)
        if product_id:
            if (
                not is_valid_uuid(product_id)
                or not Product.objects.filter(id=product_id).exists()
            ):
                return CustomResponse(
                    errors={"message": "provide a valid product ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            company_stock_details = company_stock_details.filter(item=product_id)
        paginator = CustomDashboardPagination()
        paginated_data = paginator.paginate_queryset(
            request=request, queryset=company_stock_details
        )
        serializer = serializers.CompanyAvailableStockSerializer(
            instance=paginated_data, many=True
        )
        return paginator.get_paginated_response(serializer.data)


class CompanyAvailableCategoriesAPIView(APIView):
    permission_classes = [CompanyRequiredPermission]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company_id")
        category_id = request.query_params.get("category_id")
        search = request.query_params.get("search")

        available_categories = Category.objects.filter(company=company_id)
        if search and is_valid_string(search):
            available_categories = available_categories.filter(
                name__icontains=search
            ).order_by("name")
        if category_id:
            if (
                not is_valid_uuid(category_id)
                or not available_categories.filter(id=category_id).exists()
            ):
                return CustomResponse(
                    errors={"message": "provide a valid category ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            available_categories = available_categories.filter(id=category_id)
        paginated_data = Paginator.paginate(
            request=request, queryset=available_categories
        )
        serializer = serializers.CompanyCategoriesSerializer(
            instance=paginated_data, many=True
        )
        data = {
            "message": "successfully fetched categories.",
            "categories": serializer.data,
            "count": len(serializer.data),
            "total_categories": available_categories.count(),
        }
        return CustomResponse(data=data, status_code=200, status=status.HTTP_200_OK)


class SalesAndStockSummaryCardsDataAPIView(APIView):
    permission_classes = [CompanyRequiredPermission]

    """
    View to retrieve the summary of total products sold, total amount sold,
    products in stock, inventory value, and products out of stock for a specific company and branch.
    """

    def get(self, request):
        """
        Handles GET request to retrieve the sales and stock summary filtered by company and branch.
        """

        company_id = request.query_params.get("company_id")
        branch_id = request.query_params.get("branch_id")

        if not company_id or not branch_id:
            return Response(
                {"error": "Both company_id and branch_id are required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not Company.objects.filter(id=company_id).exists():
            return Response(
                {"error": "Invalid company ID was supplied."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not Branch.objects.filter(id=branch_id).exists():
            return Response(
                {"error": "Invalid branch ID was supplied."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Total sales data for products sold and amount sold
            total_sales = OrderProduct.objects.filter(
                orders__status="closed",
                orders__company_id=company_id,
                orders__branch_id=branch_id,
            ).aggregate(
                total_products_sold=Sum("quantity"), total_amount_sold=Sum("sub_total")
            )

            total_products_sold_count = total_sales.get("total_products_sold", 0) or 0
            total_amount_sold_value = total_sales.get("total_amount_sold", 0) or 0

            # Products in stock count and inventory value
            products_in_stock = StockDetail.objects.filter(
                company_id=company_id,
                branch_id=branch_id,
                quantity__gt=F("out_of_stock_threshold"),
            )
            products_in_stock_count = products_in_stock.count()
            inventory_value = (
                products_in_stock.aggregate(
                    total_value=Sum(F("quantity") * F("stock_price"))
                )["total_value"]
                or 0
            )

            # Products out of stock count and value
            products_out_of_stock = StockDetail.objects.filter(
                company_id=company_id,
                branch_id=branch_id,
                quantity__lte=F("out_of_stock_threshold"),
            )
            products_out_of_stock_count = products_out_of_stock.count()
            products_out_of_stock_value = (
                products_out_of_stock.aggregate(
                    out_of_stock_value=Sum(F("quantity") * F("stock_price"))
                )["out_of_stock_value"]
                or 0
            )

            response_data = {
                "counts_and_values": {
                    "total_products_sold": {
                        "count": total_products_sold_count,
                        "value": total_amount_sold_value,
                    },
                    "total_amount_sold": {
                        "count": total_products_sold_count,
                        "value": total_amount_sold_value,
                    },
                    "products_in_stock": {
                        "count": products_in_stock_count,
                        "value": inventory_value,
                    },
                    "inventory_value": {
                        "count": products_in_stock_count,
                        "value": inventory_value,
                    },
                    "products_out_of_stock": {
                        "count": products_out_of_stock_count,
                        "value": products_out_of_stock_value,
                    },
                }
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            print("Error:", str(e))
            return Response(
                {
                    "error": "An error occurred while retrieving the sales and stock summary."
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetInstantWebCategoriesByBranch(APIView):

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, branch_id):
        try:
            # Retrieve the branch
            branch = Branch.objects.get(id=branch_id)

            # Get the associated InstantWeb object
            instant_web = InstantWeb.objects.filter(branch=branch).first()

            if instant_web is None:
                return Response(
                    {"detail": "No Instant web found for this branch."},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get the categories associated with the InstantWeb object
            categories = instant_web.navigation_set_menu.all()

            # Serialize the categories
            serializer = serializers.CompanyCategoriesSerializer(categories, many=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Branch.DoesNotExist:
            return Response(
                {"detail": "Branch not found."}, status=status.HTTP_404_NOT_FOUND
            )


class CompanyItemDetailsAPIView(APIView):
    permission_classes = [CompanyRequiredPermission]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company_id")
        item_id = request.query_params.get("item_id")
        name_search = request.query_params.get("search")

        company_items = Product.objects.filter(company=company_id)
        if name_search and is_valid_string(name_search):
            company_items = company_items.filter(name__icontains=name_search)
        if item_id:
            if (
                not is_valid_uuid(item_id)
                or not company_items.filter(id=item_id).exists()
            ):
                return CustomResponse(
                    errors={"message": "provide a valid item ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            company_items = company_items.filter(id=item_id)
        paginated_data = Paginator.paginate(request=request, queryset=company_items)
        serializer = serializers.CompanyProductSerializer(
            instance=paginated_data, many=True
        )
        data = {
            "message": "successfully fetched product(s).",
            "items": serializer.data,
            "count": len(serializer.data),
            "total_items": company_items.count(),
        }
        return CustomResponse(data=data, status_code=200, status=status.HTTP_200_OK)


class CreateQRCodeView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = serializers.CreateQRCodeSerializer(data=request.data)
        if serializer.is_valid():
            try:
                qr_code = serializer.save()
                qr_code.generate_qr_code()
            except ValidationError as e:
                return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return Response(
                    {"detail": "An unexpected error occurred. Please try again later."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            return Response(
                {"code_url": qr_code.code_url}, status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ScanQRCodeView(APIView):
    def get(self, request, pk, format=None):
        # Try to retrieve a QRCode with the given pk
        try:
            qr_code = QRCode.objects.get(pk=pk)
            # Increment the scan count and save
            qr_code.scans += 1
            qr_code.save()
            # Redirect to the link associated with the QRCode
            return HttpResponseRedirect(qr_code.link)

        except QRCode.DoesNotExist:
            pass

        # If QRCode was not found, try to retrieve a Table with the given pk
        try:
            table = Table.objects.get(pk=pk)
            # Increment the scan count and save
            table.scans += 1
            table.save()
            # Redirect to the link associated with the Table
            return HttpResponseRedirect(table.link)

        except Table.DoesNotExist:
            pass

        # If neither QRCode nor Table was found, return a 404 error
        return Response(
            {"error": "Invalid request"}, status=status.HTTP_400_BAD_REQUEST
        )


class QRCodeListView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["branch", "categories", "scans", "all_categories"]
    search_fields = ["name", "branch__name", "categories__name"]
    pagination_class = CustomDashboardPagination

    def get(self, request):
        company_id = request.query_params.get("company")
        if not company_id:
            return Response(
                {"error": "company_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        qr_codes = QRCode.objects.filter(company_id=company_id)

        # Apply filters
        for backend in self.filter_backends:
            qr_codes = backend().filter_queryset(self.request, qr_codes, self)

        # Calculate totals
        total_qr_codes = qr_codes.count()
        total_scans = qr_codes.aggregate(total_scans=Sum("scans"))["total_scans"] or 0

        # Apply pagination
        paginator = CustomDashboardPagination()
        page = paginator.paginate_queryset(qr_codes, request)
        if page is not None:
            serializer = serializers.QRCodeSerializer(page, many=True)
            paginated_response = paginator.get_paginated_response(serializer.data)
            paginated_response.data["total_qr_codes"] = total_qr_codes
            paginated_response.data["total_scans"] = total_scans
            return paginated_response

        serializer = serializers.QRCodeSerializer(qr_codes, many=True)
        response_data = {
            "results": serializer.data,
            "total_qr_codes": total_qr_codes,
            "total_scans": total_scans,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class QRCodeDetailView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        try:
            return QRCode.objects.get(id=pk)
        except QRCode.DoesNotExist:
            return None

    def put(self, request, pk):
        qr_code = self.get_object(pk)
        if qr_code is None:
            return Response("Invalid QR Code ID", status=status.HTTP_400_BAD_REQUEST)

        serializer = serializers.QRCodeSerializer(qr_code, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk):
        qr_code = self.get_object(pk)
        if qr_code is None:
            return Response("Invalid QR Code ID", status=status.HTTP_400_BAD_REQUEST)

        serializer = serializers.QRCodeSerializer(
            qr_code, data=request.data, partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        qr_code = self.get_object(pk)
        if qr_code is None:
            return Response("Invalid QR Code ID", status=status.HTTP_400_BAD_REQUEST)

        qr_code.delete()
        return Response(
            {"message": "QR code successfully deleted"}, status=status.HTTP_200_OK
        )


class CreateTableView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        tables_data = request.data.get("tables", [])

        if not tables_data:
            return Response(
                {"detail": "No table data provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        created_tables = []
        for table_data in tables_data:
            serializer = CreateTableSerializer(data=table_data)
            if serializer.is_valid():
                table = serializer.save()
                table.generate_table_qr_code()
                created_tables.append(TableSerializer(table).data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        return Response(created_tables, status=status.HTTP_201_CREATED)


class TableListView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["branch", "categories", "scans", "all_categories"]
    search_fields = ["name", "branch__name", "categories__name"]
    pagination_class = CustomDashboardPagination

    def get(self, request):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")

        if not company_id:
            return Response(
                {"error": "company_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        if not is_valid_uuid(company_id):
            raise ValidationError({"message": "enter a valid company id"})

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {"error": "Invalid company ID"}, status=status.HTTP_404_NOT_FOUND
            )

        tables = Table.objects.filter(company=company_id)

        # Apply filters
        for backend in self.filter_backends:
            tables = backend().filter_queryset(self.request, tables, self)

        # Calculate totals
        total_tables = tables.count()
        total_scans = tables.aggregate(total_scans=Sum("scans"))["total_scans"] or 0

        # Apply pagination
        paginator = CustomDashboardPagination()
        page = paginator.paginate_queryset(tables, request)
        if page is not None:
            serializer = TableSerializer(page, many=True)
            paginated_response = paginator.get_paginated_response(serializer.data)
            paginated_response.data["total_tables"] = total_tables
            paginated_response.data["total_scans"] = total_scans
            return paginated_response

        serializer = TableSerializer(tables, many=True)
        response_data = {
            "results": serializer.data,
            "total_tables": total_tables,
            "total_scans": total_scans,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class TableDetailView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        try:
            return Table.objects.get(id=pk)
        except Table.DoesNotExist:
            raise NotFound({"error": "Invalid table ID"})

    def get(self, request, pk):
        table = self.get_object(pk)
        serializer = TableSerializer(table)
        return Response(serializer.data)

    def put(self, request, pk):
        table = self.get_object(pk)
        serializer = UpdateTableSerializer(table, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk):
        table = self.get_object(pk)
        serializer = UpdateTableSerializer(table, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        table = self.get_object(pk)
        table.delete()
        return Response(
            {"message": "Table successfully deleted"}, status=status.HTTP_200_OK
        )


class AnalyticsView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    """
    AnalyticsView provides an overview of the company's product and sales performance.
    It aggregates data such as total products, total sold, amount sold, total returned,
    total stock inventory, total stock value, and lists the top and least selling products.
    The view can be filtered by company ID, device type, and date range.
    """

    def get(self, request):
        # Retrieve query parameters
        company_id = request.query_params.get("company_id")
        device = request.query_params.get("device")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        # Ensure company_id parameter is provided
        if not company_id:
            return Response({"error": "company_id parameter is required"}, status=400)

        # Validate the existence of the company
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise NotFound("Company not found")

        # Filter sales transactions by company and optionally by device type and date range
        sales_transactions = SalesTransaction.objects.filter(company=company)
        if device:
            sales_transactions = sales_transactions.filter(device=device)
        if start_date and end_date:
            try:
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
                end_date = datetime.strptime(end_date, "%Y-%m-%d")
                sales_transactions = sales_transactions.filter(
                    created_at__range=[start_date, end_date]
                )
            except ValueError:
                return Response(
                    {"error": "Invalid date format. Use YYYY-MM-DD."}, status=400
                )

        # Calculate the total number of products for the company
        total_products = Product.objects.filter(company=company).count()

        # Calculate the total quantity of products sold
        total_sold = (
            sales_transactions.aggregate(total=Sum("salestransactionitem__quantity"))[
                "total"
            ]
            or 0
        )

        # Calculate the total amount of sales
        amount_sold = (
            sales_transactions.aggregate(total=Sum("total_sales_amount"))["total"] or 0
        )

        # Calculate the total number of returned transactions
        total_returned = ReturnRefund.objects.filter(company=company)
        if start_date and end_date:
            total_returned = total_returned.filter(
                created_at__range=[start_date, end_date]
            )
        total_returned = total_returned.count()

        # Calculate the total stock inventory
        total_stock_inventory = (
            Product.objects.filter(company=company).aggregate(
                total=Sum("stockdetail__quantity")
            )["total"]
            or 0
        )

        # Calculate the total stock value by multiplying stock by product price
        total_stock_value = (
            Product.objects.filter(company=company)
            .annotate(stock_value=Sum("stockdetail__quantity") * F("product_price"))
            .aggregate(total=Sum("stock_value"))["total"]
            or 0
        )

        # Retrieve the top 5 selling products
        top_selling_products = (
            sales_transactions.values("salestransactionitem__item__name")
            .annotate(total_sold=Sum("salestransactionitem__quantity"))
            .order_by("-total_sold")[:5]
        )

        # Retrieve the least 5 selling products
        least_selling_products = (
            sales_transactions.values("salestransactionitem__item__name")
            .annotate(total_sold=Sum("salestransactionitem__quantity"))
            .order_by("total_sold")[:5]
        )

        # Prepare the response data
        data = {
            "total_products": total_products,
            "total_sold": total_sold,
            "amount_sold": amount_sold,
            "total_returned": total_returned,
            "total_stock_inventory": total_stock_inventory,
            "total_stock_value": total_stock_value,
            "top_selling_products": top_selling_products,
            "least_selling_products": least_selling_products,
        }

        # Return the aggregated analytics data
        return Response(data)


class TransactionAnalysisView(APIView):

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    """
    API view to provide transaction analysis for a given company within a specified date range.
    The analysis includes total transactions, transaction volume, online/offline transactions,
    monthly transactions, transaction sources, gross profit, and net profit.
    """

    def get(self, request, *args, **kwargs):
        # Get query parameters
        company_id = request.query_params.get("company_id")
        start_date_str = request.query_params.get("start_date")
        end_date_str = request.query_params.get("end_date")
        period = request.query_params.get("period")  # "week" or "month"

        # Validate company_id
        if not company_id:
            raise ValidationError("company_id is required")

        # Parse the start and end date from string to date object if provided
        start_date = parse_date(start_date_str) if start_date_str else None
        end_date = parse_date(end_date_str) if end_date_str else None

        # Calculate current week range
        today = datetime.now()
        current_week_start = today - timedelta(days=today.weekday())
        current_week_end = current_week_start + timedelta(days=6)

        # Calculate current month range
        current_month_start = today.replace(day=1)
        next_month_start = (today.replace(day=28) + timedelta(days=4)).replace(day=1)
        current_month_end = next_month_start - timedelta(days=1)

        # Filter transactions based on company_id and date range if provided
        transactions = SalesTransaction.objects.filter(company_id=company_id)
        if start_date and end_date:
            transactions = transactions.filter(created_at__range=[start_date, end_date])
        elif start_date:
            transactions = transactions.filter(created_at__gte=start_date)
        elif end_date:
            transactions = transactions.filter(created_at__lte=end_date)

        # Additional filter for "this week" or "this month"
        if period == "week":
            transactions = transactions.filter(
                created_at__range=[current_week_start, current_week_end]
            )
        elif period == "month":
            transactions = transactions.filter(
                created_at__range=[current_month_start, current_month_end]
            )

        # Calculate total number of transactions
        total_transactions = transactions.count()

        # Calculate total transaction volume (sum of total_sales_amount)
        total_transaction_volume = (
            transactions.aggregate(Sum("total_sales_amount"))["total_sales_amount__sum"]
            or 0
        )

        # Calculate total online transactions volume (sum of total_sales_amount for online transactions)
        total_online = (
            transactions.filter(device="online").aggregate(Sum("total_sales_amount"))[
                "total_sales_amount__sum"
            ]
            or 0
        )

        # Calculate total offline transactions volume (sum of total_sales_amount for offline transactions)
        total_offline = (
            transactions.filter(device="offline").aggregate(Sum("total_sales_amount"))[
                "total_sales_amount__sum"
            ]
            or 0
        )

        # Calculate monthly transactions (sum of total_sales_amount for each month)
        transactions_by_month = (
            transactions.annotate(month=TruncMonth("created_at"))
            .values("month")
            .annotate(total=Sum("total_sales_amount"))
            .order_by("month")
        )
        transactions_by_month = {
            t["month"].strftime("%b"): t["total"] for t in transactions_by_month
        }

        # Calculate transaction volume by payment source (sum of total_sales_amount for each payment source)
        transaction_source = transactions.values("means_of_payment").annotate(
            total=Sum("total_sales_amount")
        )
        transaction_source = {
            t["means_of_payment"]: t["total"] for t in transaction_source
        }

        # Calculate gross profit and net profit by month
        monthly_gross_profit = (
            transactions.annotate(month=TruncMonth("created_at"))
            .values("month")
            .annotate(gross_profit=Sum("total_sales_amount"))
            .order_by("month")
        )

        monthly_net_profit = (
            transactions.annotate(month=TruncMonth("created_at"))
            .values("month")
            .annotate(net_profit=Sum("total_cost"))
            .order_by("month")
        )

        gross_profit = {
            t["month"].strftime("%b"): t["gross_profit"] for t in monthly_gross_profit
        }
        net_profit = {
            t["month"].strftime("%b"): t["net_profit"] for t in monthly_net_profit
        }

        # Prepare data for response
        data = {
            "total_transactions": total_transactions,
            "total_transaction_volume": total_transaction_volume,
            "total_online": total_online,
            "total_offline": total_offline,
            "transactions_by_month": transactions_by_month,
            "transaction_source": transaction_source,
            "gross_profit": gross_profit,
            "net_profit": net_profit,
        }

        # Serialize the data
        serializer = TransactionAnalysisSerializer(data)

        # Return the response
        return Response(serializer.data)
