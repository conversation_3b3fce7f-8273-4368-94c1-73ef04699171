from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import check_password
from django.utils import timezone
from rest_framework import serializers

from account.enums import ScheduleTypes
from account.models import (
    AccountSystem,
    Beneficiary,
    BulkTransfer,
    BulkTransferItem,
    DebitCreditRecordOnAccount,
    Transaction,
    Wallet,
)
from core.helpers.apis.request_cls import LibertyPayPlus
from core.models import KycDetails
from datetime import timedelta
from requisition.models import Company


User = get_user_model()


class ReceivingTransListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "status",
            "transaction_type",
            "amount",
            "balance_before",
            "balance_after",
            "source_account_name",
            "source_buddy_number",
            "payroll_reference",
            "is_reversed",
        ]


class SendMoneySerializer(serializers.Serializer):
    amount = serializers.FloatField(min_value=10, required=True)
    bank_name = serializers.CharField(max_length=500, required=True)
    bank_code = serializers.CharField(max_length=20, required=True)
    account_number = serializers.CharField(max_length=10, required=True)
    account_name = serializers.CharField(max_length=500, required=True)
    narration = serializers.CharField(max_length=500, required=True)
    transaction_pin = serializers.CharField(max_length=4, min_length=4, required=True)


class VfdRequestSerializer(serializers.Serializer):
    request_data = serializers.CharField(max_length=500, required=True)


class CreatAccountSerializer(serializers.Serializer):
    bvn_no = serializers.CharField(max_length=11, min_length=11, required=True)

    def validate(self, attrs):
        bvn_no = attrs.get("bvn_no")
        request = self.context.get("request")
        user = request.user
        user_bvn_number = user.bvn_number

        if not user_bvn_number:
            raise serializers.ValidationError(
                {
                    "message": "No BVN associated with the provided user details. Please complete BVN registration."
                }
            )

        elif bvn_no != user_bvn_number:
            raise serializers.ValidationError(
                {
                    "message": "The provided BVN does not match the existing BVN on record for this user. Please "
                    "double-check and try again."
                }
            )

        users = User.objects.filter(bvn_number=bvn_no)
        if users.exists() and users.first() != user:
            raise serializers.ValidationError(
                {
                    "message": "The provided BVN belongs to another user. Please verify the BVN and try again with the "
                    "correct information."
                }
            )

        user_accounts = AccountSystem.objects.filter(user=user)
        if user_accounts.exists():
            raise serializers.ValidationError(
                {"message": "An account is already associated with the provided BVN."}
            )

        liberty_pay = LibertyPayPlus()
        kyc_details = liberty_pay.get_user_kyc_details(
            email=user.email, bvn_number=bvn_no
        )
        if kyc_details is None:
            raise serializers.ValidationError(
                {"message": "BVN details not found. Please complete BVN registration"}
            )

        attrs["kyc_details"] = kyc_details
        return attrs


class AdminCreatAccountSerializer(serializers.Serializer):
    bvn_no = serializers.CharField(max_length=11, min_length=11, required=True)

    def validate(self, attrs):
        bvn_no = attrs.get("bvn_no")
        # request = self.context.get('request')
        user_qs = User.objects.filter(bvn_number=bvn_no)

        if not user_qs.exists():
            raise serializers.ValidationError({"message": "User does not exist."})

        user = user_qs.first()
        print(user.email, "\n\n")
        user_accounts = AccountSystem.objects.filter(user=user)
        if user_accounts.exists():
            raise serializers.ValidationError(
                {"message": "An account is already associated with the provided BVN."}
            )

        kyc = KycDetails.objects.filter(user=user, verification_status="SUCCESSFUL")
        kyc_details = None

        if not kyc.exists():
            liberty_pay = LibertyPayPlus()
            kyc_details = liberty_pay.get_user_kyc_details(
                email=user.email, bvn_number=bvn_no
            )
            if kyc_details is None:
                raise serializers.ValidationError(
                    {
                        "message": "BVN details not found. Please complete BVN registration"
                    }
                )

        attrs["kyc_details"] = kyc_details
        attrs["user_id"] = user.id
        return attrs


class AdminCreatSpecificAccountSerializer(serializers.Serializer):
    bvn_no = serializers.CharField(max_length=11, min_length=11, required=True)
    acct_type = serializers.CharField(max_length=250, required=True)

    def validate(self, attrs):
        bvn_no = attrs.get("bvn_no")
        acct_type = attrs.get("acct_type")
        # request = self.context.get('request')

        accounts_type = ["PERSONAL_PAYROLL", "INSTANT_WAGE", "PERSONAL_SPEND_MGMT"]
        if acct_type not in accounts_type:
            raise serializers.ValidationError({"message": "Invalid account type."})

        user_qs = User.objects.filter(bvn_number=bvn_no)

        if not user_qs.exists():
            raise serializers.ValidationError({"message": "User does not exist."})

        user = user_qs.first()
        # print(user.email, "\n\n")
        user_accounts = AccountSystem.objects.filter(user=user, account_type=acct_type)
        if user_accounts.exists():
            raise serializers.ValidationError(
                {
                    "message": "An account is already associated with the provided BVN and account type."
                }
            )

        kyc = KycDetails.objects.filter(user=user)
        kyc_details = None

        if not kyc.exists():
            liberty_pay = LibertyPayPlus()
            kyc_details = liberty_pay.get_user_kyc_details(
                email=user.email, bvn_number=bvn_no
            )
            if kyc_details is None:
                raise serializers.ValidationError(
                    {
                        "message": "BVN details not found. Please complete BVN registration"
                    }
                )

        attrs["kyc_details"] = kyc_details
        attrs["user_id"] = user.id
        return attrs


class UpdateAcctSerializer(serializers.Serializer):
    account_no = serializers.CharField(max_length=10, min_length=10, required=True)
    bvn = serializers.CharField(max_length=11, min_length=11, required=True)
    nin = serializers.CharField(max_length=11, min_length=11, required=True)
    update_type = serializers.ChoiceField(choices=["NIN", "BVN"])

    def validate(self, data):
        account_number = data.get("account_no")
        account_qs = AccountSystem.objects.filter(account_number=account_number)

        if not account_qs.exists():
            raise serializers.ValidationError({"message": "Account does not exist."})
        return data


class AccountSystemDetailsSerializer(serializers.ModelSerializer):

    class Meta:
        model = AccountSystem
        fields = "__all__"

        fields = (
            "account_provider",
            "account_number",
            "account_name",
            "account_type",
            "bank_name",
            "bank_code",
            "created_at",
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        wallet_data = (
            Wallet.objects.filter(account=instance)
            .values(
                "id",
                "balance",
                "previous_balance",
                "wallet_type",
                "is_active",
            )
            .first()
        )
        account_company_nstance = instance.company
        if account_company_nstance is not None:
            company = {
                "company_id": account_company_nstance.id,
                "company_name": account_company_nstance.company_name,
                "company_wallet_type": account_company_nstance.company_wallet_type,
                "industry": account_company_nstance.industry,
            }
        else:
            company = None
        representation["wallet_data"] = wallet_data
        representation["company"] = company

        return representation


class WalletHistorySerializer(serializers.ModelSerializer):

    class Meta:
        model = DebitCreditRecordOnAccount
        fields = (
            "entry",
            "requisition_type",
            "balance_before",
            "balance_after",
            "amount",
            "date_credited",
            "last_updated",
            "date_created",
        )


class WalletTransactionHistorySerializer(serializers.ModelSerializer):

    class Meta:
        model = Transaction
        # fields = "__all__"
        fields = (
            "user",
            "transaction_ref",
            "amount",
            "debit_amount",
            "balance_before",
            "balance_after",
            "status",
            "transaction_type",
            "total_amount_received",
            "payout_type",
            "beneficiary_account_number",
            "beneficiary_account_name",
            "narration",
            "commission",
            "date_created"
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        representation["beneficiary_bank_name"] = instance.bank_name
        representation["from"] = instance.source_account_name
        source_account_number = (
            instance.source_account_number
            if instance.transaction_type != "BANK_TRANSFER"
            else "101*******"
        )
        representation["source_account_nuban"] = source_account_number
        representation["provider"] = "VFD"

        return representation


class BeneficiarySerializer(serializers.ModelSerializer):

    class Meta:
        model = Beneficiary
        fields = (
            "id",
            "beneficiary_name",
            "account_number",
            "bank_name",
            "bank_code",
            "is_active",
            "saved_as_beneficiary",
        )


class BulkTransferItemSerializer(serializers.ModelSerializer):
    benficiary = BeneficiarySerializer()

    class Meta:
        model = BulkTransferItem
        fields = "__all__"


from django.db.models import Sum, Count


class ScheduledBulkTransferItemsSerializer(serializers.ModelSerializer):
    transfer_items = serializers.SerializerMethodField()
    total_transferable_amount = (
        serializers.SerializerMethodField()
    )  # Total of all transfer amounts
    # total_transfer_count = (
    #     serializers.SerializerMethodField()
    # )  # Number of transfer items

    class Meta:
        model = BulkTransfer
        fields = "__all__"

    def get_transfer_items(self, instance):
        """Retrieve related BulkTransferItems with full details."""
        transfer_items = BulkTransferItem.objects.filter(bulk_transfer=instance)
        return BulkTransferItemSerializer(transfer_items, many=True).data

    def get_total_transferable_amount(self, instance):
        """Calculate and return the total transfer amount for this bulk transfer."""
        total_amount = BulkTransferItem.objects.filter(
            bulk_transfer=instance
        ).aggregate(total=Sum("amount"))["total"]
        return total_amount or 0  # Return 0 if no items

    # def get_total_transfer_count(self, instance):
    #     """Get the count of transfer items in this bulk transfer."""
    #     count = BulkTransferItem.objects.filter(bulk_transfer=instance).count()
    #     return count


class SingleBeneficiarySerializer(serializers.Serializer):
    beneficiary_name = serializers.CharField(required=True, allow_blank=False)
    account_number = serializers.CharField(required=True, allow_blank=False)
    bank_name = serializers.CharField(required=True, allow_blank=False)
    bank_code = serializers.CharField(required=True, allow_blank=False)
    narration = serializers.CharField(required=False, allow_blank=True)
    save_as_beneficiary = serializers.BooleanField(required=True)
    amount = serializers.FloatField(required=True)


class CreateBeneficiarySerializer(serializers.Serializer):
    beneficiaries = serializers.ListSerializer(
        child=SingleBeneficiarySerializer(), required=True
    )

    def validate(self, data):
        """
        Ensure no duplicate (account_number, bank_code) pairs exist in the request.
        """
        beneficiaries = data["beneficiaries"]
        unique_pairs = set()
        duplicates = set()

        for beneficiary in beneficiaries:
            pair = (beneficiary["account_number"], beneficiary["bank_code"])

            if pair in unique_pairs:
                duplicates.add(pair)  # Store duplicate for error message
            else:
                unique_pairs.add(pair)

        if duplicates:
            raise ValueError(
                f"Duplicate account_number & bank_code pairs found: {list(duplicates)}"
            )

        return data


class BulkTransferSerializer(serializers.Serializer):
    company_id = serializers.CharField(required=True)
    # account_type = serializers.CharField(required=True)
    transaction_pin = serializers.CharField(required=True)
    # beneficiaries = serializers.ListSerializer(
    #     child=serializers.DictField(), required=True
    # )
    beneficiaries = serializers.ListSerializer(
        child=SingleBeneficiarySerializer(), required=True
    )
    is_scheduled = serializers.BooleanField(
        default=False,
    )
    schedule_name = serializers.CharField(
        default=False, allow_blank=True, allow_null=True
    )
    schedule_type = serializers.ChoiceField(
        required=False, choices=ScheduleTypes.choices, allow_null=True
    )
    start_date = serializers.DateField(required=False, allow_null=True)
    end_date = serializers.DateField(required=False, allow_null=True)

    def validate(self, data):
        company_id = data.get("company_id")
        transaction_pin = data.get("transaction_pin")
        beneficiaries = data.get("beneficiaries")
        is_scheduled = data.get("is_scheduled")
        schedule_type = data.get("schedule_type")
        start_date = data.get("start_date")
        end_date = data.get("end_date")
        schedule_name = data.get("schedule_name")

        errors = {}
        if is_scheduled:
            if not schedule_type:
                errors["schedule_type"] = (
                    "This field is required when is_scheduled is True."
                )
            if not start_date:
                errors["start_date"] = (
                    "This field is required when is_scheduled is True."
                )
            if not end_date:
                errors["end_date"] = "This field is required when is_scheduled is True."
            if not schedule_name:
                errors["schedule_name"] = (
                    "This field is required when is_scheduled is True."
                )

        if errors:
            raise serializers.ValidationError(errors)

        # Validate company existence
        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise serializers.ValidationError({"message": "Company does not exist"})

        user = company_instance.user
        wallet_type = company_instance.company_wallet_type

        if not user.requisition_transaction_pin:
            raise serializers.ValidationError({"message": "Kindly set transaction"})

        # Validate transaction pin
        if not check_password(transaction_pin, user.requisition_transaction_pin):
            raise serializers.ValidationError({"message": "Incorrect transaction pin"})

        # Fetch all beneficiary IDs from request
        # beneficiary_ids = [b.get("id") for b in beneficiaries if "id" in b]

        if not beneficiaries:
            raise serializers.ValidationError({"message": "No beneficiaries provided"})

        ###############################################################################
        # Fetch existing beneficiaries in a single query
        # existing_beneficiaries = {
        #     str(b.id): b for b in Beneficiary.objects.filter(id__in=beneficiary_ids)
        # }

        # # Validate all beneficiaries exist & amounts > 100
        # for b in beneficiaries:
        #     beneficiary_id = str(b.get("id"))
        #     amount = b.get("amount")

        #     if beneficiary_id not in existing_beneficiaries:
        #         raise serializers.ValidationError(
        #             {"message": f"Beneficiary with ID {beneficiary_id} does not exist"}
        #         )

        #     if amount is None or not isinstance(amount, (int, float)) or amount <= 100:
        #         raise serializers.ValidationError(
        #             {
        #                 "message": f"Invalid amount for beneficiary {beneficiary_id}. Amount must be greater than 100"
        #             }
        #         )
        ###############################################################################

        unique_set = set()
        for beneficiary in beneficiaries:
            unique_together = (beneficiary["account_number"], beneficiary["bank_code"])
            if unique_together in unique_set:
                raise serializers.ValidationError(
                    f"Duplicate beneficiary found with account {unique_together[0]} and bank {unique_together[1]}."
                )
            unique_set.add(unique_together)

        account_instance = AccountSystem.objects.filter(
            company=company_instance,
            account_type=(
                "SPEND_MGMT" if wallet_type == "CORPORATE" else "NON_CORP_SPEND_MGMT"
            ),
        ).first()

        if not account_instance:
            raise serializers.ValidationError(
                {"message": f"Company account details not found"}
            )

        wallet_instance = Wallet.objects.filter(account=account_instance).first()
        updated_beneficiaries_with_ids = Beneficiary.get_create_beneficiaries(
            user=user, beneficiaries=beneficiaries
        )
        get_amount_summary = Beneficiary.get_total_deductable_and_actual_transfer_for_multiple_beneficiary(
            beneficiaries=updated_beneficiaries_with_ids
        )

        _total_deductable = get_amount_summary.get("total_deductable")
        if not wallet_instance:
            raise serializers.ValidationError({"message": f"Wallet not found"})

        if not is_scheduled:
            if _total_deductable > wallet_instance.balance:
                raise serializers.ValidationError(
                    {
                        "message": f"Insufficient wallet balance: {wallet_instance.balance} Debit amount: {_total_deductable}"
                    }
                )

        wallet_id = wallet_instance.id
        # Get the current time
        now = timezone.now()
        interval = 10

        # Calculate the time difference (5 minutes)
        time_threshold = now - timedelta(minutes=interval)

        # Filter BulkTransfer objects created within the last 5 minutes
        recent_transfers = BulkTransfer.objects.filter(
            created_at__gte=time_threshold,
            user_wallet=wallet_instance,
            expected_debit=_total_deductable,
        )

        # Example validation
        if recent_transfers.last() is not None:
            raise serializers.ValidationError(
                {
                    "message": f"Duplicate transfer exist please try again in {interval} minutes."
                }
            )
        data["wallet_id"] = wallet_id
        data["updated_beneficiaries_with_ids"] = updated_beneficiaries_with_ids
        return data
