# abstract_provider.py
from abc import ABC, abstractmethod

from account.helpers.vbank_handler import VfdBankMgr
# from helpers.reusable_functions import convert_date_format


class AbstractAccountProvider(ABC):
    @abstractmethod
    def create_account(
        self,
        user,
        kyc,
        previous_account_number=None,
        cac_num=None,
        incorporation_date=None,
    ):
        pass


class VfdAccountProvider(AbstractAccountProvider):
    def create_account(
        self,
        kyc,
        formatted_date,
        previous_account_number=None,
        cac_num=None,
        account_type=None,
        company_name=None,
        bvn=None,
    ):
        vfd = VfdBankMgr()
        if cac_num and formatted_date:

            return vfd.create_corporate_client(
                rcNumber=cac_num,
                companyName=f"{company_name}-{account_type}".lower(),
                incorporationDate=str(formatted_date),
                bvn=bvn,
            )
        else:
            # create personal wallet
            return vfd.create_wallet(
                dob=str(formatted_date),
                bvn=kyc.bvn_number,
                previousAccountNo=previous_account_number,
            )
