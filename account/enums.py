from django.db.models import TextChoices


class AcctProvider(TextChoices):
    FIDELITY = "FIDELITY", "FIDELITY"
    VFD = "VFD", "VFD"
    WEMA = "WEMA", "WEMA"


class AccountType(TextChoices):
    COMMISSION = "COMMISSION", "Commission"
    COMPANY_SPEND_MGMT = "COMPANY_SPEND_MGMT", "Company Spend Mgmt"
    INSTANT_WAGE = "INSTANT_WAGE", "Instant Wage"
    NON_CORP_PAYROLL = "NON_CORP_PAYROLL", "Non Corporate Payroll"
    NON_CORP_SPEND_MGMT = "NON_CORP_SPEND_MGMT", "Non Corporate Spend Mgmt"
    PAYROLL = "PAYROLL", "Payroll"
    PERSONAL = "PERSONAL", "Personal"
    PERSONAL_PAYROLL = "PERSONAL_PAYROLL", "Personal Payroll"
    PERSONAL_SPEND_MGMT = "PERSONAL_SPEND_MGMT", "Personal Spend Mgmt"
    SALES = "SALES", "Sales"
    SMS = "SMS", "Sms"
    SPEND_MGMT = "SPEND_MGMT", "Spend Mgmt"
    PENSION = "PENSION", "Pension"
    STAMP_DUTY = "STAMP_DUTY", "Stamp Duty"
    OTHERS = "OTHERS", "Others"


class TransactionType(TextChoices):
    ADD_FUND_LIBERTYPAY = "ADD_FUND_LIBERTYPAY", "Add Fund Libertypay"
    AIRTIME_TOP_UP = "AIRTIME_TOP_UP", "Airtime Top Up"
    BANK_TRANSFER = "BANK_TRANSFER", "Bank Transfer"
    WALLET_TRANSFER = "WALLET_TRANSFER", "Wallet Transfer"
    BUDDY = "BUDDY", "Buddy"
    FUND_BUDDY = "FUND_BUDDY", "Fund Buddy"
    CARD = "CARD", "Card"
    COMMISSION = "COMMISSION", "Commission"
    DEPOSIT = "DEPOSIT", "Deposit"
    DUPLICATE_FUND_WALLET = "DUPLICATE_FUND_WALLET", "Duplicate Fund Wallet"
    FUND_WALLET = "FUND_WALLET", "Fund Wallet"
    RETRIEVAL = "RETRIEVAL", "Retrieval"
    REVERSAL = "REVERSAL", "Reversal"
    SEND_MONEY_TO_FLOAT = "SEND_MONEY_TO_FLOAT", "Send Money To Float"
    STAMP_DUTY = "STAMP_DUTY", "Stamp Duty"
    STAMP_DUTY_REFUND = "STAMP_DUTY_REFUND", "Stamp Duty Refund"
    FLOAT_RECONCILIATION = "FLOAT_RECONCILIATION", "Float Reconciliation"


class TransactionStatus(TextChoices):
    FAILED = "FAILED", "Failed"
    IGNORE_HISTORY = "IGNORE_HISTORY", "Ignore History"
    IN_PROGRESS = "IN_PROGRESS", "In Progress"
    PENDING = "PENDING", "Pending"
    REVERSED = "REVERSED", "Reversed"
    SUCCESSFUL = "SUCCESSFUL", "Successful"
    SCHEDULED = "SCHEDULED", "Scheduled"  # Waiting for a future run
    RUNNING = "RUNNING", "Running"  # Currently executing (for scheduled)
    CANCELLED = "CANCELLED", "Cancelled"  # Transfer was canceled
    EXPIRED = "EXPIRED", "Expired"  # Scheduled transfer missed execution
    COMPLETED = "COMPLETED", "Completed"  # All transfers successful
    PARTIALLY_COMPLETED = "PARTIALLY_COMPLETED", "Partially Completed"  # Some items succeeded


class TransferStage(TextChoices):
    DEDUCTED = "DEDUCTED", "Deducted"
    DEBIT = "DEBIT", "Debit"
    FLOAT_TO_INTERNAL = "FLOAT_TO_INTERNAL", "Float To Internal"
    FLOAT_TO_EXTERNAL = "FLOAT_TO_EXTERNAL", "Float To External"
    INTERNAL_TO_FLOAT = "INTERNAL_TO_FLOAT", "Internal To Float"
    INTERNAL_TO_INTERNAL = "INTERNAL_TO_INTERNAL", "Internal To Internal"
    INTERNAL_TO_OUTWARDS = "INTERNAL_TO_OUTWARDS", "Internal To Outwards"


class DebitCreditEntry(TextChoices):
    CREDIT = "CREDIT", "Credit"
    DEBIT = "DEBIT", "Debit"
    REVERSAL = "REVERSAL", "Reversal"


class ScheduleTypes(TextChoices):
    ONE_OFF = "ONE_OFF", "ONE_OFF"
    DAILY = "DAILY", "DAILY"
    WEEKLY = "WEEKLY", "WEEKLY"
    MONTHLY = "MONTHLY", "MONTHLY"
    YEARLY = "YEARLY", "YEARLY"
