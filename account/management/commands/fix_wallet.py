
from collections import defaultdict
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from account.models import Wallet, AccountType  # Adjust to your actual model path

class Command(BaseCommand):
    help = 'Fix duplicate wallets by updating wallet type to OTHERS except one. Skips wallets with balance > 0.'

    def add_arguments(self, parser):
        parser.add_argument(
            'wallet_type',
            type=str,
            help='The wallet type to check for (e.g., PERSONAL_PAYROLL)',
        )

    def handle(self, *args, **options):
        wallet_type_str = options['wallet_type'].upper()

        # Validate wallet type
        try:
            target_wallet_type = getattr(AccountType, wallet_type_str)
        except AttributeError:
            raise CommandError(f"Invalid wallet type: {wallet_type_str}")

        self.stdout.write(f"Processing wallets with type: {wallet_type_str}")

        # Get all wallets of this type
        wallets = Wallet.objects.filter(wallet_type=target_wallet_type).select_related('account')

        user_wallets = defaultdict(list)
        for wallet in wallets:
            user_wallets[wallet.user_id].append(wallet)

        updated = 0

        for user_id, user_wallet_list in user_wallets.items():
            if len(user_wallet_list) <= 1:
                continue  # nothing to fix

            with_balance = [w for w in user_wallet_list if w.balance > 0]
            zero_balance = [w for w in user_wallet_list if w.balance <= 0]

            with transaction.atomic():
                if with_balance:
                    for w in zero_balance:
                        w.wallet_type = AccountType.OTHERS
                        w.save(update_fields=["wallet_type"])
                        if w.account:
                            w.account.account_type = AccountType.OTHERS
                            w.account.save(update_fields=["account_type"])
                        updated += 1
                else:
                    zero_balance.sort(key=lambda x: x.id)  # choose one to keep
                    to_keep = zero_balance[0]
                    for w in zero_balance[1:]:
                        w.wallet_type = AccountType.OTHERS
                        w.save(update_fields=["wallet_type"])
                        if w.account:
                            w.account.account_type = AccountType.OTHERS
                            w.account.save(update_fields=["account_type"])
                        updated += 1

        self.stdout.write(self.style.SUCCESS(f"Updated {updated} wallets."))
