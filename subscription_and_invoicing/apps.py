from django.apps import AppConfig


# class SubscriptionAndInvoicingConfig(AppConfig):
#     default_auto_field = "django.db.models.BigAutoField"
#     name = "subscription_and_invoicing"

# subscriptions/apps.py


class SubscriptionAndInvoicingConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "subscription_and_invoicing"

    def ready(self):
        from subscription_and_invoicing import signals