from core.models import PaystackPayment
from subscription_and_invoicing import models as subscription
from rest_framework.response import Response
from celery import shared_task
from decimal import Decimal
from rest_framework import status
from django.contrib.auth import get_user_model
from django.db import transaction, models
from subscription_and_invoicing.models import (
    CommissionStructure,
    Commission,
    InvoiceTransaction,
    Invoice,
)
from subscription_and_invoicing.serializers import InvoiceTransactionSerializer
from django.utils import timezone
from .models import ModuleSubscription, CompanySubscription
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


@shared_task
def paystack_payment_confirmation(batch_id, amount_paid, reference):
    try:
        if not batch_id or not amount_paid:
            logger.error(
                f"Missing required fields - batch_id: {batch_id}, amount_paid: {amount_paid}"
            )
            return "Missing required fields"

        with transaction.atomic():
            invoice = (
                subscription.Invoice.objects.select_related("company_subscription")
                .filter(
                    models.Q(batch_id=batch_id) | models.Q(invoice_reference=reference)
                )
                .first()
            )

            if not invoice:
                logger.error(
                    f"Invoice not found using batch_id or reference: {batch_id} / {reference}"
                )
                return f"Invoice not found"

            amount_paid_decimal = Decimal(str(amount_paid))
            invoice_total = invoice.total_amount or Decimal("0")

            if amount_paid_decimal <= 0:
                logger.error(f"Invalid payment amount: {amount_paid_decimal}")
                return "Invalid payment amount"

            if batch_id == reference and amount_paid_decimal >= invoice_total:
                new_balance_due = max(Decimal("0"), invoice_total - amount_paid_decimal)
                excess_amount = max(Decimal("0"), amount_paid_decimal - invoice_total)

                invoice.payment_status = "paid_excess" if excess_amount > 0 else "paid"
                invoice.amount_paid = amount_paid_decimal
                invoice.balance_due = new_balance_due
                invoice.settled_amount = amount_paid_decimal
                invoice.is_active = True

                if excess_amount > 0:
                    invoice.amount_brought_forward = excess_amount

                invoice.save()
                logger.info(
                    f"Invoice {invoice.id} updated with payment status {invoice.payment_status}"
                )

                company_subscription = invoice.company_subscription
                if not company_subscription:
                    company_subscription = (
                        subscription.CompanySubscription.objects.filter(
                            invoice_ref=reference
                        ).first()
                    )

                    if company_subscription:
                        invoice.company_subscription = company_subscription
                        invoice.save(update_fields=["company_subscription"])
                        logger.info(
                            f"Linked invoice to CompanySubscription via reference {reference}"
                        )
                    else:
                        logger.warning(
                            f"No CompanySubscription found for reference {reference}"
                        )

                if company_subscription:
                    subscription.CompanySubscription.objects.filter(
                        id=company_subscription.id
                    ).update(
                        status="active",
                        token_previous_balance=models.F("token_current_balance"),
                        token_current_balance=models.F("token_current_balance")
                        + amount_paid_decimal,
                        token_status="active",
                        invoice_ref=reference,
                        updated_at=timezone.now(),
                    )

                    logger.info(
                        f"Updated CompanySubscription ID {company_subscription.id}"
                    )

                    module_subs = subscription.ModuleSubscription.objects.filter(
                        company_subscription=company_subscription
                    )

                    if module_subs.exists():
                        module_subs.update(
                            invoice_ref=reference,
                            status="active",
                            is_active=True,
                            updated_at=timezone.now(),
                        )
                        logger.info(
                            f"Updated {module_subs.count()} ModuleSubscription(s)"
                        )
                    else:
                        logger.warning(
                            f"No ModuleSubscriptions found for CompanySubscription ID {company_subscription.id}"
                        )

                    if hasattr(
                        company_subscription, "update_overall_subscription_days"
                    ):
                        try:
                            company_subscription.update_overall_subscription_days()
                            logger.info(
                                f"Updated overall subscription days for CompanySubscription {company_subscription.id}"
                            )
                        except Exception as update_err:
                            logger.warning(
                                f"Error updating overall subscription days: {str(update_err)}"
                            )
                else:
                    logger.warning(
                        f"Invoice {invoice.id} has no associated CompanySubscription"
                    )

                subscription.InvoiceTransaction.objects.create(
                    invoice=invoice,
                    amount=amount_paid_decimal,
                    payment_method="paystack",
                    excess_payment=excess_amount,
                    is_processed=True,
                    invoice_reference=reference,
                )

                logger.info(
                    f"Created InvoiceTransaction for invoice ID {invoice.id} with amount {amount_paid_decimal}"
                )

                return f"✅ Payment processed and subscriptions updated for invoice {invoice.id}"

            else:
                # Handle partial payments
                invoice.amount_paid = (
                    invoice.amount_paid or Decimal("0")
                ) + amount_paid_decimal
                invoice.balance_due = max(
                    Decimal("0"), invoice_total - invoice.amount_paid
                )
                invoice.settled_amount = (
                    invoice.settled_amount or Decimal("0")
                ) + amount_paid_decimal

                if invoice.balance_due == Decimal("0"):
                    invoice.payment_status = "paid"
                elif invoice.balance_due < invoice_total:
                    invoice.payment_status = "part_payment"
                else:
                    invoice.payment_status = "unpaid"

                invoice.save()

                logger.info(
                    f"Partial payment processed for invoice ID {invoice.id}, payment status: {invoice.payment_status}"
                )
                return f"🟡 Partial payment processed for invoice {invoice.id}"

    except Exception as e:
        logger.error(f"❌ Error processing payment confirmation: {str(e)}")
        return f"❌ Error: {str(e)}"


def _process_commission(invoice_transaction, amount_paid_decimal):
    """
    Process commission calculations for the invoice transaction.

    Args:
        invoice_transaction: InvoiceTransaction instance
        amount_paid_decimal: Payment amount as Decimal
    """
    try:
        commission_structures = subscription.CommissionStructure.objects.all()

        for structure in commission_structures:
            # Get recipients based on commission role
            recipients = User.objects.filter(commission__role=structure.role)

            for recipient in recipients:
                commission_amount = amount_paid_decimal * (structure.percentage / 100)

                # Create commission record
                subscription.Commission.objects.create(
                    invoice_transaction=invoice_transaction,
                    recipient=recipient,
                    role=structure.role,
                    amount=commission_amount,
                    paid=False,
                )

        logger.info(
            f"Successfully processed commissions for transaction {invoice_transaction.id}"
        )

    except Exception as e:
        logger.error(f"Commission processing error: {str(e)}")
        raise e


# # Quick test function for single scenario
# def quick_test_payment(paystack_payment_id, test_reference, test_amount):
#     """
#     Quick test function for single payment scenario.
#
#     Args:
#         paystack_payment_id: ID of the PaystackPayment instance
#         test_reference: Test reference number
#         test_amount: Test amount
#
#     Returns:
#         str: Test result
#     """
#     logger.info(f"Quick test - Reference: {test_reference}, Amount: {test_amount}")
#
#     return paystack_payment_confirmation(
#         id=paystack_payment_id, test_amount=test_amount, test_reference=test_reference
#     )


def _process_commission(invoice_transaction, amount_paid_decimal):
    """
    Process commission calculations for the invoice transaction.

    Args:
        invoice_transaction: InvoiceTransaction instance
        amount_paid_decimal: Payment amount as Decimal
    """
    try:
        commission_structures = subscription.CommissionStructure.objects.all()

        for structure in commission_structures:
            # Get recipients based on commission role
            recipients = User.objects.filter(commission__role=structure.role)

            for recipient in recipients:
                commission_amount = amount_paid_decimal * (structure.percentage / 100)

                # Create commission record
                subscription.Commission.objects.create(
                    invoice_transaction=invoice_transaction,
                    recipient=recipient,
                    role=structure.role,
                    amount=commission_amount,
                    paid=False,
                )

        logger.info(
            f"Successfully processed commissions for transaction {invoice_transaction.id}"
        )

    except Exception as e:
        logger.error(f"Commission processing error: {str(e)}")
        raise e


@shared_task
def expire_trial_subs():
    """
    Celery task to expire individual trial module subscriptions that have passed their end date.
    Uses a single query to find all expired trial module subscriptions.
    """
    now = timezone.now()
    expired_subs = ModuleSubscription.objects.filter(
        end_date__lt=now,
        is_active=True,
        status="active",
        company_subscription__access_type="trial",
    )

    count = expired_subs.count()

    if count == 0:
        logger.info("No trial subscriptions to expire.")
        return "No trial subscriptions to expire."

    for sub in expired_subs:
        # Use mark_expired() method which handles proper status updates and company subscription syncing
        if hasattr(sub, "mark_expired") and callable(getattr(sub, "mark_expired")):
            sub.mark_expired()
            logger.info(
                f"Expired trial subscription for module '{sub.module}' and company '{sub.company_subscription.company}'"
            )
        else:
            # Fallback if mark_expired doesn't exist
            ModuleSubscription.objects.filter(pk=sub.pk).update(
                is_active=False, status="expired"
            )
            # Update instance attributes to match DB
            sub.is_active = False
            sub.status = "expired"
            # Update parent company subscription
            if hasattr(sub.company_subscription, "update_status"):
                sub.company_subscription.update_status()
            logger.info(
                f"Expired trial subscription for module '{sub.module}' and company '{sub.company_subscription.company}'"
            )

    logger.info(f"{count} trial module subscriptions expired.")
    return f"{count} trial module subscriptions expired."


@shared_task
def expire_trial_subscriptions():
    """
    Celery task to expire company trial subscriptions when all their module subscriptions have expired.
    Checks each trial company subscription and its modules.
    """
    now = timezone.now()
    expired_companies = []
    expired_modules_count = 0

    # Get all trial company subscriptions that are still marked as trial
    trials = CompanySubscription.objects.filter(status="trial", access_type="trial")

    if not trials.exists():
        logger.info("No active trial subscriptions found.")
        return "No trial subscriptions to expire."

    logger.info(f"Checking {trials.count()} trial subscriptions for expiry...")

    # Process each company subscription
    for company_sub in trials:
        module_subs = company_sub.modulesubscription_set.all()

        if not module_subs.exists():
            logger.warning(
                f"Company subscription {company_sub.pk} has no module subscriptions"
            )
            continue

        # Track if all modules for this company are expired
        all_modules_expired = True
        newly_expired_modules = 0

        # Check each module subscription
        for module_sub in module_subs:
            if module_sub.end_date <= now:
                if module_sub.status != "expired" or module_sub.is_active:
                    # Use mark_expired if available
                    if hasattr(module_sub, "mark_expired") and callable(
                        getattr(module_sub, "mark_expired")
                    ):
                        if module_sub.mark_expired():
                            newly_expired_modules += 1
                            logger.info(f"Expired module subscription: {module_sub}")
                    else:
                        # Fallback direct update
                        ModuleSubscription.objects.filter(pk=module_sub.pk).update(
                            status="expired", is_active=False
                        )
                        module_sub.status = "expired"
                        module_sub.is_active = False
                        newly_expired_modules += 1
                        logger.info(f"Expired module subscription: {module_sub}")
            else:
                # This module is still active, so not all modules are expired
                all_modules_expired = False

        expired_modules_count += newly_expired_modules

        # After updating modules, let company subscription update its status
        if newly_expired_modules > 0:
            # Force an update of the company subscription status
            if hasattr(company_sub, "update_status"):
                company_sub.update_status()
                # If the update made it expired, log it
                if company_sub.status == "expired":
                    expired_companies.append(company_sub.company.company_name)
                    logger.info(
                        f"Marking company subscription as expired: {company_sub}"
                    )
            else:
                # Only manually update if all modules are now expired
                if all_modules_expired and company_sub.status != "expired":
                    CompanySubscription.objects.filter(pk=company_sub.pk).update(
                        status="expired"
                    )
                    company_sub.status = "expired"
                    expired_companies.append(company_sub.company.company_name)
                    logger.info(
                        f"Marking company subscription as expired: {company_sub}"
                    )

    if expired_modules_count > 0:
        logger.info(f"Expired {expired_modules_count} module subscriptions")

    if expired_companies:
        logger.info(
            f"Expired {len(expired_companies)} trial company subscriptions: {expired_companies}"
        )

    return f"Expired {len(expired_companies)} company subscriptions and {expired_modules_count} module subscriptions."
