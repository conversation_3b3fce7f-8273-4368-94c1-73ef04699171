# admin.py - Enhanced with django-import-export

import csv
from datetime import timedelta
from django.contrib import admin
from django.contrib.admin import SimpleListFilter
from django.http import HttpResponse
from django.urls import reverse
from django.utils.html import format_html
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg

# Django Import-Export imports
from import_export import resources, fields
from import_export.admin import ImportExportModelAdmin, ExportMixin
from import_export.widgets import ForeignKeyWidget, DateTimeWidget, BooleanWidget

from .models import (
    ExcludedPath,
    Invoice,
    PromotionalOffer,
    SubscriptionPlan,
    Module,
    CompanySubscription,
    ModuleSubscription,
    SubscriptionAudit,
)


# ==================== IMPORT-EXPORT RESOURCES ====================


class CompanySubscriptionResource(resources.ModelResource):
    """Resource for exporting CompanySubscription data with related fields"""

    # Basic company information
    company_id = fields.Field(attribute="company__id", column_name="Company ID")
    company_name = fields.Field(
        attribute="company__company_name", column_name="Company Name"
    )
    company_email = fields.Field(
        attribute="company__user__email", column_name="Company Email"
    )
    industry = fields.Field(attribute="company__industry", column_name="Industry")
    cac_number = fields.Field(attribute="company__cac_num", column_name="CAC Number")
    company_size = fields.Field(attribute="company__size", column_name="Company Size")

    # Subscription information
    subscription_status = fields.Field(
        attribute="status", column_name="Subscription Status"
    )
    access_type = fields.Field(attribute="access_type", column_name="Access Type")
    overall_subscription_days = fields.Field(
        attribute="overall_sub_days", column_name="Overall Subscription Days"
    )
    invoice_reference = fields.Field(
        attribute="invoice_ref", column_name="Invoice Reference"
    )
    pos_included = fields.Field(
        attribute="pos_included", column_name="POS Included", widget=BooleanWidget()
    )

    # Dates
    subscription_created = fields.Field(
        attribute="created_at",
        column_name="Subscription Created",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )
    last_updated = fields.Field(
        attribute="updated_at",
        column_name="Last Updated",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )

    # Calculated fields
    active_modules_count = fields.Field(column_name="Active Modules Count")
    total_modules_count = fields.Field(column_name="Total Modules Count")

    def dehydrate_active_modules_count(self, subscription):
        """Calculate active modules count"""
        return subscription.modulesubscription_set.filter(
            is_active=True, end_date__gt=timezone.now()
        ).count()

    def dehydrate_total_modules_count(self, subscription):
        """Calculate total modules count"""
        return subscription.modulesubscription_set.count()

    class Meta:
        model = CompanySubscription
        fields = (
            "id",
            "company_id",
            "company_name",
            "company_email",
            "industry",
            "cac_number",
            "company_size",
            "subscription_status",
            "access_type",
            "overall_subscription_days",
            "invoice_reference",
            "pos_included",
            "active_modules_count",
            "total_modules_count",
            "subscription_created",
            "last_updated",
        )
        export_order = fields

    def get_queryset(self):
        """Optimize queryset for export"""
        return (
            super()
            .get_queryset()
            .select_related("company", "company__user")
            .prefetch_related("modulesubscription_set")
        )


class ModuleSubscriptionResource(resources.ModelResource):
    """Resource for exporting detailed ModuleSubscription data"""

    # Company information
    company_id = fields.Field(
        attribute="company_subscription__company__id", column_name="Company ID"
    )
    company_name = fields.Field(
        attribute="company_subscription__company__company_name",
        column_name="Company Name",
    )
    company_email = fields.Field(
        attribute="company_subscription__company__user__email",
        column_name="Company Email",
    )
    industry = fields.Field(
        attribute="company_subscription__company__industry", column_name="Industry"
    )
    company_size = fields.Field(
        attribute="company_subscription__company__size", column_name="Company Size"
    )

    # Module information
    module_name = fields.Field(attribute="module__name", column_name="Module Name")
    module_code = fields.Field(attribute="module__code", column_name="Module Code")
    module_description = fields.Field(
        attribute="module__description", column_name="Module Description"
    )
    is_premium_module = fields.Field(
        attribute="module__is_premium",
        column_name="Is Premium Module",
        widget=BooleanWidget(),
    )

    # Plan information
    plan_name = fields.Field(attribute="plan__name", column_name="Plan Name")
    plan_duration_months = fields.Field(
        attribute="plan__duration_months", column_name="Plan Duration (Months)"
    )
    plan_price = fields.Field(attribute="plan__price", column_name="Plan Price")

    # Subscription details
    subscription_status = fields.Field(
        attribute="status", column_name="Subscription Status"
    )
    is_active = fields.Field(
        attribute="is_active", column_name="Is Active", widget=BooleanWidget()
    )
    is_auto_renewal = fields.Field(
        attribute="is_auto_renewal", column_name="Auto Renewal", widget=BooleanWidget()
    )

    # Financial information
    pending_balance = fields.Field(
        attribute="pending_balance", column_name="Pending Balance"
    )
    promotional_offer_name = fields.Field(
        attribute="promotional_offer__name", column_name="Promotional Offer"
    )
    discount_amount = fields.Field(
        attribute="promotional_offer__discount_amount", column_name="Discount Amount"
    )

    # Dates and duration
    start_date = fields.Field(
        attribute="start_date",
        column_name="Start Date",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )
    end_date = fields.Field(
        attribute="end_date",
        column_name="End Date",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )
    days_remaining = fields.Field(column_name="Days Remaining")
    module_overall_days = fields.Field(
        attribute="module_overall_sub_days", column_name="Module Overall Days"
    )

    # Additional information
    invoice_reference = fields.Field(
        attribute="invoice_ref", column_name="Invoice Reference"
    )
    company_subscription_status = fields.Field(
        attribute="company_subscription__status",
        column_name="Company Subscription Status",
    )
    access_type = fields.Field(
        attribute="company_subscription__access_type", column_name="Access Type"
    )

    # Timestamps
    created_at = fields.Field(
        attribute="created_at",
        column_name="Created At",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )

    def dehydrate_days_remaining(self, module_subscription):
        """Calculate days remaining"""
        days = module_subscription.days_remaining()
        return str(days) if days >= 0 else "Expired"

    class Meta:
        model = ModuleSubscription
        fields = (
            "id",
            "company_id",
            "company_name",
            "company_email",
            "industry",
            "company_size",
            "module_name",
            "module_code",
            "module_description",
            "is_premium_module",
            "plan_name",
            "plan_duration_months",
            "plan_price",
            "subscription_status",
            "is_active",
            "is_auto_renewal",
            "pending_balance",
            "promotional_offer_name",
            "discount_amount",
            "start_date",
            "end_date",
            "days_remaining",
            "module_overall_days",
            "invoice_reference",
            "company_subscription_status",
            "access_type",
            "created_at",
        )
        export_order = fields

    def get_queryset(self):
        """Optimize queryset for export"""
        return (
            super()
            .get_queryset()
            .select_related(
                "company_subscription__company",
                "company_subscription__company__user",
                "module",
                "plan",
                "promotional_offer",
            )
        )


class InvoiceResource(resources.ModelResource):
    """Resource for exporting Invoice data"""

    # Company information
    company_id = fields.Field(attribute="company__id", column_name="Company ID")
    company_name = fields.Field(
        attribute="company__company_name", column_name="Company Name"
    )
    company_email = fields.Field(
        attribute="company__user__email", column_name="Company Email"
    )
    industry = fields.Field(attribute="company__industry", column_name="Industry")

    # Invoice details
    invoice_reference = fields.Field(
        attribute="invoice_reference", column_name="Invoice Reference"
    )
    batch_id = fields.Field(attribute="batch_id", column_name="Batch ID")

    # Financial information
    total_amount = fields.Field(attribute="total_amount", column_name="Total Amount")
    amount_paid = fields.Field(attribute="amount_paid", column_name="Amount Paid")
    balance_due = fields.Field(attribute="balance_due", column_name="Balance Due")
    settled_amount = fields.Field(
        attribute="settled_amount", column_name="Settled Amount"
    )
    amount_brought_forward = fields.Field(
        attribute="amount_brought_forward", column_name="Amount Brought Forward"
    )

    # Status and configuration
    payment_status = fields.Field(
        attribute="payment_status", column_name="Payment Status"
    )
    is_active = fields.Field(
        attribute="is_active", column_name="Is Active", widget=BooleanWidget()
    )
    used_excess_payment = fields.Field(
        attribute="used_excess_payment",
        column_name="Used Excess Payment",
        widget=BooleanWidget(),
    )
    promo_applied = fields.Field(
        attribute="promo_applied", column_name="Promo Applied", widget=BooleanWidget()
    )

    # Dates
    start_date = fields.Field(
        attribute="start_date",
        column_name="Start Date",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )
    expiry_date = fields.Field(
        attribute="expiry_date",
        column_name="Expiry Date",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )
    created_at = fields.Field(
        attribute="created_at",
        column_name="Created At",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )

    # Sales officer
    sales_officer = fields.Field(
        attribute="sales_officer__name", column_name="Sales Officer"
    )
    package_description = fields.Field(
        attribute="package_description", column_name="Package Description"
    )

    # Calculated fields
    days_until_expiry = fields.Field(column_name="Days Until Expiry")

    def dehydrate_days_until_expiry(self, invoice):
        """Calculate days until expiry"""
        if not invoice.expiry_date:
            return ""
        days = (invoice.expiry_date - timezone.now()).days
        return str(days) if days >= 0 else "Expired"

    class Meta:
        model = Invoice
        fields = (
            "id",
            "company_id",
            "company_name",
            "company_email",
            "industry",
            "invoice_reference",
            "batch_id",
            "total_amount",
            "amount_paid",
            "balance_due",
            "settled_amount",
            "amount_brought_forward",
            "payment_status",
            "is_active",
            "used_excess_payment",
            "promo_applied",
            "start_date",
            "expiry_date",
            "days_until_expiry",
            "sales_officer",
            "package_description",
            "created_at",
        )
        export_order = fields

    def get_queryset(self):
        """Optimize queryset for export"""
        return (
            super()
            .get_queryset()
            .select_related("company", "company__user", "sales_officer")
        )


class SubscriptionPlanResource(resources.ModelResource):
    """Resource for exporting SubscriptionPlan data"""

    name = fields.Field(attribute="name", column_name="Plan Name")
    description = fields.Field(attribute="description", column_name="Description")
    price = fields.Field(attribute="price", column_name="Price")
    duration_months = fields.Field(
        attribute="duration_months", column_name="Duration (Months)"
    )
    is_active = fields.Field(
        attribute="is_active", column_name="Is Active", widget=BooleanWidget()
    )
    created_at = fields.Field(
        attribute="created_at",
        column_name="Created At",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )
    updated_at = fields.Field(
        attribute="updated_at",
        column_name="Updated At",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )

    class Meta:
        model = SubscriptionPlan
        fields = (
            "id",
            "name",
            "description",
            "price",
            "duration_months",
            "is_active",
            "created_at",
            "updated_at",
        )
        export_order = fields


# ==================== FILTER CLASSES ====================


class PriceRangeFilter(SimpleListFilter):
    title = "Price Range"
    parameter_name = "price_range"

    def lookups(self, request, model_admin):
        return (
            ("0-50k", "₦0 - ₦50,000"),
            ("50k-150k", "₦50,000 - ₦150,000"),
            ("150k-300k", "₦150,000 - ₦300,000"),
            ("300k+", "₦300,000+"),
        )

    def queryset(self, request, queryset):
        if self.value() == "0-50k":
            return queryset.filter(plan__price__lte=50000)
        if self.value() == "50k-150k":
            return queryset.filter(plan__price__gt=50000, plan__price__lte=150000)
        if self.value() == "150k-300k":
            return queryset.filter(plan__price__gt=150000, plan__price__lte=300000)
        if self.value() == "300k+":
            return queryset.filter(plan__price__gt=300000)


class ExpiryFilter(SimpleListFilter):
    title = "Expiry Status"
    parameter_name = "expiry_status"

    def lookups(self, request, model_admin):
        return (
            ("expired", "Expired"),
            ("due_7", "Due in 7 days"),
            ("due_30", "Due in 30 days"),
            ("active", "Active (>30 days)"),
        )

    def queryset(self, request, queryset):
        now = timezone.now()
        if self.value() == "expired":
            return queryset.filter(modulesubscription__end_date__lt=now).distinct()
        if self.value() == "due_7":
            return queryset.filter(
                modulesubscription__end_date__range=(now, now + timedelta(days=7))
            ).distinct()
        if self.value() == "due_30":
            return queryset.filter(
                modulesubscription__end_date__range=(now, now + timedelta(days=30))
            ).distinct()
        if self.value() == "active":
            return queryset.filter(
                modulesubscription__end_date__gt=now + timedelta(days=30)
            ).distinct()


# ==================== ADMIN CLASSES ====================


@admin.register(CompanySubscription)
class CompanySubscriptionAdmin(ImportExportModelAdmin):
    """Enhanced CompanySubscription admin with import/export functionality"""

    resource_class = CompanySubscriptionResource

    list_display = (
        "id",
        "company",
        "subscribed_company",
        "status",
        "access_type",
        "overall_sub_days",
        "invoice_ref",
        "active_modules_display",
        "pos_included",
        "created_at",
    )
    list_filter = (
        "status",
        "access_type",
        "pos_included",
        ExpiryFilter,
        PriceRangeFilter,
    )
    search_fields = ("company__company_name", "invoice_ref")
    readonly_fields = ("created_at", "updated_at")
    actions = ["mark_as_expired", "extend_all_modules"]

    autocomplete_fields = ["created_by", "company"]

    def get_queryset(self, request):
        """Optimize queryset with select_related and prefetch_related"""
        return (
            super()
            .get_queryset(request)
            .select_related("company")
            .prefetch_related(
                "modulesubscription_set__module", "modulesubscription_set__plan"
            )
        )

    def subscribed_company(self, obj):
        url = reverse("admin:requisition_company_change", args=[obj.company.id])
        return format_html('<a href="{}">{}</a>', url, obj.company.company_name)

    subscribed_company.short_description = "Subscribed Company"

    def active_modules_display(self, obj):
        active = obj.modulesubscription_set.filter(
            is_active=True, end_date__gt=timezone.now()
        ).count()
        total = Module.objects.count()
        if active < total:
            return format_html(
                '<span style="color: orange;">{}/{}</span>', active, total
            )
        return f"{active}/{total}"

    active_modules_display.short_description = "Active Modules"

    def mark_as_expired(self, request, queryset):
        """Mark selected subscriptions as expired"""
        updated = queryset.update(status="expired")
        self.message_user(
            request, f"Successfully marked {updated} subscriptions as expired."
        )

    mark_as_expired.short_description = "Mark selected subscriptions as expired"

    def extend_all_modules(self, request, queryset):
        """Extend all modules for selected subscriptions by 30 days"""
        extended_count = 0
        for subscription in queryset:
            modules = subscription.modulesubscription_set.filter(is_active=True)
            for module in modules:
                module.end_date += timedelta(days=30)
                module.save()
                extended_count += 1

        self.message_user(
            request,
            f"Successfully extended {extended_count} module subscriptions by 30 days.",
        )

    extend_all_modules.short_description = "Extend all active modules by 30 days"


@admin.register(ModuleSubscription)
class ModuleSubscriptionAdmin(ImportExportModelAdmin):
    """Enhanced ModuleSubscription admin with import/export functionality"""

    resource_class = ModuleSubscriptionResource

    list_display = (
        "module",
        "company_name",
        "plan_display",
        "is_active",
        "start_date",
        "end_date",
        "days_remaining_display",
        "pending_balance_display",
        "promo_details",
        "module_overall_sub_days",
    )
    list_filter = ("is_active", "module", "plan", "status")
    search_fields = (
        "company_subscription__company__company_name",
        "module__name",
        "plan__name",
    )
    actions = ["refresh_module_days"]

    autocomplete_fields = [
        "company_subscription",
        "module",
        "promotional_offer",
        "plan",
    ]

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return (
            super()
            .get_queryset(request)
            .select_related(
                "company_subscription__company", "module", "plan", "promotional_offer"
            )
        )

    def company_name(self, obj):
        return obj.company_subscription.company.company_name

    company_name.short_description = "Company"

    def plan_display(self, obj):
        return obj.plan.name if obj.plan else "-"

    plan_display.short_description = "Subscription Plan"

    def days_remaining_display(self, obj):
        days = obj.days_remaining()
        if days <= 0:
            return format_html('<span style="color: red;">Expired</span>')
        elif days <= 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return f"{days} days"

    days_remaining_display.short_description = "Days Remaining"

    def pending_balance_display(self, obj):
        if obj.pending_balance > 0:
            formatted_balance = "{:,.2f}".format(float(obj.pending_balance))
            return format_html(
                '<span style="color: red;">₦{}</span>', formatted_balance
            )
        return "-"

    pending_balance_display.short_description = "Pending Balance"

    def promo_details(self, obj):
        if obj.promotional_offer:
            try:
                discount_amount = float(obj.promotional_offer.discount_amount)
                formatted_amount = "{:,.2f}".format(discount_amount)
                return format_html(
                    "{} (-₦{})", obj.promotional_offer.name, formatted_amount
                )
            except (ValueError, TypeError):
                return "-"
        return "-"

    promo_details.short_description = "Promotion Applied"

    def refresh_module_days(self, request, queryset):
        """Admin action to refresh module subscription days for selected subscriptions."""
        count = 0
        for subscription in queryset:
            subscription.update_module_overall_days()
            count += 1

        self.message_user(
            request,
            f"Successfully updated subscription days for {count} module subscriptions.",
        )

    refresh_module_days.short_description = "Refresh module subscription days"


@admin.register(Invoice)
class InvoiceAdmin(ImportExportModelAdmin):
    """Enhanced Invoice admin with import/export functionality"""

    resource_class = InvoiceResource

    list_display = (
        "invoice_reference",
        "company_link",
        "amount_display",
        "amount_due",
        "batch_id",
        "payment_status",
        "expiry_status",
        "sales_officer",
        "created_at",
    )
    list_filter = (
        "payment_status",
        ExpiryFilter,
        "promo_applied",
        "sales_officer",
    )
    search_fields = (
        "invoice_reference",
        "batch_id",
        "company__company_name",
        "package_description",
    )
    readonly_fields = ("invoice_reference", "batch_id", "created_at", "updated_at")

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("company", "company_subscription", "sales_officer")
        )

    def company_link(self, obj):
        url = reverse("admin:requisition_company_change", args=[obj.company.id])
        return format_html('<a href="{}">{}</a>', url, obj.company.company_name)

    company_link.short_description = "Company"

    def amount_display(self, obj):
        if obj.amount_paid:
            formatted_amount = "{:,.2f}".format(float(obj.amount_paid))
            return format_html("₦{}", formatted_amount)
        return "₦0.00"

    amount_display.short_description = "Amount Paid"

    def amount_due(self, obj):
        if obj.balance_due and obj.balance_due > 0:
            formatted_balance = "{:,.2f}".format(float(obj.balance_due))
            return format_html(
                '<span style="color: red;">₦{}</span>', formatted_balance
            )
        return format_html("₦0.00")

    amount_due.short_description = "Balance Due"

    def expiry_status(self, obj):
        if not obj.expiry_date:
            return "-"
        days_until_expiry = (obj.expiry_date - timezone.now()).days
        if days_until_expiry < 0:
            return format_html('<span style="color: red;">Expired</span>')
        elif days_until_expiry <= 7:
            return format_html(
                '<span style="color: orange;">{} days</span>', days_until_expiry
            )
        elif days_until_expiry <= 30:
            return format_html(
                '<span style="color: blue;">{} days</span>', days_until_expiry
            )
        return f"{days_until_expiry} days"

    expiry_status.short_description = "Days Until Expiry"


@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(ImportExportModelAdmin):
    """Enhanced SubscriptionPlan admin with import/export functionality"""

    resource_class = SubscriptionPlanResource

    list_display = (
        "id",
        "name",
        "duration_months",
        "price_display",
        "is_active",
        "created_at",
    )
    list_filter = ("is_active", "duration_months")
    search_fields = ("name", "description")
    readonly_fields = ("created_at", "updated_at")

    def price_display(self, obj):
        formatted_price = "{:,.2f}".format(float(obj.price))
        return format_html("₦{}", formatted_price)

    price_display.short_description = "Price"


# ==================== INLINE CLASSES ====================


class ModuleSubscriptionInline(admin.TabularInline):
    model = ModuleSubscription
    extra = 1
    readonly_fields = (
        "created_at",
        "days_remaining_display",
        "start_date",
        "end_date",
    )
    fields = (
        "module",
        "plan",
        "status",
        "module_overall_sub_days",
        "invoice_ref",
        "is_active",
        "start_date",
        "end_date",
        "days_remaining_display",
        "is_auto_renewal",
        "pending_balance",
    )

    def days_remaining_display(self, obj):
        if not obj.id:  # New object being created
            return "-"
        days = obj.days_remaining()
        if days < 0:
            return format_html('<span style="color: red;">Expired</span>')
        elif days < 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return f"{days} days"

    days_remaining_display.short_description = "Days Remaining"


# ==================== REMAINING ADMIN CLASSES ====================


@admin.register(PromotionalOffer)
class PromotionalOfferAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "discount_amount_display",
        "is_active",
        "start_date",
        "end_date",
    )
    list_filter = ("is_active", "start_date", "end_date")
    search_fields = ("name", "description")

    def discount_amount_display(self, obj):
        formatted_amount = "{:,.2f}".format(float(obj.discount_amount))
        return format_html("₦{}", formatted_amount)

    discount_amount_display.short_description = "Discount Amount"


@admin.register(Module)
class ModuleAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "code",
        "is_premium",
        "description",
        "requires_subscription",
        "created_at",
    )
    list_filter = ("is_premium", "requires_subscription")
    search_fields = ("name", "code", "description")
    readonly_fields = ("created_at",)
    ordering = ("name",)


@admin.register(SubscriptionAudit)
class SubscriptionAuditAdmin(admin.ModelAdmin):
    list_display = ("company", "action", "action_by", "created_at", "view_details")
    list_filter = ("action", "created_at")
    search_fields = ("company__company_name", "action", "action_by__email")
    readonly_fields = ("created_at",)

    def view_details(self, obj):
        return format_html('<pre style="font-size: 11px;">{}</pre>', obj.details)

    view_details.short_description = "Audit Details"

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(ExcludedPath)
class ExcludedPathAdmin(admin.ModelAdmin):
    list_display = ("path_pattern", "description", "is_active", "updated_at")
    list_filter = ("is_active",)
    search_fields = ("path_pattern", "description")
    readonly_fields = ("created_at", "updated_at")

    def save_model(self, request, obj, form, change):
        """Clear cache when a path is added or modified"""
        super().save_model(request, obj, form, change)
        from django.core.cache import cache

        cache.delete("excluded_path_patterns")
