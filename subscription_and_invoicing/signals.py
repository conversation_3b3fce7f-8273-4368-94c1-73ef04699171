import logging
from datetime import timedelta
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from django.contrib.auth import get_user_model

from requisition.models import Company
from subscription_and_invoicing.models import (
    CompanySubscription,
    ModuleSubscription,
    Module,
    AccessPath,
)

logger = logging.getLogger(__name__)
User = get_user_model()


@receiver(post_save, sender=Company)
def create_trial_subscription_on_company_creation(sender, instance, created, **kwargs):
    if not created:
        return

    logger.info(
        f"[Signal Triggered] Company '{instance}' created. Setting up trial subscriptions..."
    )

    trial_days = 12
    now = timezone.now()
    end_date = now + timedelta(days=trial_days)

    modules = Module.objects.all()
    if not modules.exists():
        logger.warning(
            f"[Warning] No modules found in system. Skipping subscription setup for company '{instance}'."
        )
        return

    created_by = (
        instance.user
        if hasattr(instance, "user") and isinstance(instance.user, User)
        else None
    )

    try:
        company_sub = CompanySubscription.objects.create(
            company=instance,
            access_type=AccessPath.TRIAL,
            status="trial",
            created_by=created_by,
        )
        # print("COMPANY_SUB :::::::", company_sub)

        module_subs = [
            ModuleSubscription(
                company_subscription=company_sub,
                module=module,
                start_date=now,
                end_date=end_date,
                status="active",
                is_active=True,
            )
            for module in modules
        ]
        # print("MODULE_SUB::::::::::", module_subs)

        ModuleSubscription.objects.bulk_create(module_subs)
        company_sub.update_overall_subscription_days()

        logger.info(
            f"[Success] Trial subscription and module access created for company '{instance}'"
        )

    except Exception as e:
        logger.error(
            f"[Error] Failed to create trial subscriptions for company '{instance}': {e}"
        )
