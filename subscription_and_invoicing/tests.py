# from django.urls import reverse
# from rest_framework import status
# from rest_framework.test import APITestCase, APIClient
# from unittest.mock import patch, MagicMock
# from django.contrib.auth import get_user_model
# from unittest import mock
#
# from core.models import PaystackPayment
# from requisition import models as req
# from sales_app import models as sales
# from stock_inventory import models as sti
# from subscription_and_invoicing import models as sub
# from invoicing import models as inv
# from performance_sales_metrics_dashboard import models as perf
# from decimal import Decimal
# from datetime import datetime
#
# from subscription_and_invoicing.apis import LibertyPayOnboardingMgr
#
# User = get_user_model()
#
#
# # class CreateInvoiceAPITestCase(APITestCase):
# #     def setUp(self):
# #         self.user = User.objects.create_user(
# #             email='<EMAIL>',
# #             password='testpass',
# #             first_name='Tobi',
# #             last_name='Taiwo'
# #         )
# #         self.client.force_authenticate(user=self.user)
# #
# #         self.company = req.Company.objects.create(
# #             company_name="Test Company",
# #             user=self.user
# #         )
# #
# #         self.branch = sti.Branch.objects.create(
# #             name="Test Branch",
# #             company=self.company,
# #             created_by=self.user,
# #             address="123 Test Street",
# #             vat=7.5
# #         )
# #
# #         self.customer = sales.Customer.objects.create(
# #             name="Test Customer",
# #             company=self.company,
# #             branch=self.branch,
# #             created_by=self.user,
# #         )
# #
# #         self.subscription_module1 = sub.SubscriptionType.objects.create(
# #             name="Test Subscription Module 1",
# #             price=1000
# #         )
# #         self.subscription_module2 = sub.SubscriptionType.objects.create(
# #             name="Test Subscription Module 2",
# #             price=2000
# #         )
# #
# #         self.sales_lead = perf.SalesLead.objects.create(
# #             name = "TestSalesLead",
# #             is_sales_lead = True
# #         )
# #
# #         self.sales_officer = perf.SalesOfficer.objects.create(
# #             name="TestSalesOfficer",
# #             referral_code="RO0FG6",
# #             is_sales_officer=True,
# #             sales_lead=self.sales_lead
# #         )
# #
# #     def test_create_invoice(self):
# #         url = reverse('create_invoice')
# #         data = {
# #             "company": self.company.id,
# #             "subscription_modules": [self.subscription_module1.id, self.subscription_module2.id],
# #             "duration": 8,
# #             "sales_officer_referral_code": "RO0FG6",
# #             "customer": self.customer.id
# #         }
# #
# #         response = self.client.post(url, data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
# #         self.assertIn("amount_payable", response.data)
# #         self.assertEqual(len(response.data["subscription_modules"]), 2)
# #         self.assertTrue(inv.Invoice.objects.filter(company=self.company).exists())
# #
# #     def test_create_invoice_with_invalid_referral_code(self):
# #         url = reverse('create_invoice')
# #         data = {
# #             "company": self.company.id,
# #             "subscription_modules": [self.subscription_module1.id, self.subscription_module2.id],
# #             "duration": 12,
# #             "sales_officer_referral_code": "invalid-referral-code",
# #             "customer": self.customer.id
# #         }
# #         response = self.client.post(url, data, format='json')
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #         self.assertIn("sales_officer_referral_code", response.data)
# #
# #     def test_create_invoice_with_invalid_uuid(self):
# #         url = reverse('create_invoice')
# #         data = {
# #             "company": self.company.id,
# #             "subscription_modules": [self.subscription_module1.id, self.subscription_module2.id],
# #             "duration": 12,
# #             "sales_officer_referral_code": "valid-referral-code",
# #             "customer": "invalid-uuid"
# #         }
# #
# #         response = self.client.post(url, data, format='json')
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #
# #
# #     def test_create_invoice_without_required_fields(self):
# #         url = reverse('create_invoice')
# #         data = {
# #             "company": self.company.id,
# #             "duration": 12,
# #             "sales_officer_referral_code": "valid-referral-code",
# #             "customer": self.customer.id
# #         }
# #         response = self.client.post(url, data, format='json')
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #         self.assertIn("subscription_modules", response.data)
# #
# #
# #
# # class ReceivePaymentAPITestCase(APITestCase):
# #
# #     def setUp(self):
# #         self.user = User.objects.create_user(
# #             email='<EMAIL>',
# #             password='testpass',
# #             first_name='Tobi',
# #             last_name='Taiwo'
# #         )
# #         self.client.force_authenticate(user=self.user)
# #
# #         self.company = req.Company.objects.create(
# #             company_name="Test Company",
# #             user=self.user
# #         )
# #
# #         self.branch = sti.Branch.objects.create(
# #             name="Test Branch",
# #             company=self.company,
# #             created_by=self.user,
# #             address="123 Test Street",
# #             vat=7.5
# #         )
# #
# #         self.invoice = sub.Invoice.objects.create(
# #             company=self.company,
# #             amount=Decimal('1000000.00'),
# #             start_date=datetime.now(),
# #             amount_due=Decimal('1000000.00'),
# #             settled_amount=Decimal('0.00'),
# #             payment_status='unpaid',
# #             sales_officer="TestSalesOfficer",
# #             batch_id="BATCH001"
# #         )
# #
# #         self.paystack_payment = PaystackPayment.objects.create(
# #             company=self.company,
# #             branch=self.branch,
# #             email="<EMAIL>",
# #             event="charge.success",
# #             payment_id="PAYMENT001",
# #             reference="BATCH001",
# #             amount=Decimal('900000.00'),
# #             status="success",
# #             confirmed=True,
# #             payer_name="Test Payer",
# #             channel="Card",
# #             gateway_response="Approved by Financial Institution",
# #             currency="NGN"
# #         )
# #
# #     def test_receive_payment_successful(self):
# #         url = reverse('receive_payment')
# #         data = {
# #             "batch_id": "BATCH001",
# #             "amount_paid": "900000.00"
# #         }
# #
# #         response = self.client.post(url, data, format='json')
# #         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
# #         self.assertEqual(response.data['payment_method'], 'Card')
# #         self.assertEqual(response.data['amount'], '900000.00')
# #         self.assertEqual(response.data['excess_payment'], 0)
# #         self.assertEqual(sub.Invoice.objects.get(id=self.invoice.id).payment_status, 'part_payment')
# #
# #     def test_receive_payment_excess(self):
# #         self.paystack_payment.amount = Decimal('1100000.00')
# #         self.paystack_payment.save()
# #
# #         url = reverse('receive_payment')
# #         data = {
# #             "batch_id": "BATCH001",
# #             "amount_paid": "1100000.00"
# #         }
# #
# #         response = self.client.post(url, data, format='json')
# #         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
# #         self.assertEqual(response.data['payment_method'], 'Card')
# #         self.assertEqual(response.data['excess_payment'], 100000.0)
# #         self.assertEqual(sub.Invoice.objects.get(id=self.invoice.id).payment_status, 'paid_excess')
# #         self.assertEqual(sub.Invoice.objects.get(id=self.invoice.id).amount_brought_forward, Decimal('100000.00'))
# #
# #     def test_receive_payment_invalid_batch_id(self):
# #         url = reverse('receive_payment')
# #         data = {
# #             "batch_id": "INVALIDBATCH",
# #             "amount_paid": "1000000.00"
# #         }
# #
# #         response = self.client.post(url, data, format='json')
# #         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
# #         self.assertEqual(response.data['error'], 'Invalid batch ID')
# #
# #     def test_receive_payment_no_amount_paid(self):
# #         url = reverse('receive_payment')
# #         data = {
# #             "batch_id": "BATCH001",
# #             "amount_paid": ""
# #         }
# #
# #         response = self.client.post(url, data, format='json')
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #         self.assertEqual(response.data['error'], 'Missing required fields')
# #
# #     def test_receive_payment_partial(self):
# #         self.paystack_payment.amount = Decimal('500000.00')
# #         self.paystack_payment.save()
# #
# #         url = reverse('receive_payment')
# #         data = {
# #             "batch_id": "BATCH001",
# #             "amount_paid": "500000.00"
# #         }
# #
# #         response = self.client.post(url, data, format='json')
# #         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
# #         self.assertEqual(response.data['payment_method'], 'Card')
# #         self.assertEqual(response.data['amount'], '500000.00')
# #         self.assertEqual(response.data['excess_payment'], 0)
# #         self.assertEqual(sub.Invoice.objects.get(id=self.invoice.id).payment_status, 'part_payment')
# #         self.assertEqual(sub.Invoice.objects.get(id=self.invoice.id).amount_due, Decimal('500000.00'))
# #
# #
# #
#
# # class CreateUserAndCompanyTestCase(APITestCase):
# #
# #     def setUp(self):
# #         self.client = APIClient()
# #         self.url = reverse('create-user-and-company')
# #         self.user_data = {
# #             "first_name": "Toby",
# #             "last_name": "White",
# #             "email": "<EMAIL>",
# #             "phone_number": "***********",
# #             "username": "tobywhite",
# #             "state": "Lagos",
# #             "lga": "Ikeja",
# #             "street": "123 Main Street",
# #             "nearest_landmark": "Near the Park",
# #             "company_name": "John's Company",
# #             "industry": "Retail",
# #             "size": 10,
# #             "channel": "Online",
# #             "cac_num": "12345678",
# #             "company_wallet_type": "MAIN",
# #         }
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.verify_user')
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.create_transaction_pin')
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.fetch_user_details')
# #     def test_create_user_and_company_success(self, mock_fetch_user_details, mock_create_transaction_pin, mock_verify_user, mock_sign_up):
# #         # Mock LibertyPay API responses for a successful flow
# #         mock_sign_up.return_value = {
# #             "status": "success",
# #             "status_code": 201,
# #             "response": {
# #                 "passcode": "123456",
# #                 "access": "fake-access-token"
# #             }
# #         }
# #         mock_verify_user.return_value = {"status": "success", "status_code": 200}
# #         mock_create_transaction_pin.return_value = {"status": "success", "status_code": 200}
# #         mock_fetch_user_details.return_value = {
# #             "status": "success",
# #             "status_code": 200,
# #             "response": {
# #                 "user_data": {
# #                     "id": "1",
# #                     "first_name": "Toby",
# #                     "last_name": "White",
# #                     "email": "<EMAIL>",
# #                     "phone_number": "***********",
# #                     "customer_id": "CUST123",
# #                     "is_active": True,
# #                     "type_of_user": "Business",
# #                     "bvn_number": "***********",
# #                     "street": "123 Main Street",
# #                     "state": "Lagos",
# #                     "lga": "Ikeja",
# #                     "nearest_landmark": "Near the Park"
# #                 },
# #                 "accounts_data": []
# #             }
# #         }
# #
# #         response = self.client.post(self.url, data=self.user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_200_OK)
# #         self.assertEqual(response.data['message'], "User created successfully")
# #
# #         user_instance = sub.UserCompanyDetails.objects.get(email=self.user_data['email'])
# #         self.assertEqual(user_instance.implementation_steps, "create_company")
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
# #     def test_create_user_and_company_failure(self, mock_sign_up):
# #         mock_sign_up.return_value = {
# #             "status": "failed",
# #             "status_code": 400,
# #             "response": {"error": "Invalid data"}
# #         }
# #
# #         response = self.client.post(self.url, data=self.user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
# #     def test_create_user_with_missing_fields(self, mock_sign_up):
# #         incomplete_user_data = self.user_data.copy()
# #         incomplete_user_data.pop('email')
# #
# #         response = self.client.post(self.url, data=incomplete_user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #         self.assertIn('email', response.data)
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
# #     def test_create_user_with_invalid_email_format(self, mock_sign_up):
# #         invalid_user_data = self.user_data.copy()
# #         invalid_user_data['email'] = 'invalid-email'
# #
# #         response = self.client.post(self.url, data=invalid_user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #         self.assertIn('email', response.data)
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
# #     def test_create_user_with_duplicate_email(self, mock_sign_up):
# #         sub.UserCompanyDetails.objects.create(**self.user_data)
# #
# #         response = self.client.post(self.url, data=self.user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #         self.assertIn('email', response.data)
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.create_transaction_pin')
# #     def test_create_user_and_company_transaction_pin_failure(self, mock_create_transaction_pin, mock_sign_up):
# #         mock_sign_up.return_value = {"status": "success", "status_code": 201, "response": {"access": "fake-access-token"}}
# #         mock_create_transaction_pin.return_value = {
# #             "status": "failed", "status_code": 400, "response": {"error": "PIN creation failed"}
# #         }
# #
# #         response = self.client.post(self.url, data=self.user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #         self.assertIn('error', response.data)
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
# #     def test_partial_data_saved_on_failure(self, mock_sign_up):
# #         mock_sign_up.return_value = {
# #             "status": "failed",
# #             "status_code": 400,
# #             "response": {"error": "Invalid data"}
# #         }
# #
# #         response = self.client.post(self.url, data=self.user_data, format='json')
# #
# #         self.assertFalse(sub.UserCompanyDetails.objects.filter(email=self.user_data['email']).exists())
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.create_transaction_pin')
# #     def test_create_transaction_pin_invalid_length(self, mock_create_transaction_pin):
# #         mock_create_transaction_pin.return_value = {
# #             "status": "failed", "status_code": 400, "response": {"error": "PIN must be 6 digits"}
# #         }
# #
# #         response = self.client.post(self.url, data=self.user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #         self.assertIn('PIN must be 6 digits', response.data['error'])
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
# #     def test_create_user_with_invalid_phone_number(self, mock_sign_up):
# #         mock_sign_up.return_value = {
# #             "status": "success",
# #             "status_code": 201,
# #             "response": {
# #                 "passcode": "123456",
# #                 "access": "fake-access-token"
# #             }
# #         }
# #
# #         invalid_user_data = self.user_data.copy()
# #         invalid_user_data['phone_number'] = 'invalid-phone'
# #
# #         response = self.client.post(self.url, data=invalid_user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
# #         self.assertIn('phone_number', response.data)
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.fetch_user_details')
# #     def test_fetch_user_details_failure(self, mock_fetch_user_details):
# #         mock_fetch_user_details.return_value = {
# #             "status": "failed", "status_code": 500, "response": {"error": "User not found"}
# #         }
# #
# #         response = self.client.post(self.url, data=self.user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
# #         self.assertIn('error', response.data)
# #
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.verify_user')
# #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.create_company')
# #     def test_company_creation_success_after_user(self, mock_create_company, mock_verify_user, mock_sign_up):
# #         mock_sign_up.return_value = {"status": "success", "status_code": 201, "response": {"access": "fake-access-token"}}
# #         mock_verify_user.return_value = {"status": "success", "status_code": 200}
# #         mock_create_company.return_value = {"status": "success", "status_code": 201, "response": {"company_id": "COMP123"}}
# #
# #         response = self.client.post(self.url, data=self.user_data, format='json')
# #
# #         self.assertEqual(response.status_code, status.HTTP_200_OK)
# #         self.assertEqual(response.data['message'], "Company created successfully")
# #
# #         user_instance = sub.UserCompanyDetails.objects.get(email=self.user_data['email'])
# #         self.assertEqual(user_instance.company_name, self.user_data['company_name'])
# #         self.assertEqual(user_instance.company_wallet_type, self.user_data['company_wallet_type'])
#
#
#
# class CreateUserAndCompanyTestCase(APITestCase):
#     def setUp(self):
#         self.client = APIClient()
#         self.url = reverse('create-user-and-company')
#         self.user_data = {
#             "first_name": "Toby",
#             "last_name": "White",
#             "email": "<EMAIL>",
#             "phone_number": "***********",
#             "username": "tobywhite",
#             "state": "Lagos",
#             "lga": "Ikeja",
#             "street": "123 Main Street",
#             "nearest_landmark": "Near the Park",
#             "company_name": "John's Company",
#             "industry": "Retail",
#             "size": 10,
#             "channel": "Online",
#             "cac_num": "12345678",
#             "company_wallet": "MAIN",  # Ensure this is consistent with your model
#         }
#
#     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
#     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.verify_user')
#     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.create_transaction_pin')
#     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.fetch_user_details')
#     def test_create_user_and_company_success(self, mock_fetch_user_details, mock_create_transaction_pin, mock_verify_user, mock_sign_up):
#         mock_sign_up.return_value = {
#             "status": "success",
#             "status_code": 201,
#             "response": {
#                 "passcode": "123456",
#                 "access": "fake-access-token"
#             }
#         }
#         mock_verify_user.return_value = {"status": "success", "status_code": 200}
#         mock_create_transaction_pin.return_value = {"status": "success", "status_code": 200}
#         mock_fetch_user_details.return_value = {
#             "status": "success",
#             "status_code": 200,
#             "response": {
#                 "user_data": {
#                     "id": "1",
#                     "first_name": "Toby",
#                     "last_name": "White",
#                     "email": "<EMAIL>",
#                     "phone_number": "***********",
#                     "customer_id": "CUST123",
#                     "is_active": True,
#                     "type_of_user": "Business",
#                     "bvn_number": "***********",
#                     "street": "123 Main Street",
#                     "state": "Lagos",
#                     "lga": "Ikeja",
#                     "nearest_landmark": "Near the Park"
#                 },
#                 "accounts_data": []
#             }
#         }
#
#         response = self.client.post(self.url, data=self.user_data, format='json')
#
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data['message'], "User created successfully")
#
#         user_instance = sub.UserCompanyDetails.objects.get(email=self.user_data['email'])
#         self.assertEqual(user_instance.company_name, self.user_data['company_name'])
#         self.assertEqual(user_instance.company_wallet, self.user_data['company_wallet'])  # Changed to company_wallet
#
#     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
#     def test_create_user_and_company_failure(self, mock_sign_up):
#         mock_sign_up.return_value = {
#             "status": "failed",
#             "status_code": 400,
#             "response": {"error": "Invalid data"}
#         }
#
#         response = self.client.post(self.url, data=self.user_data, format='json')
#
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertIn('error', response.data)
#
#     def test_create_user_with_missing_fields(self):
#         incomplete_user_data = self.user_data.copy()
#         incomplete_user_data.pop('email')
#
#         response = self.client.post(self.url, data=incomplete_user_data, format='json')
#
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertIn('email', response.data)
#
#     def test_create_user_with_invalid_email_format(self):
#         invalid_user_data = self.user_data.copy()
#         invalid_user_data['email'] = 'invalid-email'
#
#         response = self.client.post(self.url, data=invalid_user_data, format='json')
#
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertIn('email', response.data)
#
#
#     # def test_create_user_with_duplicate_email(self):
#     #     sub.UserCompanyDetails.objects.create(**self.user_data)
#     #
#     #     response = self.client.post(self.url, data=self.user_data, format='json')
#     #
#     #     self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#     #     self.assertIn('email', response.data)
#
#     # @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
#     # @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.create_transaction_pin')
#     # def test_create_user_and_company_transaction_pin_failure(self, mock_create_transaction_pin, mock_sign_up):
#     #     mock_sign_up.return_value = {"status": "success", "status_code": 201, "response": {"access": "fake-access-token"}}
#     #     mock_create_transaction_pin.return_value = {"status": "failed", "status_code": 400, "response": {"error": "PIN creation failed"}}
#     #
#     #     response = self.client.post(self.url, data=self.user_data, format='json')
#     #
#     #     self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#     #     self.assertIn('error', response.data)
#
#     # def test_partial_data_saved_on_failure(self):
#     #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
#     #     def inner(mock_sign_up):
#     #         mock_sign_up.return_value = {
#     #             "status": "failed",
#     #             "status_code": 400,
#     #             "response": {"error": "Invalid data"}
#     #         }
#     #
#     #         response = self.client.post(self.url, data=self.user_data, format='json')
#     #
#     #         self.assertFalse(sub.UserCompanyDetails.objects.filter(email=self.user_data['email']).exists())
#     #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#     #
#     #     inner()
#
#     # @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.create_transaction_pin')
#     # def test_create_transaction_pin_invalid_length(self, mock_create_transaction_pin):
#     #     mock_create_transaction_pin.return_value = {
#     #         "status": "failed", "status_code": 400, "response": {"error": "PIN must be 6 digits"}
#     #     }
#     #
#     #     response = self.client.post(self.url, data=self.user_data, format='json')
#     #
#     #     self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#     #     self.assertIn('PIN must be 6 digits', response.data['error'])
#
#     # def test_create_user_with_invalid_phone_number(self):
#     #     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
#     #     def inner(mock_sign_up):
#     #         mock_sign_up.return_value = {
#     #             "status": "success",
#     #             "status_code": 201,
#     #             "response": {
#     #                 "passcode": "123456",
#     #                 "access": "fake-access-token"
#     #             }
#     #         }
#     #
#     #         invalid_user_data = self.user_data.copy()
#     #         invalid_user_data['phone_number'] = 'invalid-phone'
#     #
#     #         response = self.client.post(self.url, data=invalid_user_data, format='json')
#     #
#     #         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#     #         self.assertIn('phone_number', response.data)
#     #
#     #     inner()
#
#     # @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.fetch_user_details')
#     # def test_fetch_user_details_failure(self, mock_fetch_user_details):
#     #     mock_fetch_user_details.return_value = {
#     #         "status": "failed", "status_code": 500, "response": {"error": "User not found"}
#     #     }
#     #
#     #     response = self.client.post(self.url, data=self.user_data, format='json')
#     #
#     #     self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
#     #     self.assertIn('error', response.data)
#
#     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.sign_up')
#     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.verify_user')
#     @mock.patch('subscription_and_invoicing.apis.LibertyPayOnboardingMgr.create_company')
#     def test_company_creation_success_after_user(self, mock_create_company, mock_verify_user, mock_sign_up):
#         mock_sign_up.return_value = {"status": "success", "status_code": 201,
#                                      "response": {"access": "fake-access-token"}}
#         mock_verify_user.return_value = {"status": "success", "status_code": 200}
#         mock_create_company.return_value = {"status": "success", "status_code": 201,
#                                             "response": {"company_id": "COMP123"}}
#
#         response = self.client.post(self.url, data=self.user_data, format='json')
#
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data['message'], "Company created successfully")
#
#         user_instance = sub.UserCompanyDetails.objects.get(email=self.user_data['email'])
#         self.assertEqual(user_instance.company_name, self.user_data['company_name'])
#         self.assertEqual(user_instance.company_wallet, self.user_data['company_wallet'])
