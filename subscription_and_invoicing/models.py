from datetime import datetime, timedelta, date
from pprint import pprint

from pprint import pprint

# from django.contrib.auth.models import User
from django.utils import timezone
import random
import string
from django.db.models import Sum
import uuid
import json
import json
from django.conf import settings
from django.db import models
from django.contrib.auth import get_user_model
from rest_framework.views import APIView

from core.models import BaseModel
from helpers.reusable_functions import (
    generate_transaction_pin,
    generate_password,
    get_account_details,
)
from performance_sales_metrics_dashboard import models as perf

from datetime import timedelta
from django.utils import timezone
from decimal import Decimal
from invoicing import models as inv
from requisition.models import Company
from sales_app.models import Customer
from requisition import models as req
import logging
from subscription_and_invoicing.apis import LibertyPayOnboardingMgr
from django.db.models import Sum, F, ExpressionWrapper, DurationField

# from requisition.models import Company
User = get_user_model()


class SubscriptionModule(models.Model):
    pass


class SubscriptionType(models.Model):
    pass


class PromotionalOffer(models.Model):
    """Tracks promotional subscription offers"""

    name = models.CharField(max_length=100)
    description = models.TextField()
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
    is_active = models.BooleanField(default=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} ({self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')})"


class SubscriptionPlan(models.Model):
    """Defines available subscription plans"""

    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    duration_months = models.IntegerField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.duration_months} month(s) - ₦{self.price}"

    class Meta:
        ordering = ["price"]


class Module(models.Model):
    """Available system modules that can be subscribed to"""

    name = models.CharField(max_length=100)
    code = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    is_premium = models.BooleanField(default=True)
    requires_subscription = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class AccessPath:
    """Define the three distinct access paths for Paybox360"""

    PAID = "paid"
    TRIAL = "trial"
    BYPASS = "bypass"
    TOKEN = "token"

    CHOICES = (
        (PAID, "Paid Subscription"),
        (TRIAL, "Free Trial"),
        (BYPASS, "Free Merchant"),
        (TOKEN, "Token-Based Access"),
    )


class TokenStatus:
    """Define token status options"""

    ACTIVE = "active"
    INSUFFICIENT = "insufficient"
    INACTIVE = "inactive"

    CHOICES = (
        (ACTIVE, "Active"),
        (INSUFFICIENT, "Insufficient Balance"),
        (INACTIVE, "Inactive"),
    )


class CompanySubscription(models.Model):
    """Tracks company subscriptions and their status"""

    SUBSCRIPTION_STATUS = (
        ("active", "Active"),
        ("inactive", "Inactive"),
        ("trial", "Trial"),
        ("expired", "Expired"),
        ("cancelled", "Cancelled"),
    )

    company = models.ForeignKey("requisition.Company", on_delete=models.CASCADE)
    modules = models.ManyToManyField(Module, through="ModuleSubscription")
    access_type = models.CharField(
        max_length=20,
        choices=AccessPath.CHOICES,
        help_text="Determines how the company accesses Paybox360 modules",
    )
    plan = models.ForeignKey(
        SubscriptionPlan, on_delete=models.SET_NULL, null=True, blank=True
    )
    invoice_ref = models.CharField(max_length=100, blank=True, null=True)
    overall_sub_days = models.CharField(max_length=225, blank=True, null=True)
    status = models.CharField(max_length=20, choices=SUBSCRIPTION_STATUS)
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="created_subscriptions"
    )
    token_previous_balance = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Previous token balance before last transaction",
    )
    token_current_balance = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Current available token balance",
    )
    token_status = models.CharField(
        max_length=20,
        choices=TokenStatus.CHOICES,
        default=TokenStatus.ACTIVE,
        help_text="Current status of token balance",
    )
    is_upgrade = models.BooleanField(default=False)
    existing_subscription = models.ForeignKey(
        "self",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="upgraded_subscriptions",
    )
    pos_included = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.company.company_name} Subscription"

    # Add this method to your CompanySubscription model

    def update_overall_subscription_days(self):
        """
        Calculate total subscription days across all modules for this company subscription.
        Safely handles None dates to prevent TypeError.
        """
        print(f"Updating overall subscription days for company subscription: {self}")

        # Get all module subscriptions for this company subscription
        module_subscriptions = self.modulesubscription_set.all()

        total_days = 0
        processed_subscriptions = 0

        for subscription in module_subscriptions:
            # Safely handle None dates
            if (
                subscription.end_date is not None
                and subscription.start_date is not None
            ):
                try:
                    delta = (subscription.end_date - subscription.start_date).days
                    total_days += max(0, delta)
                    processed_subscriptions += 1
                    print(f"Added {delta} days from module {subscription.module.name}")
                except (TypeError, AttributeError) as e:
                    print(
                        f"Error calculating date difference for subscription {subscription.id}: {e}"
                    )
                    print(
                        f"Start date: {subscription.start_date}, End date: {subscription.end_date}"
                    )
                    continue
            else:
                # Handle subscriptions with None dates (e.g., token subscriptions)
                print(
                    f"Skipping subscription {subscription.id} with None dates: "
                    f"start={subscription.start_date}, end={subscription.end_date}"
                )

                # For token subscriptions, we might want to add a default large value
                if hasattr(self, "access_type") and self.access_type == "token":
                    # Token subscriptions are effectively unlimited
                    default_unlimited_days = 36500  # 100 years
                    total_days += default_unlimited_days
                    processed_subscriptions += 1
                    print(
                        f"Added {default_unlimited_days} days for token subscription {subscription.id}"
                    )
                elif subscription.start_date is not None:
                    # Has start date but no end date - might be an error for regular subscriptions
                    print(
                        f"Warning: Subscription {subscription.id} has start_date but no end_date"
                    )
                    # You could add a default duration here if needed
                    continue
                else:
                    # Both dates are None - likely an error condition
                    print(
                        f"Warning: Subscription {subscription.id} has both dates as None"
                    )
                    continue

        # Update the overall subscription days field if it exists
        if hasattr(self, "overall_subscription_days"):
            self.overall_subscription_days = str(total_days)
            print(f"Updated overall_subscription_days to: {total_days}")

            # Use update to avoid triggering save() and potential recursion
            CompanySubscription.objects.filter(pk=self.pk).update(
                overall_subscription_days=str(total_days)
            )
        else:
            print(
                "CompanySubscription model doesn't have overall_subscription_days field"
            )

        print(
            f"Total subscription days calculated: {total_days} from {processed_subscriptions} subscriptions"
        )
        return total_days

    def update_status(self):
        """
        Set company subscription to 'active' if it has at least one active module subscription.
        Otherwise set to 'inactive' (unless it's trial or bypass, which should stay as is).
        """
        if self.access_type in [AccessPath.BYPASS, AccessPath.TRIAL, AccessPath.TOKEN]:
            return  # Don't override trial or bypass status

        # Check both status and is_active for module subscriptions
        active_modules = self.modulesubscription_set.filter(
            status="active", is_active=True, end_date__gt=timezone.now()
        )

        new_status = "active" if active_modules.exists() else "inactive"

        if self.status != new_status:
            self.status = new_status
            print(
                f"Company subscription status updated to {new_status} for {self.company.company_name}"
            )
            # Use update instead of save to avoid potential circular updates
            CompanySubscription.objects.filter(pk=self.pk).update(status=new_status)

    @classmethod
    def process_bulk_token_deductions(cls, get_company_transaction_counts_by_date):
        """
        Process bulk token deductions for all companies based on their transaction counts
        Designed to run at midnight for daily processing

        Args:
            get_company_transaction_counts_by_date: Function that returns company transaction counts
                                        Example: {"company_id_1": 12, "company_id_2": 0}

        Returns:
            dict: Processing results with success/failure details
        """
        # Call your function to get transaction counts for all companies
        transaction_data = get_company_transaction_counts_by_date()

        results = {
            "total_companies": len(transaction_data),
            "processed": 0,
            "successful": 0,
            "failed": 0,
            "skipped": 0,
            "total_tokens_deducted": 0,
            "companies_with_insufficient_balance": [],
            "errors": [],
        }

        for company_id, transaction_count in transaction_data.items():
            try:
                # Skip companies with no transactions
                if transaction_count <= 0:
                    results["skipped"] += 1
                    continue

                # Find the token-based subscription for this company
                try:
                    subscription = cls.objects.get(
                        company_id=company_id, access_type=AccessPath.TOKEN
                    )
                except cls.DoesNotExist:
                    results["failed"] += 1
                    results["errors"].append(
                        f"Company {company_id}: No token subscription found"
                    )
                    continue
                except cls.MultipleObjectsReturned:
                    # Get the most recent token subscription if multiple exist
                    subscription = (
                        cls.objects.filter(
                            company_id=company_id, access_type=AccessPath.TOKEN
                        )
                        .order_by("-created_at")
                        .first()
                    )

                # Check if sufficient balance exists
                tokens_to_deduct = transaction_count
                if subscription.token_current_balance < tokens_to_deduct:
                    subscription.token_status = TokenStatus.INSUFFICIENT
                    subscription.save(update_fields=["token_status"])

                    results["failed"] += 1
                    results["companies_with_insufficient_balance"].append(
                        {
                            "company_id": company_id,
                            "company_name": subscription.company.company_name,
                            "required_tokens": tokens_to_deduct,
                            "current_balance": float(
                                subscription.token_current_balance
                            ),
                            "transaction_count": transaction_count,
                        }
                    )
                    continue

                # Store previous balance before deduction
                subscription.token_previous_balance = subscription.token_current_balance

                # Deduct tokens from current balance
                subscription.token_current_balance -= tokens_to_deduct

                # Update token status based on remaining balance
                if subscription.token_current_balance <= 0:
                    subscription.token_status = TokenStatus.INSUFFICIENT
                else:
                    subscription.token_status = TokenStatus.ACTIVE

                # Save all updated fields to database
                subscription.save(
                    update_fields=[
                        "token_previous_balance",
                        "token_current_balance",
                        "token_status",
                    ]
                )

                # Create transaction record for audit trail (optional)
                # try:
                #     TokenTransaction.objects.create(
                #         company_subscription=subscription,
                #         transaction_type="usage",
                #         amount=tokens_to_deduct,
                #         previous_balance=subscription.token_previous_balance,
                #         new_balance=subscription.token_current_balance,
                #         description=f"Daily bulk deduction: {transaction_count} transactions",
                #     )
                # except:
                #     # TokenTransaction model might not exist or other DB issues
                #     pass

                # Update success counters
                results["processed"] += 1
                results["successful"] += 1
                results["total_tokens_deducted"] += tokens_to_deduct

            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Company {company_id}: {str(e)}")

        return results


# class TokenTransaction(models.Model):
#     """Track token transactions for audit and reporting"""
#
#     TRANSACTION_TYPES = (
#         ("purchase", "Token Purchase"),
#         ("usage", "Token Usage"),
#         ("refund", "Token Refund"),
#         ("adjustment", "Balance Adjustment"),
#     )
#
#     company_subscription = models.ForeignKey(
#         CompanySubscription, on_delete=models.CASCADE, related_name="token_transactions"
#     )
#     transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
#     amount = models.DecimalField(max_digits=10, decimal_places=2)
#     previous_balance = models.DecimalField(max_digits=10, decimal_places=2)
#     new_balance = models.DecimalField(max_digits=10, decimal_places=2)
#     description = models.TextField(blank=True)
#     reference = models.CharField(max_length=100, blank=True, null=True)
#     created_by = models.ForeignKey(
#         User, on_delete=models.SET_NULL, null=True, related_name="token_transactions"
#     )
#     created_at = models.DateTimeField(auto_now_add=True)
#
#     class Meta:
#         ordering = ["-created_at"]
#
#     def __str__(self):
#         return f"{self.company_subscription.company.company_name} - {self.transaction_type} - {self.amount}"
#


class ModuleSubscription(models.Model):
    STATUS_CHOICES = [
        ("not_started", "Not Started"),
        ("active", "Active"),
        ("expired", "Expired"),
    ]

    """Links modules to company subscriptions with specific access controls"""
    company_subscription = models.ForeignKey(
        CompanySubscription, on_delete=models.CASCADE
    )
    plan = models.ForeignKey(
        SubscriptionPlan, on_delete=models.PROTECT, null=True, blank=True
    )
    module = models.ForeignKey(Module, on_delete=models.CASCADE)
    is_auto_renewal = models.BooleanField(default=False)
    promotional_offer = models.ForeignKey(
        PromotionalOffer, on_delete=models.SET_NULL, null=True, blank=True
    )
    pending_balance = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Balance to be paid after successful onboarding",
    )
    invoice_ref = models.CharField(max_length=100, blank=True, null=True)
    module_overall_sub_days = models.CharField(max_length=225, blank=True, null=True)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default="not_started"
    )
    is_active = models.BooleanField(default=False)
    start_date = models.DateTimeField(blank=True, null=True)
    end_date = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # class Meta:
    #     unique_together = ("company_subscription", "module")

    def update_module_overall_days(self):
        """
        Calculate total subscription days for this module, safely handling None dates
        """
        print(
            f"Updating total subscription days for module: {self.module}, company: {self.company_subscription}"
        )

        subscriptions = ModuleSubscription.objects.filter(
            module=self.module, company_subscription=self.company_subscription
        )

        total_days = 0
        for sub in subscriptions:
            # Safely handle None dates to prevent TypeError
            if sub.end_date is not None and sub.start_date is not None:
                try:
                    delta = (sub.end_date - sub.start_date).days
                    total_days += max(0, delta)
                    print(f"Added {delta} days for subscription {sub.id}")
                except (TypeError, AttributeError) as e:
                    print(
                        f"Error calculating date difference for subscription {sub.id}: {e}"
                    )
                    print(f"Start date: {sub.start_date}, End date: {sub.end_date}")
                    continue
            else:
                # Log subscriptions with None dates for debugging
                print(
                    f"Skipping subscription {sub.id} with None dates: start={sub.start_date}, end={sub.end_date}"
                )

                # For token subscriptions or unlimited subscriptions with None dates
                # Check if this is a token subscription by looking at company_subscription access_type
                if (
                    hasattr(sub.company_subscription, "access_type")
                    and sub.company_subscription.access_type == "token"
                ):
                    # For token subscriptions, use a large number to represent unlimited access
                    default_unlimited_days = 36500  # 100 years
                    total_days += default_unlimited_days
                    print(
                        f"Added default unlimited days ({default_unlimited_days}) for token subscription {sub.id}"
                    )
                elif sub.start_date is not None:
                    # If we have start date but no end date for regular subscriptions
                    # This might be an error condition, but we'll handle it gracefully
                    print(
                        f"Warning: Regular subscription {sub.id} has start_date but no end_date"
                    )
                    # You could either skip it or add a default duration
                    # For now, we'll skip it
                    continue
                else:
                    # Both dates are None - this is likely an error condition
                    print(
                        f"Warning: Subscription {sub.id} has both start_date and end_date as None"
                    )
                    continue

        self.module_overall_sub_days = str(total_days)
        print(f"Final module_overall_sub_days value: {self.module_overall_sub_days}")

        # Use update to avoid triggering save() and potential recursion
        ModuleSubscription.objects.filter(pk=self.pk).update(
            module_overall_sub_days=str(total_days)
        )

        # Update the instance attribute to match database
        # This ensures the instance reflects the database state
        self._state.db = "default"  # Ensure it knows it's saved

        if hasattr(self.company_subscription, "update_overall_subscription_days"):
            self.company_subscription.update_overall_subscription_days()

        return total_days

    def mark_expired(self):
        """Mark subscription as expired if past end date"""
        now = timezone.now()
        if self.status == "not_started":
            return False  # Nothing to do for not started subscriptions

        # Only check expiration if we have an end_date
        if self.status != "expired" and self.end_date and self.end_date < now:
            print(f"Marking subscription as expired: {self}")

            # Use update method to avoid recursion
            ModuleSubscription.objects.filter(pk=self.pk).update(
                status="expired", is_active=False
            )

            # Update instance attributes to match DB
            self.status = "expired"
            self.is_active = False

            # Update parent company subscription
            if hasattr(self.company_subscription, "update_status"):
                self.company_subscription.update_status()

            return True  # Changes were made

        return False  # No changes needed

    def days_remaining(self):
        """Calculate days remaining, handling None end_date gracefully"""
        if not self.is_active:
            return 0

        # For token subscriptions or subscriptions without end_date, return a large number
        if self.end_date is None:
            return 999999  # Effectively unlimited

        now = timezone.now()
        return max(0, (self.end_date - now).days)

    def update_status(self, save=True):
        """
        Updates the subscription status and is_active field based on the start_date and end_date.
        Handles None dates gracefully for token subscriptions.
        """
        now = timezone.now()

        # Handle cases where start_date is None
        if self.start_date is None:
            # For new subscriptions, set start_date to now
            self.start_date = now

        # Determine status based on dates
        if self.start_date <= now:
            # Subscription has started
            if self.end_date is None:
                # No end date means unlimited (token subscription)
                new_status = "active"
            elif self.end_date >= now:
                # Has end date and hasn't expired
                new_status = "active"
            else:
                # Has expired
                new_status = "expired"
        else:
            # Start date is in the future
            new_status = "not_started"

        new_is_active = new_status == "active"

        # Only proceed with updates if there are changes
        if self.status != new_status or self.is_active != new_is_active:
            self.status = new_status
            self.is_active = new_is_active
            print(
                f"Status updated to {new_status}, is_active set to {new_is_active} for {self}"
            )

            if save:
                # Using update instead of save to avoid potential infinite recursion
                ModuleSubscription.objects.filter(pk=self.pk).update(
                    status=new_status, is_active=new_is_active
                )

        # Always sync the parent company subscription status
        if hasattr(self.company_subscription, "update_status"):
            self.company_subscription.update_status()

    def save(self, *args, **kwargs):
        """
        Custom save method with proper date handling and recursion prevention
        """
        update_fields = kwargs.get("update_fields")
        now = timezone.now()

        # Set start_date if not provided
        if not self.start_date:
            self.start_date = now

        # Set end_date for regular subscriptions with plans (not for token subscriptions)
        if not self.end_date and self.plan:
            duration_months = self.plan.duration_months
            start = self.start_date

            # Calculate end date based on duration
            if start.month + duration_months <= 12:
                end_month = start.month + duration_months
                end_year = start.year
            else:
                years_to_add = (start.month + duration_months - 1) // 12
                end_month = (start.month + duration_months - 1) % 12 + 1
                end_year = start.year + years_to_add

            try:
                self.end_date = start.replace(
                    year=end_year, month=end_month, day=start.day
                )
            except ValueError:
                # Handle day overflow (e.g., Jan 31 + 1 month should be Feb 28/29)
                if end_month == 2:
                    end_day = (
                        29
                        if (
                            (end_year % 4 == 0 and end_year % 100 != 0)
                            or (end_year % 400 == 0)
                        )
                        else 28
                    )
                elif end_month in [4, 6, 9, 11]:
                    end_day = 30
                else:
                    end_day = 31
                self.end_date = start.replace(
                    year=end_year, month=end_month, day=end_day
                )

        # Call the original save
        super().save(*args, **kwargs)

        # Update status only if we're not already updating specific fields
        # This prevents recursion issues
        if not update_fields or not all(
            field in update_fields for field in ["status", "is_active"]
        ):
            self.update_status(save=True)

        # Update subscription days only if not explicitly skipped
        if not update_fields or "module_overall_sub_days" not in update_fields:
            self.update_module_overall_days()


class SubscriptionAudit(models.Model):
    """Tracks all subscription-related events for auditing"""

    company = models.ForeignKey("requisition.Company", on_delete=models.CASCADE)
    subscription = models.ForeignKey(
        CompanySubscription, on_delete=models.CASCADE, null=True
    )
    action = models.CharField(max_length=50)
    action_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    details = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.action} by {self.action_by} for {self.company} on {self.created_at.strftime('%Y-%m-%d %H:%M:%S')}"

    class Meta:
        ordering = ["-created_at"]


class Invoice(models.Model):
    """Tracks billing information for company subscriptions"""

    PAYMENT_STATUS_CHOICES = [
        ("paid", "Paid"),
        ("part_payment", "Part Payment"),
        ("paid_excess", "Paid in Excess"),
        ("unpaid", "Unpaid"),
    ]

    # Core relationships
    company = models.ForeignKey(
        "requisition.Company",
        on_delete=models.CASCADE,
        related_name="subscription_invoices",
    )
    company_subscription = models.ForeignKey(
        "subscription_and_invoicing.CompanySubscription",
        on_delete=models.SET_NULL,
        related_name="invoices",
        null=True,
        blank=True,
    )
    leads = models.ForeignKey(
        "performance_sales_metrics_dashboard.Lead",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="subscription_invoices",
    )

    # Invoice details
    invoice_reference = models.CharField(
        max_length=255, default=uuid.uuid4, unique=True, editable=False
    )
    batch_id = models.CharField(
        max_length=10, unique=True, editable=False, null=True, blank=True
    )

    # Financial fields
    total_amount = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    balance_due = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    amount_brought_forward = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    settled_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Subscription details
    subscribed_modules = models.ManyToManyField(
        "subscription_and_invoicing.Module",
        through="subscription_and_invoicing.InvoiceModuleDetail",
        related_name="invoices",
    )
    start_date = models.DateTimeField()
    expiry_date = models.DateTimeField(null=True, blank=True)

    # Status fields
    is_active = models.BooleanField(default=False)
    payment_status = models.CharField(
        max_length=15, choices=PAYMENT_STATUS_CHOICES, default="unpaid"
    )
    used_excess_payment = models.BooleanField(default=False)

    # Attribution
    sales_officer = models.ForeignKey(
        "performance_sales_metrics_dashboard.SalesOfficer",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="generated_invoices",
    )
    package_description = models.CharField(max_length=255, blank=True, null=True)
    promo_applied = models.BooleanField(default=False)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_invoices",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["invoice_reference"]),
            models.Index(fields=["batch_id"]),
            models.Index(fields=["payment_status"]),
        ]

    def __str__(self):
        return f"Invoice {self.invoice_reference} - {self.company.company_name}"

    @property
    def is_due_for_renewal(self):
        if not self.expiry_date:
            return False
        return self.is_active and self.expiry_date <= timezone.now() + timedelta(days=7)

    @staticmethod
    def generate_batch_id(length=5):
        return "".join(random.choices(string.ascii_uppercase + string.digits, k=length))

    def recalculate_amount_due(self):
        """
        Recalculates the total amount due for the invoice based on subscribed modules.
        """
        total_amount = sum(
            detail.total_price for detail in self.invoicemoduledetail_set.all()
        )

        self.total_amount = total_amount
        self.balance_due = max(0, total_amount - self.settled_amount)
        self.save()


class InvoiceModuleDetail(models.Model):
    """Tracks individual module details for each invoice"""

    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
    module = models.ForeignKey(Module, on_delete=models.CASCADE)
    quantity = models.IntegerField(default=1, help_text="Number of months")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    plan_label = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Readable label of the subscription plan used (e.g., Basic - 6 months)",
    )

    class Meta:
        unique_together = ["invoice", "module"]

    def save(self, *args, **kwargs):
        """Calculate total price before saving"""
        discount_decimal = Decimal(self.discount) / Decimal(
            100
        )  # Convert discount to Decimal
        self.total_price = (self.unit_price * self.quantity) * (
            Decimal(1) - discount_decimal
        )
        super().save(*args, **kwargs)


class InvoiceAudit(models.Model):
    """Tracks all changes to invoices for auditing"""

    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
    action = models.CharField(max_length=50)
    action_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    previous_state = models.JSONField(null=True)
    new_state = models.JSONField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]


class InvoiceTransaction(models.Model):
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=50)
    transaction_date = models.DateTimeField(auto_now_add=True)
    is_processed = models.BooleanField(default=False)
    excess_payment = models.FloatField(default=0)
    invoice_reference = models.CharField(max_length=255)

    def __str__(self):
        return self.invoice.invoice_reference


class Commission(models.Model):
    invoice_transaction = models.ForeignKey(
        InvoiceTransaction, on_delete=models.CASCADE
    )
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    role = models.ForeignKey(
        "CommissionStructure", on_delete=models.CASCADE, null=True, blank=True
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    paid = models.BooleanField(default=False)


class CommissionStructure(models.Model):
    role = models.CharField(max_length=50, unique=True)
    percentage = models.DecimalField(max_digits=5, decimal_places=4)

    def __str__(self):
        return f"{self.role}: {self.percentage}"


class UserCompanyDetails(BaseModel):
    STEP_CHOICES = [
        ("user_creation", "User_Creation"),
        ("verify_account", "Verify_Account"),
        ("create_transaction_pin", "Create_Transaction_Pin"),
        ("create_paybox_user", "Create_Paybox_User"),
        ("create_company", "Create_Company"),
    ]
    first_name = models.CharField(max_length=225, blank=True, null=True)
    last_name = models.CharField(max_length=225, blank=True, null=True)
    email = models.CharField(max_length=225, blank=True, null=True)
    phone_number = models.CharField(max_length=13, blank=True, null=True)
    username = models.CharField(max_length=225, blank=True, null=True)
    state = models.CharField(max_length=225, blank=True, null=True)
    lga = models.CharField(max_length=225, blank=True, null=True)
    street = models.CharField(max_length=225, blank=True, null=True)
    nearest_landmark = models.CharField(max_length=225, blank=True, null=True)
    company_name = models.CharField(max_length=225, blank=True, null=True)
    industry = models.CharField(max_length=225, blank=True, null=True)
    size = models.IntegerField(default=0)
    channel = models.CharField(max_length=225, blank=True, null=True)
    access = models.TextField(null=True, blank=True)
    cac_num = models.CharField(max_length=225, blank=True, null=True)
    company_wallet = models.CharField(max_length=225, blank=True, null=True)
    pin = models.CharField(max_length=225, blank=True, null=True)
    password = models.CharField(max_length=225, blank=True, null=True)
    implementation_steps = models.CharField(
        max_length=225, choices=STEP_CHOICES, default="user_creation"
    )
    user_creation_data = models.TextField(blank=True, null=True)
    verify_account_data = models.TextField(blank=True, null=True)
    create_transaction_pin_data = models.TextField(blank=True, null=True)
    create_paybox_user_data = models.TextField(blank=True, null=True)
    create_company_data = models.TextField(blank=True, null=True)

    @classmethod
    def create_user(cls, user_data: dict):

        onboarding_mgr = LibertyPayOnboardingMgr()

        password = generate_password()
        pin = generate_transaction_pin()

        object_instance = cls.objects.create(
            first_name=user_data.get("first_name"),
            last_name=user_data.get("last_name"),
            email=user_data.get("email"),
            phone_number=user_data.get("phone_number"),
            username=user_data.get("username"),
            state=user_data.get("state"),
            lga=user_data.get("lga"),
            size=user_data.get("size"),
            nearest_landmark=user_data.get("nearest_landmark"),
            street=user_data.get("street"),
            company_name=user_data.get("company_name"),
            company_wallet=user_data.get("company_wallet_type"),
            channel=user_data.get("channel"),
            cac_num=user_data.get("cac_num"),
            password=password,
            pin=pin,
        )

        result = onboarding_mgr.sign_up(
            first_name=user_data.get("first_name"),
            last_name=user_data.get("last_name"),
            email=user_data.get("email"),
            phone_number=user_data.get("phone_number"),
            username=user_data.get("username"),
            state=user_data.get("state"),
            lga=user_data.get("lga"),
            nearest_landmark=user_data.get("nearest_landmark"),
            street=user_data.get("street"),
            password=password,
            pin=pin,
        )

        object_instance.save()
        # object_instance.user_creation_data = result
        if result:
            logger.debug(f"Result received: {result}")
            object_instance.user_creation_data = json.dumps(result)
        else:
            object_instance.user_creation_data = json.dumps(
                {"error": "No data returned"}
            )

        object_instance.save()
        success_code = [200, 201]
        if (
            result.get("status") == "success"
            and result.get("status_code") in success_code
        ):
            data = result.get("response")

            passcode = data.get("passcode")
            access = data.get("access")

            object_instance.implementation_steps = "verify_account"
            object_instance.access = access

            object_instance.save()

            object_instance.verify_account(passcode=passcode)
        else:
            pass

        return result

    def verify_account(self, passcode):

        onboarding_mgr = LibertyPayOnboardingMgr()
        result = onboarding_mgr.verify_user(self.email, passcode)

        self.verify_account_data = json.dumps(result)
        self.save()
        success_code = [200, 201]
        if (
            result.get("status") == "success"
            and result.get("status_code") in success_code
        ):
            data = result.get("response")
            self.implementation_steps = "create_transaction_pin"
            self.save()
            self.create_transaction_pin()

        else:
            pass

    def create_transaction_pin(self):
        onboarding_mgr = LibertyPayOnboardingMgr()
        result = onboarding_mgr.create_transaction_pin(
            transaction_pin=self.pin, token=self.access
        )

        self.create_transaction_pin_data = json.dumps(result)
        self.save()
        success_code = [200, 201]
        if (
            result.get("status") == "success"
            and result.get("status_code") in success_code
        ):
            data = result.get("response")
            self.implementation_steps = "create_paybox_user"
            self.save()
            self.create_paybox_user()

    def create_paybox_user(self):

        onboarding_mgr = LibertyPayOnboardingMgr()
        result = onboarding_mgr.fetch_user_details(token=self.access)
        self.create_paybox_user_data = json.dumps(result)
        self.save()

        success_code = [200, 201]

        if (
            result.get("status") == "success"
            and result.get("status_code") in success_code
        ):
            data = result.get("response")
            pprint(data)
            user_data = data.get("user_data")
            accounts_data = data.get("accounts_data")
            # print(accounts_data,"\n\n")
            # liberty_pay_acct_detail = get_account_details(account_data=accounts_data)
            # account_name = liberty_pay_acct_detail.get("account_name") if liberty_pay_acct_detail else None
            # bank_name = liberty_pay_acct_detail.get("bank_name")
            # account_no = liberty_pay_acct_detail.get("account_number") if liberty_pay_acct_detail else None
            self.implementation_steps = "create_company"
            self.save()

            liberty_pay_id = user_data.get("id")
            user_qs = User.objects.filter(liberty_pay_id=liberty_pay_id)
            if user_qs.exists():
                user = user_qs.first()

            else:
                user = User.objects.create(
                    liberty_pay_id=liberty_pay_id,
                    liberty_pay_customer_id=user_data.get("customer_id"),
                    phone_no=user_data.get("phone_number"),
                    email=user_data.get("email"),
                    first_name=user_data.get("first_name"),
                    last_name=user_data.get("last_name"),
                    bvn_number=user_data.get("bvn_number"),
                    bvn_first_name=user_data.get("bvn_first_name"),
                    bvn_last_name=user_data.get("bvn_last_name"),
                    account_no=None,
                    account_name=None,
                    street=user_data.get("street"),
                    state=user_data.get("state"),
                    lga=user_data.get("lga"),
                    nearest_landmark=user_data.get("nearest_landmark"),
                    gender=user_data.get("gender"),
                    is_active=bool(user_data.get("is_active")),
                    liberty_pay_customer_status=bool(user_data.get("is_active")),
                    liberty_pay_user_type=user_data.get("type_of_user"),
                    bank=None,
                    bank_code="",
                )
            self.create_company(user=user)

    def create_company(self, user):
        company_data = {
            "company_name": self.company_name,
            "industry": self.industry,
            "cac_num": self.cac_num,
            "size": self.size,
            "wallet_type": self.company_wallet,
        }

        company = req.Company.create_company(
            user=user, validated_data=company_data, transaction_pin=self.pin
        )

        """
        After company creation, fetch the company email, find a matching leads in the leads table.
        Loop through the leads and update the onboard_status
        
        """
        company_email = self.email

        leads = perf.Lead.objects.filter(company_email=company_email)
        for lead in leads:
            if lead.onboard_status == "AWAITING_ONBOARDING":
                lead.onboard_status = "ONBOARDING"
                lead.save()

    def tie_subscription_to_company(self):
        """
        This method ties the subscription to the company based on the payment status of invoices.
        """
        company_email = self.email
        leads_qs = perf.Lead.objects.filter(company_email=company_email)
        invoices = Invoice.objects.filter(leads__in=leads_qs)

        for invoice in invoices:
            if invoice.payment_status in ["paid", "paid_excess"]:
                subscription_modules = SubscriptionModule.objects.filter(
                    invoice=invoice
                )

                for subscription_module in subscription_modules:
                    subscription_module.company = self

                    # Calculate the expiry date and set active status
                    expiry_date = subscription_module.calculate_expiry_date()
                    subscription_module.is_active = expiry_date > timezone.now()

                    subscription_module.save()

                # Set invoice as active if any of the subscription modules are active
                invoice.is_active = subscription_modules.filter(is_active=True).exists()
                invoice.save()

        self.save()


# subscription_and_invoicing/models.py - Add this new model
class ExcludedPath(models.Model):
    """Model to store paths that should be excluded from subscription checking"""

    path_pattern = models.CharField(
        max_length=255,
        help_text="Regex pattern for excluded path (e.g. ^/req/get-companies/)",
    )
    description = models.TextField(
        blank=True,
        null=True,
        help_text="Optional description of why this path is excluded",
    )
    is_active = models.BooleanField(
        default=True, help_text="Whether this exclusion is currently active"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Excluded Path"
        verbose_name_plural = "Excluded Paths"
        ordering = ["path_pattern"]

    def __str__(self):
        return self.path_pattern
