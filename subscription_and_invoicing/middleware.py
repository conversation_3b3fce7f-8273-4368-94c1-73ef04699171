# subscription_and_invoicing/middleware.py
from django.utils import timezone
from django.http import JsonResponse
from subscription_and_invoicing.service import SubscriptionManager
from .models import SubscriptionModule, Company, ExcludedPath
import logging
import re
from functools import lru_cache
from django.core.cache import cache


logger = logging.getLogger(__name__)


class SubscriptionAccessMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        
        # First compile the module patterns
        self._compile_module_patterns()
        
        # Then compile the exclusion patterns
        self._compile_exclusion_patterns()
        
        # Finally set up the dependencies
        self._setup_module_dependencies()

    def _compile_module_patterns(self):
        """
        Precompile regex patterns for module mapping.
        """
        # Module mapping with compiled patterns
        self.module_map = [
            (re.compile(r"^/api/v1/stock/"), "INVENTORY"),
            (re.compile(r"^/api/v1/sales/"), "SALES"),
            (re.compile(r"^/api/v1/invoicing/"), "SALES"),
            (re.compile(r"^/instant_web/"), "SALES"),
            (re.compile(r"^/req/"), "SPEND"),
            # (re.compile(r"^/payroll/"), "HR"),
            # (re.compile(r"^/management/"), "HR"),
            # (re.compile(r"^/leave-management/"), "HR"),
        ]

    def _compile_exclusion_patterns(self):
        """
        Precompile regex patterns for excluded paths for better performance.
        Fetches patterns from the ExcludedPath model.
        """
        # Check cache first
        cached_patterns = cache.get('excluded_path_patterns')
        if cached_patterns:
            self.excluded_patterns = cached_patterns
            return
            
        try:
            # Fetch active exclusions from the database
            excluded_paths = ExcludedPath.objects.filter(is_active=True).values_list('path_pattern', flat=True)
            
            # Compile all patterns for faster matching
            self.excluded_patterns = [re.compile(pattern) for pattern in excluded_paths]
            
            # Cache the patterns for 1 hour to avoid frequent database queries
            cache.set('excluded_path_patterns', self.excluded_patterns, 3600)
        except:
            # Fallback to hardcoded paths if the model doesn't exist yet
            # (e.g., during migrations)
            excluded_paths = [
                r"^/req/get-companies/",
                r"^/req/all_vendor_list/",
                r"^/payroll/company_list/",
                r"^/payroll/user_role/",
                r"^/api/v1/sales/get_user_details",
                r"^/api/v1/stock/companies",
                r"^/api/v1/stock/branch/stock_details",
                r"^/req/industries/\?page_size=\d+",
                r"^/req/verify-reg-number/",
                r"^/req/get-teams/",
                r"^/req/get-team_members/",
                r"^/req/get-budgets/",
                r"^/req/company-verification/",
                r"^/payroll/company_list_dashboard/",
                r"^/req/cdg-company/",
                r"^/req/procurement/",
                r"^/req/dis-trnx-exist/",
                r"^/req/industries/",
                r"^/api/v1/stock/branches/",
                r"^/api/v1/stock/company/",
                r"^/api/v1/sales/branches",
                r"^/payroll/company_list/",
            ]
            self.excluded_patterns = [re.compile(pattern) for pattern in excluded_paths]
    
    def _setup_module_dependencies(self):
        """
        Set up module dependencies in two ways:
        1. Module-level: Full access to a module if user has subscription to another
        2. Endpoint-level: Access to specific endpoints only
        """
        self.module_dependencies = {
            "SALES": {
                "required_by_modules": ["INVENTORY"],
                "allowed_endpoints": [
                    # You can leave this empty initially and add specific endpoints later
                    re.compile(r"^/api/v1/sales/get-product-info/"),
                    re.compile(r"^/payroll/user_role/"),
                    re.compile(r"^/req/all_vendor_list/"),
                    re.compile(r"^/api/v1/sales/"),
                    re.compile(r"^/api/v1/invoicing/"),
                    re.compile(r"^/instant_web/"),

                ],
            },
            "INVENTORY": {
                "required_by_modules": ["SALES"],
                "allowed_endpoints": [
                    re.compile(r"^/api/v1/sales/get-product-info/"),
                    re.compile(r"^/payroll/user_role/"),
                    re.compile(r"^/req/all_vendor_list/"),
                    re.compile(r"^/api/v1/sales/"),
                    re.compile(r"^/api/v1/invoicing/"),
                    re.compile(r"^/instant_web/"),

                ],
            },
        }

    def get_company_uuid_from_request(self, request):
        company_uuid = request.GET.get("company_uuid")
        company_id = request.GET.get("company_id")
        company_param = request.GET.get("company")

        if company_uuid:
            return company_uuid

        if company_id:
            return company_id

        if company_param:
            return company_param

        return None

    def __call__(self, request):
        # Early return for excluded paths before any processing
        if self._is_excluded_path(request.path_info):
            return self.get_response(request)

        # First, let the request pass through all middlewares and get processed
        response = self.get_response(request)

        # After processing, check if the user is authenticated
        if hasattr(request, "user") and request.user.is_authenticated:
            logger.debug(f"User authenticated: {request.user.email}")

            # Check if the path matches any of our subscription-restricted modules
            module_code = self._get_module_from_api_path(request.path_info)

            if module_code:
                # Try to get company_uuid from request parameters
                company_uuid = self.get_company_uuid_from_request(request)
                has_access = SubscriptionManager.check_module_access(
                    request.user, module_code, company_uuid
                ) 

                # If no direct access, check dependency-based access
                if not has_access:
                    has_access = self._check_dependency_access(
                        request.user, module_code, request.path_info, company_uuid
                    )

                if not has_access:
                    logger.info(
                        f"Access denied for {request.user.email} to module: {module_code}, path: {request.path_info}"
                    )
                    # Return a 403 response
                    return JsonResponse(
                        {"error": f"Subscription required for {module_code} module"},
                        status=403,
                    )

        return response

    def _is_excluded_path(self, path):
        """
        Check if path should be excluded from subscription checks.
        Using compiled regex patterns for better performance.
        """
        for pattern in self.excluded_patterns:
            if pattern.match(path) or pattern.match(path + "/"):
                return True
        return False

    @lru_cache(maxsize=128)
    def _get_module_from_api_path(self, path):
        """
        Map API paths to module codes based on compiled patterns.
        Returns None for paths that don't match any module.
        Uses lru_cache for better performance with repeated requests.

        Args:
            path (str): The URL path to check

        Returns:
            str or None: Module code if matched, None otherwise
        """
        # Check module mapping
        for pattern, code in self.module_map:
            if pattern.match(path) or pattern.match(path + "/"):
                return code

        return None

    def _check_dependency_access(self, user, target_module_code, path_info, company_uuid=None):
        """
        Check if a user has dependency-based access to a module or specific endpoint.

        Args:
            user (User): The user to check
            target_module_code (str): The module code of the endpoint
            path_info (str): The current request path
            company_uuid (str, optional): UUID of the company to check access for

        Returns:
            bool: True if user has dependency-based access, False otherwise
        """
        # Check if this module has dependency configurations
        if target_module_code not in self.module_dependencies:
            return False

        dependency_config = self.module_dependencies[target_module_code]

        # Check if user has access to any of the requiring modules
        has_requiring_module_access = False
        for required_by_module in dependency_config["required_by_modules"]:
            if SubscriptionManager.check_module_access(user, required_by_module, company_uuid):
                has_requiring_module_access = True
                break

        if not has_requiring_module_access:
            return False

        # If no specific endpoints are defined, grant access to the entire module
        if not dependency_config.get("allowed_endpoints"):
            logger.debug(
                f"Module-level dependency access granted for {user.email} to {target_module_code}"
            )
            return True

        # If specific endpoints are defined, check if the path matches any
        for pattern in dependency_config["allowed_endpoints"]:
            if pattern.match(path_info) or pattern.match(path_info + "/"):
                logger.debug(
                    f"Endpoint-level dependency access granted for {user.email} to {path_info}"
                )
                return True

        # If specific endpoints are defined but none match, deny access
        return False