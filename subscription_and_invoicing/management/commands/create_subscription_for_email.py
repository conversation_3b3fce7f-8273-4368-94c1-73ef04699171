import logging
from django.utils import timezone
from decimal import Decimal
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand, CommandError

from subscription_and_invoicing.models import (
    SubscriptionPlan,
    Module,
    CompanySubscription,
    ModuleSubscription,
    SubscriptionAudit,
    Invoice,
)

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = "Create subscriptions for the default company linked to a given user email"

    def handle(self, *args, **kwargs):
        email = "<EMAIL>"
        try:
            # Get the user by email
            user = User.objects.get(email=email)

            # Ensure the user has a default company
            default_company = user.default_company
            if not default_company:
                raise CommandError(f"User {email} has no default company set.")

            self.stdout.write(self.style.SUCCESS(f"Processing subscriptions for default company: {default_company.company_name}"))

            # Define modules and subscription plans
            modules = Module.objects.filter(id__in=[1, 2, 3, 4])
            plans = [
                SubscriptionPlan.objects.get(id=1),  # 6-month plan
                SubscriptionPlan.objects.get(id=2),  # 12-month plan
            ]

            for plan in plans:
                # Set subscription dates
                start_date = timezone.now()
                end_date = start_date + timezone.timedelta(days=30 * plan.duration_months)

                # Create the company subscription
                subscription = CompanySubscription.objects.create(
                    company=default_company,
                    access_type="paid",
                    status="active",
                    created_by=user,
                )

                # Create subscriptions for each module
                for module in modules:
                    ModuleSubscription.objects.create(
                        company_subscription=subscription,
                        module=module,
                        plan=plan,
                        is_active=True,
                        start_date=start_date,
                        end_date=end_date,
                        status="active",
                    )

                # Generate an invoice for this subscription
                Invoice.objects.create(
                    company=default_company,
                    company_subscription=subscription,
                    total_amount=plan.price,
                    amount_paid=plan.price,
                    balance_due=Decimal("0"),
                    payment_status="paid",
                    start_date=start_date,
                    expiry_date=end_date,
                )

                # Log the subscription audit
                SubscriptionAudit.objects.create(
                    company=default_company,
                    subscription=subscription,
                    action="Subscription created for user email",
                    details={
                        "plan": plan.name,
                        "status": "active",
                        "modules": [module.name for module in modules],
                    },
                )

            logger.info(f"Successfully processed subscription for {default_company.company_name} under user {email}")
            self.stdout.write(self.style.SUCCESS(f"Subscriptions created successfully for {email} under default company: {default_company.company_name}"))

        except User.DoesNotExist:
            raise CommandError(f"User with email {email} does not exist.")
        except SubscriptionPlan.DoesNotExist:
            raise CommandError("One or more subscription plans (6-month or 12-month) do not exist.")
        except Exception as e:
            logger.error(f"Error creating subscriptions for {email}: {e}")
            raise CommandError(f"Failed to create subscriptions for {email}: {e}")
