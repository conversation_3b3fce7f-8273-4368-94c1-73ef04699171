import csv
import os
from datetime import datetime
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from subscription_and_invoicing.models import (
    SubscriptionPlan, 
    Module, 
    CompanySubscription, 
    ModuleSubscription,
    AccessPath
)
from requisition.models import Company

User = get_user_model()

class Command(BaseCommand):
    help = 'Import subscriptions from a CSV file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--admin-email',
            dest='admin_email',
            help='Email of the admin user to set as the creator of subscriptions',
            default='<EMAIL>'
        )

    def handle(self, *args, **options):
        file_path = 'Paybox sub - Sheet1.csv'
        admin_email = options.get('admin_email')

        # Validate file exists
        if not os.path.exists(file_path):
            self.stderr.write(self.style.ERROR(f'File not found: {file_path}'))
            return
        
        # Get admin user by email
        created_by = None
        if admin_email:
            try:
                created_by = User.objects.get(email=admin_email)
                self.stdout.write(self.style.SUCCESS(f'Using admin user: {created_by.email}'))
            except User.DoesNotExist:
                self.stderr.write(self.style.ERROR(f'Admin user with email {admin_email} not found'))
                return
        
        try:
            # Get INVENTORY module 
            inventory_module = self.get_inventory_module()
            if not inventory_module:
                self.stderr.write(self.style.ERROR("Cannot continue without INVENTORY module"))
                return
            
            # Process CSV file
            with open(file_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                
                processed = 0
                skipped = 0
                
                for row in reader:
                    try:
                        with transaction.atomic():
                            # Process this row
                            result = self.process_subscription_row(row, created_by, inventory_module)
                            if result:
                                processed += 1
                                self.stdout.write(self.style.SUCCESS(
                                    f"Created subscription for: {row.get('Company', 'Unknown company')}"
                                ))
                            else:
                                skipped += 1
                    except Exception as e:
                        self.stderr.write(self.style.ERROR(
                            f"Error processing row for {row.get('Company', 'Unknown')}: {str(e)}"
                        ))
                        skipped += 1
            
            self.stdout.write(self.style.SUCCESS(
                f"Import completed. Processed: {processed}, Skipped: {skipped}"
            ))
        
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Failed to import data: {str(e)}"))

    def get_inventory_module(self):
        """Get the INVENTORY module"""
        try:
            return Module.objects.get(code='INVENTORY')
        except Module.DoesNotExist:
            self.stderr.write(self.style.ERROR("INVENTORY module not found. Please create it first."))
            return None
            
    def find_company(self, company_name, email=None):
        """Find a company with the given name"""
        if not company_name:
            self.stderr.write(self.style.ERROR(
                f"No company name provided"
            ))
            return None
            
        # Try to find company by name
        try:
            # Try exact match first
            company = Company.objects.filter(company_name=company_name).first()
            if company:
                self.stdout.write(self.style.SUCCESS(
                    f"Found company '{company_name}'"
                ))
                return company
                
            # Try case-insensitive match
            company = Company.objects.filter(company_name__iexact=company_name).first()
            if company:
                self.stdout.write(self.style.SUCCESS(
                    f"Found company '{company_name}' (case-insensitive match)"
                ))
                return company
                
            # Try partial match (contains)
            company = Company.objects.filter(company_name__icontains=company_name).first()
            if company:
                self.stdout.write(self.style.SUCCESS(
                    f"Found company containing '{company_name}'"
                ))
                return company
            
            self.stderr.write(self.style.ERROR(
                f"Company '{company_name}' not found. Please check company name or add the company first."
            ))
            return None
        except Exception as e:
            self.stderr.write(self.style.ERROR(
                f"Error finding company with name {company_name}: {str(e)}"
            ))
            return None

    def parse_date(self, date_str, company_name):
        """Parse date string in multiple formats"""
        date_formats = [
            '%Y-%m-%d %H:%M:%S',  # 2024-01-01 12:00:00
            '%Y-%m-%d',           # 2024-01-01
            '%m/%d/%Y',           # 01/01/2024
            '%d/%m/%Y',           # 01/01/2024 (day/month format)
            '%m/%d/%y',           # 01/01/24
            '%d/%m/%y',           # 01/01/24 (day/month format)
        ]
        
        for date_format in date_formats:
            try:
                date_obj = datetime.strptime(date_str, date_format)
                # For 2-digit years, ensure we're not interpreting future dates incorrectly
                if '%y' in date_format and date_obj.year > 2099:
                    date_obj = date_obj.replace(year=date_obj.year - 100)
                self.stdout.write(self.style.SUCCESS(
                    f"Successfully parsed date {date_str} as {date_obj.strftime('%Y-%m-%d')}"
                ))
                return timezone.make_aware(date_obj)
            except ValueError:
                continue
                
        self.stderr.write(self.style.ERROR(
            f"Invalid date format for {company_name}: {date_str}"
        ))
        return None
    
    def get_subscription_plan(self, plan_name):
        """Get subscription plan based on name or duration"""
        plan_name_lower = plan_name.lower()
        
        # Check for semi-annual first (more specific)
        if 'semi' in plan_name_lower or '6' in plan_name_lower:
            months = 6
        # Then check for annual without semi
        elif 'annual' in plan_name_lower or '12' in plan_name_lower:
            months = 12
        else:
            self.stderr.write(self.style.ERROR(
                f"Unable to determine plan duration from: {plan_name}"
            ))
            return None
        
        try:
            return SubscriptionPlan.objects.get(duration_months=months)
        except SubscriptionPlan.DoesNotExist:
            self.stderr.write(self.style.ERROR(
                f"Subscription plan with duration {months} months not found"
            ))
            return None
    
    def process_subscription_row(self, row, created_by, inventory_module):
        """Process a single row from the CSV file"""
        company_name = row.get('Company')
        email = row.get('Email')  # We're not using this for lookup now, but keeping it for reference
        plan_name = row.get('Subscription Plan')
        payment_date_str = row.get('Payment Date')
        
        if not company_name or not plan_name or not payment_date_str:
            self.stderr.write(self.style.ERROR(
                f"Skipping row with missing required data: {company_name or 'Unknown company'}"
            ))
            return False
        
        # Find company by name
        company = self.find_company(company_name)
        if not company:
            return False
            
        # Get subscription plan
        plan = self.get_subscription_plan(plan_name)
        if not plan:
            return False
            
        # Parse payment date (assuming it's the start date)
        payment_date = self.parse_date(payment_date_str, company_name)
        if not payment_date:
            return False
        
        # Create company subscription
        company_subscription = CompanySubscription.objects.create(
            company=company,
            access_type=AccessPath.PAID,
            plan=plan,
            status='active',
            created_by=created_by,
            is_upgrade=False,
            pos_included=False
        )
        
        # Create module subscription
        module_subscription = ModuleSubscription.objects.create(
            company_subscription=company_subscription,
            plan=plan,
            module=inventory_module,
            is_auto_renewal=False,
            status='active',
            is_active=True,
            start_date=payment_date,
            end_date=None  # Let model calculate end_date based on plan
        )
        
        return True