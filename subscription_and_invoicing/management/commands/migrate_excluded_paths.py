from django.core.management.base import BaseCommand
from subscription_and_invoicing.models import ExcludedPath

class Command(BaseCommand):
    help = 'Migrate existing excluded paths to the database'

    def handle(self, *args, **options):
        # List of paths to exclude from subscription checking
        initial_excluded_paths = [
            # Original exclusions
            r"^/req/get-companies/",
            r"^/payroll/company_list/",
            r"^/api/v1/sales/get_user_details",
            r"^/api/v1/stock/companies",
            r"^/api/v1/stock/branch/stock_details",
            # New exclusions
            r"^/req/industries/\?page_size=\d+",
            r"^/req/verify-reg-number/",
            r"^/req/get-teams/",
            r"^/req/get-team_members/",
            r"^/req/get-budgets/",
            r"^/req/company-verification/",
            r"^/payroll/company_list_dashboard/",
            r"^/req/cdg-company/",
            r"^/req/procurement/",
            r"^/req/dis-trnx-exist/",
            r"^/req/industries/",
            r"^/api/v1/stock/branches/",
            r"^/api/v1/stock/company/",
            r"^/api/v1/sales/branches",
            r"^/payroll/company_list/",
        ]
        
        created_count = 0
        for path in initial_excluded_paths:
            _, created = ExcludedPath.objects.get_or_create(
                path_pattern=path,
                defaults={
                    'description': 'Migrated from initial middleware code',
                    'is_active': True
                }
            )
            if created:
                created_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully migrated {created_count} excluded paths. '
                f'{len(initial_excluded_paths) - created_count} paths already existed.'
            )
        )