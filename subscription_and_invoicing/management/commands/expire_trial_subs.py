# subscription_and_invoicing/management/commands/expire_trial_subs.py

import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from subscription_and_invoicing.models import ModuleSubscription

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Expires trial module subscriptions that are older than 12 days.'

    def handle(self, *args, **kwargs):
        now = timezone.now()
        expired_subs = ModuleSubscription.objects.filter(
            end_date__lt=now,
            is_active=True,
            status='active',
            company_subscription__access_type='trial'  # Match AccessPath.TRIAL if enum
        )

        count = expired_subs.count()

        if count == 0:
            logger.info("✅ No trial subscriptions to expire.")
            return

        for sub in expired_subs:
            sub.is_active = False
            sub.status = 'expired'
            sub.save(update_fields=['is_active', 'status'])
            logger.info(f"💀 Expired trial subscription for module '{sub.module}' and company '{sub.company_subscription.company}'")

        logger.info(f"✅ {count} trial module subscriptions expired.")