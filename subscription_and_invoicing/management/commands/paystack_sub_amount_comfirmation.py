from django.core.management.base import BaseCommand
from django.db import transaction, models
from decimal import Decimal
import logging
from subscription_and_invoicing import models as subscription

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Test payment confirmation process with hardcoded values"

    def handle(self, *args, **options):
        try:
            # Hardcoded test values - DO NOT CHANGE
            batch_id = "YBH57"
            amount_paid = 3000
            reference = "YBH57"

            self.stdout.write(f"Testing payment confirmation with:")
            self.stdout.write(f"  Batch ID: {batch_id}")
            self.stdout.write(f"  Amount: {amount_paid}")
            self.stdout.write(f"  Reference: {reference}")

            if not batch_id or not amount_paid:
                self.stdout.write(
                    self.style.ERROR(
                        f"Missing required fields - batch_id: {batch_id}, amount_paid: {amount_paid}"
                    )
                )
                return

            with transaction.atomic():
                try:
                    invoice = subscription.Invoice.objects.select_related(
                        "company_subscription"
                    ).get(
                        models.Q(batch_id=batch_id)
                        | models.Q(invoice_reference=reference)
                    )
                except subscription.Invoice.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Invoice with batch_id/reference {batch_id}/{reference} not found"
                        )
                    )
                    return

                amount_paid_decimal = Decimal(str(amount_paid))
                invoice_total = invoice.total_amount or Decimal("0")

                if amount_paid_decimal <= 0:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Invalid payment amount: {amount_paid_decimal}"
                        )
                    )
                    return

                self.stdout.write(f"Invoice total: {invoice_total}")
                self.stdout.write(f"Amount paid: {amount_paid_decimal}")

                if batch_id == reference and amount_paid_decimal >= invoice_total:
                    current_balance_due = invoice.balance_due or Decimal("0")
                    new_amount_paid = amount_paid_decimal
                    new_balance_due = max(
                        Decimal("0"), invoice_total - amount_paid_decimal
                    )
                    new_settled_amount = amount_paid_decimal

                    if amount_paid_decimal > invoice_total:
                        payment_status = "paid_excess"
                        new_balance_due = Decimal("0")
                        excess_amount = amount_paid_decimal - invoice_total
                    else:
                        payment_status = "paid"
                        excess_amount = Decimal("0")
                        new_balance_due = Decimal("0")

                    invoice.payment_status = payment_status
                    invoice.amount_paid = new_amount_paid
                    invoice.balance_due = new_balance_due
                    invoice.settled_amount = new_settled_amount
                    invoice.is_active = True

                    if payment_status == "paid_excess":
                        invoice.amount_brought_forward = excess_amount

                    invoice.save()
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"\u2713 Updated Invoice - payment_status: {payment_status}, "
                            f"amount_paid: {new_amount_paid}, balance_due: {new_balance_due}, "
                            f"settled_amount: {new_settled_amount}"
                        )
                    )

                    # NEW: Attempt to retrieve CompanySubscription by reference if not linked on invoice
                    company_subscription = invoice.company_subscription
                    if not company_subscription:
                        try:
                            company_subscription = (
                                subscription.CompanySubscription.objects.get(
                                    invoice_ref=reference
                                )
                            )
                            invoice.company_subscription = company_subscription
                            invoice.save(update_fields=["company_subscription"])
                            self.stdout.write(
                                self.style.SUCCESS(
                                    f"\u2713 Linked Invoice to CompanySubscription via reference: {reference}"
                                )
                            )
                        except subscription.CompanySubscription.DoesNotExist:
                            self.stdout.write(
                                self.style.WARNING(
                                    f"\u26a0\ufe0f No CompanySubscription found with invoice_ref = {reference}"
                                )
                            )

                    if company_subscription:
                        company_subscription_id = company_subscription.id

                        subscription.CompanySubscription.objects.filter(
                            id=company_subscription_id
                        ).update(
                            status="active",
                            token_previous_balance=models.F("token_current_balance"),
                            token_current_balance=models.F("token_current_balance")
                            + amount_paid_decimal,
                            token_status="active",
                            invoice_ref=reference,
                            updated_at=models.functions.Now(),
                        )

                        updated_company_sub = (
                            subscription.CompanySubscription.objects.get(
                                id=company_subscription_id
                            )
                        )
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"\u2713 Updated CompanySubscription ID {company_subscription_id} - "
                                f"status: {updated_company_sub.status}, "
                                f"token_previous_balance: {updated_company_sub.token_previous_balance}, "
                                f"token_current_balance: {updated_company_sub.token_current_balance}, "
                                f"token_status: {updated_company_sub.token_status}, "
                                f"invoice_ref: {updated_company_sub.invoice_ref}"
                            )
                        )

                        module_subscriptions = (
                            subscription.ModuleSubscription.objects.filter(
                                company_subscription=company_subscription
                            )
                        )

                        if module_subscriptions.exists():
                            updated_count = module_subscriptions.update(
                                invoice_ref=reference,
                                status="active",
                                is_active=True,
                                updated_at=models.functions.Now(),
                            )

                            self.stdout.write(
                                self.style.SUCCESS(
                                    f"\u2713 Updated {updated_count} ModuleSubscription records - "
                                    f"invoice_ref: {reference}, status: active, is_active: True"
                                )
                            )

                            for module_sub in module_subscriptions:
                                self.stdout.write(
                                    self.style.SUCCESS(
                                        f"  - Module: {module_sub.module.name if hasattr(module_sub, 'module') else 'N/A'}, "
                                        f"Status: {module_sub.status}, Active: {module_sub.is_active}"
                                    )
                                )
                        else:
                            self.stdout.write(
                                self.style.WARNING(
                                    f"\u26a0\ufe0f No ModuleSubscription records found for CompanySubscription ID {company_subscription_id}"
                                )
                            )

                        try:
                            if hasattr(
                                company_subscription, "update_overall_subscription_days"
                            ):
                                company_subscription.update_overall_subscription_days()
                                self.stdout.write(
                                    self.style.SUCCESS(
                                        f"\u2713 Updated overall subscription days for CompanySubscription ID {company_subscription_id}"
                                    )
                                )
                        except Exception as e:
                            self.stdout.write(
                                self.style.WARNING(
                                    f"\u26a0\ufe0f Could not update overall subscription days: {str(e)}"
                                )
                            )
                    else:
                        self.stdout.write(
                            self.style.WARNING(
                                f"\u26a0\ufe0f Invoice {invoice.id} has no associated CompanySubscription"
                            )
                        )

                    invoice_transaction = (
                        subscription.InvoiceTransaction.objects.create(
                            invoice=invoice,
                            amount=amount_paid_decimal,
                            payment_method="test_payment",
                            excess_payment=excess_amount,
                            is_processed=True,
                            invoice_reference=reference,
                        )
                    )

                    self.stdout.write(
                        self.style.SUCCESS(
                            f"\u2713 Created InvoiceTransaction record with ID: {invoice_transaction.id}"
                        )
                    )

                    self.stdout.write(
                        self.style.SUCCESS(
                            f"\U0001f389 Successfully processed test payment for batch_id: {batch_id}, "
                            f"reference: {reference}, amount: {amount_paid_decimal}"
                        )
                    )

                else:
                    new_amount_paid = (
                        invoice.amount_paid or Decimal("0")
                    ) + amount_paid_decimal
                    new_balance_due = max(Decimal("0"), invoice_total - new_amount_paid)
                    new_settled_amount = (
                        invoice.settled_amount or Decimal("0")
                    ) + amount_paid_decimal

                    if new_balance_due == Decimal("0"):
                        payment_status = "paid"
                    elif new_balance_due < invoice_total:
                        payment_status = "part_payment"
                    else:
                        payment_status = "unpaid"

                    invoice.payment_status = payment_status
                    invoice.amount_paid = new_amount_paid
                    invoice.balance_due = new_balance_due
                    invoice.settled_amount = new_settled_amount
                    invoice.save()

                    self.stdout.write(
                        self.style.WARNING(
                            f"\u26a0\ufe0f Processed partial payment for batch_id: {batch_id}, "
                            f"amount: {amount_paid_decimal}, status: {payment_status}"
                        )
                    )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Error processing test payment: {str(e)}")
            )
            logger.error(f"Error processing test payment confirmation: {str(e)}")
            raise
