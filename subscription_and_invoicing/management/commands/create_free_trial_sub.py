import logging
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from subscription_and_invoicing.models import (
    Module,
    Company,
    CompanySubscription,
    ModuleSubscription,
    SubscriptionAudit,
)

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = "Create 12-day free trial subscriptions for companies without active subscriptions"

    def handle(self, *args, **options):
        # Fixed 12-day trial period
        trial_days = 12

        try:
            # Get all companies from the system
            companies = Company.objects.all()
            total_companies = companies.count()

            if total_companies == 0:
                self.stdout.write(
                    self.style.WARNING("No companies found in the system.")
                )
                return

            self.stdout.write(f"Found {total_companies} companies in the system.")

            # Fetch all modules from the database once
            modules = Module.objects.all()
            if not modules.exists():
                raise CommandError(
                    "No modules found in the system. Cannot create subscriptions."
                )

            self.stdout.write(
                f"Found {modules.count()} modules: {', '.join([m.name for m in modules])}"
            )

            companies_with_active_subscriptions = 0
            companies_without_subscriptions = 0
            companies_with_trial_only = 0
            deleted_existing_trials = 0
            total_subscriptions_created = 0
            companies_skipped = 0

            # Process each company
            for company in companies:
                self.stdout.write(f"\nProcessing company: {company.company_name}")

                # Check if company has any PAID/ACTIVE (non-trial) subscriptions
                paid_subscriptions = CompanySubscription.objects.filter(
                    company=company, status__in=["active", "paid"]
                ).exclude(access_type="trial")

                if paid_subscriptions.exists():
                    companies_with_active_subscriptions += 1
                    companies_skipped += 1
                    self.stdout.write(
                        self.style.WARNING(
                            f"  - {company.company_name}: Has paid subscription(s). Skipping."
                        )
                    )
                    continue

                # For ALL other companies (inactive, no subscriptions, or trial only),
                # delete any existing subscriptions and create new trial
                existing_subscriptions = CompanySubscription.objects.filter(
                    company=company
                )

                had_existing_subscriptions = existing_subscriptions.exists()

                if had_existing_subscriptions:
                    subscription_count = existing_subscriptions.count()
                    deleted_existing_trials += subscription_count
                    # Delete ALL existing subscriptions (trials, inactive, etc.)
                    existing_subscriptions.delete()
                    self.stdout.write(
                        self.style.WARNING(
                            f"  - {company.company_name}: Deleted {subscription_count} existing subscription(s)"
                        )
                    )
                    companies_with_trial_only += 1
                else:
                    companies_without_subscriptions += 1

                # Get a user to associate with the subscription creation
                created_by_user = None

                self.stdout.write(
                    f"  - Looking for user to associate with {company.company_name}..."
                )

                # Method 1: Check if company has an owner (company.user field)
                if company.user:
                    created_by_user = company.user
                    self.stdout.write(
                        f"    Found company owner: {created_by_user.email}"
                    )
                # Method 2: Check for users with default_company set to this company
                elif User.objects.filter(default_company=company).exists():
                    created_by_user = User.objects.filter(
                        default_company=company
                    ).first()
                    self.stdout.write(
                        f"    Found user with default_company: {created_by_user.email}"
                    )
                # Method 3: Check through company_owner relationship (reverse lookup)
                elif User.objects.filter(company_owner=company).exists():
                    created_by_user = User.objects.filter(company_owner=company).first()
                    self.stdout.write(
                        f"    Found user through company_owner: {created_by_user.email}"
                    )
                # Method 4: Fallback to first admin user
                elif User.objects.filter(is_staff=True).exists():
                    created_by_user = User.objects.filter(is_staff=True).first()
                    self.stdout.write(
                        f"    Using admin user as fallback: {created_by_user.email}"
                    )
                # Method 5: Final fallback to any user
                elif User.objects.exists():
                    created_by_user = User.objects.first()
                    self.stdout.write(
                        f"    Using first available user as fallback: {created_by_user.email}"
                    )

                if not created_by_user:
                    self.stdout.write(
                        self.style.ERROR(
                            f"  - {company.company_name}: No users exist in the system. Cannot create subscription."
                        )
                    )
                    continue

                self.stdout.write(
                    f"  - Selected user {created_by_user.email} to create subscription for {company.company_name}"
                )

                # Create the subscription with database transaction
                try:
                    with transaction.atomic():
                        self.stdout.write(
                            f"  - Creating trial subscription for {company.company_name}..."
                        )

                        # Create FREE TRIAL subscription with is_upgrade field
                        free_trial_subscription = CompanySubscription.objects.create(
                            company=company,
                            access_type="trial",
                            status="trial",
                            created_by=created_by_user,
                        )

                        self.stdout.write(
                            f"  - Created CompanySubscription (ID: {free_trial_subscription.id})"
                        )

                        # Calculate dates
                        start_date = timezone.now()
                        end_date = start_date + timezone.timedelta(days=trial_days)

                        self.stdout.write(
                            f"  - Trial period: {start_date.date()} to {end_date.date()}"
                        )

                        # Create module subscriptions for all modules
                        module_subscriptions = []
                        for module in modules:
                            self.stdout.write(
                                f"    - Creating module subscription for: {module.name}"
                            )
                            module_subscription = ModuleSubscription.objects.create(
                                company_subscription=free_trial_subscription,
                                module=module,
                                is_active=True,
                                start_date=start_date,
                                end_date=end_date,
                                status="active",
                            )
                            module_subscriptions.append(module_subscription)

                        self.stdout.write(
                            f"  - Created {len(module_subscriptions)} module subscriptions"
                        )

                        # Create subscription audit log with shortened action text
                        action_type = "renewal" if had_existing_subscriptions else "new"

                        # Shortened action text to fit 50-character limit
                        action_text = (
                            "Trial created via bulk command"
                            if not had_existing_subscriptions
                            else "Trial renewed via bulk command"
                        )

                        audit_entry = SubscriptionAudit.objects.create(
                            company=company,
                            subscription=free_trial_subscription,
                            action=action_text,  # Now fits within 50 characters
                            details={
                                "status": "trial",
                                "trial_days": trial_days,
                                "modules": [module.name for module in modules],
                                "created_by": created_by_user.email,
                                "start_date": start_date.isoformat(),
                                "end_date": end_date.isoformat(),
                                "action_type": action_type,
                                "full_description": "Free Trial Subscription created/renewed via bulk management command",  # Full description in details
                            },
                        )

                        self.stdout.write(
                            f"  - Created audit entry (ID: {audit_entry.id})"
                        )

                        total_subscriptions_created += 1

                        action_word = (
                            "Renewed" if had_existing_subscriptions else "Created"
                        )
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"  - {company.company_name}: {action_word} 12-day trial subscription "
                                f"(ID: {free_trial_subscription.id}) with {len(module_subscriptions)} modules"
                            )
                        )

                        logger.info(
                            f"{action_word} trial subscription for {company.company_name} "
                            f"(Created by: {created_by_user.email}, Subscription ID: {free_trial_subscription.id})"
                        )

                except Exception as e:
                    logger.error(
                        f"Error creating trial subscription for {company.company_name}: {e}"
                    )
                    self.stdout.write(
                        self.style.ERROR(
                            f"  - {company.company_name}: Failed to create subscription - {str(e)}"
                        )
                    )

            # Print summary
            self.stdout.write("\n" + "=" * 60)
            self.stdout.write(self.style.SUCCESS("SUMMARY"))
            self.stdout.write("=" * 60)
            self.stdout.write(f"Total companies in system: {total_companies}")
            self.stdout.write(
                f"Companies with active subscriptions (skipped): {companies_with_active_subscriptions}"
            )
            self.stdout.write(
                f"Companies without any subscriptions: {companies_without_subscriptions}"
            )
            self.stdout.write(
                f"Companies with existing subscriptions (trials/inactive): {companies_with_trial_only}"
            )
            self.stdout.write(
                f"Companies skipped (paid subscriptions): {companies_skipped}"
            )
            self.stdout.write(
                f"Existing subscriptions deleted: {deleted_existing_trials}"
            )
            self.stdout.write(
                f"New trial subscriptions created: {total_subscriptions_created}"
            )
            self.stdout.write(f"Trial duration: {trial_days} days")

        except Exception as e:
            logger.error(f"Error in bulk trial subscription creation: {e}")
            raise CommandError(f"Failed to process bulk trial subscriptions: {e}")