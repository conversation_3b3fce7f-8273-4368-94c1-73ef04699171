import time
from typing import List, Dict
from django.core.management.base import BaseCommand, CommandError
from django.db.models import <PERSON>, <PERSON>, Exists, OuterRef
from core.tasks import send_email
from django.contrib.auth import get_user_model

from requisition.models import Company
from subscription_and_invoicing.models import (
    Module,
    CompanySubscription,
    ModuleSubscription,
)

User = get_user_model()


class Command(BaseCommand):
    help = "Send subscription notification emails to users for companies that need to renew or purchase subscriptions"

    # Batch processing configuration
    BATCH_SIZE = 100
    BATCH_DELAY = 6  # seconds

    # Email configuration
    EMAIL_SUBJECT = "Important Update: Subscription-Based Access to Paybox360 Modules"
    EMAIL_TEMPLATE = "subscription_notification_reminder.html"

    # Required module names
    REQUIRED_MODULES = [
        "Spend Management",
        "Stock and Inventory",
        "Sales",
        "HR Management",
    ]

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            dest="dry_run",
            help="Run command without actually sending emails",
        )
        parser.add_argument(
            "--limit",
            type=int,
            dest="limit",
            help="Limit the number of email sends to process (for testing)",
        )
        parser.add_argument(
            "--debug",
            action="store_true",
            dest="debug",
            help="Show detailed debug information",
        )

    def handle(self, *args, **options):
        self.dry_run = options.get("dry_run", False)
        self.limit = options.get("limit", None)
        self.debug = options.get("debug", False)

        if self.dry_run:
            self.stdout.write(
                self.style.WARNING("Running in DRY RUN mode - no emails will be sent")
            )

        try:
            # Get emails that need to be sent (user-company pairs)
            emails_to_send = self.get_emails_to_send()

            if not emails_to_send:
                self.stdout.write(
                    self.style.SUCCESS(
                        "No companies found that need subscription notifications."
                    )
                )
                return

            total_emails = len(emails_to_send)
            self.stdout.write(
                self.style.SUCCESS(
                    f"Found {total_emails} emails to send for companies that need subscription notifications."
                )
            )

            if self.limit:
                emails_to_send = emails_to_send[: self.limit]
                self.stdout.write(
                    self.style.WARNING(
                        f"Limited to processing {len(emails_to_send)} emails."
                    )
                )

            # Process emails in batches
            self.process_emails_in_batches(emails_to_send)

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Command failed with error: {str(e)}"))
            if self.debug:
                import traceback

                self.stdout.write(self.style.ERROR(traceback.format_exc()))
            raise CommandError(f"Command execution failed: {str(e)}")

    def get_emails_to_send(self) -> List[Dict]:
        """
        Get list of emails to send based on user-company relationships.
        Returns list of dicts with user and company info for each email to send.
        """
        self.stdout.write(
            "Identifying users and companies that need subscription notifications..."
        )

        # Get all required modules
        required_modules = Module.objects.filter(name__in=self.REQUIRED_MODULES)
        if required_modules.count() != len(self.REQUIRED_MODULES):
            missing_modules = set(self.REQUIRED_MODULES) - set(
                required_modules.values_list("name", flat=True)
            )
            self.stdout.write(
                self.style.WARNING(
                    f"Warning: Missing modules in database: {missing_modules}"
                )
            )

        # Get all users with valid email addresses
        users_with_emails = User.objects.filter(
            email__isnull=False, email__gt="", is_active=True
        )

        self.stdout.write(f"Found {users_with_emails.count()} users with valid emails")

        emails_to_send = []
        no_active_modules_count = 0
        partial_subscriptions_count = 0

        for user in users_with_emails:
            try:
                # Get all active companies linked to this user
                user_companies = Company.objects.filter(
                    user=user, is_deleted=False, is_active=True
                )

                if self.debug:
                    self.stdout.write(
                        f"User: {user.email}, Companies: {user_companies.count()}"
                    )

                for company in user_companies:
                    # Check active module subscriptions for this company
                    active_module_count = (
                        ModuleSubscription.objects.filter(
                            company_subscription__company=company,
                            company_subscription__status__in=["active", "trial"],
                            module__in=required_modules,
                            status="active",
                            is_active=True,
                        )
                        .values("module")
                        .distinct()
                        .count()
                    )

                    if self.debug:
                        self.stdout.write(
                            f"  Company: {company.company_name}, Active modules: {active_module_count}"
                        )

                    # Check if company needs notification
                    needs_notification = False
                    notification_reason = ""

                    if active_module_count == 0:
                        # No active modules
                        needs_notification = True
                        notification_reason = "no_active_modules"
                        no_active_modules_count += 1
                    elif 1 <= active_module_count <= 3:
                        # Partial subscription (1-3 modules out of 4)
                        needs_notification = True
                        notification_reason = "partial_subscription"
                        partial_subscriptions_count += 1
                    # If active_module_count == 4, company has all modules, no notification needed

                    if needs_notification:
                        emails_to_send.append(
                            {
                                "user": user,
                                "company": company,
                                "active_module_count": active_module_count,
                                "reason": notification_reason,
                            }
                        )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error processing user {user.email}: {str(e)}")
                )
                continue

        self.stdout.write(
            f"  - Companies with no active modules: {no_active_modules_count}"
        )
        self.stdout.write(
            f"  - Companies with partial subscriptions (1-3 modules): {partial_subscriptions_count}"
        )
        self.stdout.write(f"Total emails to send: {len(emails_to_send)}")

        return emails_to_send

    def process_emails_in_batches(self, emails_to_send: List[Dict]):
        """Process emails in batches with delays between batches"""
        total_emails = len(emails_to_send)
        successful_sends = 0
        failed_sends = 0

        self.stdout.write(
            f"Processing {total_emails} emails in batches of {self.BATCH_SIZE}..."
        )

        for i in range(0, total_emails, self.BATCH_SIZE):
            batch_end = min(i + self.BATCH_SIZE, total_emails)
            batch = emails_to_send[i:batch_end]
            batch_number = (i // self.BATCH_SIZE) + 1
            total_batches = (total_emails + self.BATCH_SIZE - 1) // self.BATCH_SIZE

            self.stdout.write(
                f"\nProcessing batch {batch_number}/{total_batches} (emails {i+1}-{batch_end})..."
            )

            # Process current batch
            batch_successful, batch_failed = self.process_batch(batch)
            successful_sends += batch_successful
            failed_sends += batch_failed

            # Add delay between batches (except after the last batch)
            if batch_end < total_emails:
                self.stdout.write(
                    f"Waiting {self.BATCH_DELAY} seconds before next batch..."
                )
                if not self.dry_run:
                    time.sleep(self.BATCH_DELAY)

        # Final summary
        self.stdout.write(
            self.style.SUCCESS(
                f"\n=== EMAIL SENDING SUMMARY ==="
                f"\nTotal emails processed: {total_emails}"
                f"\nSuccessful emails sent: {successful_sends}"
                f"\nFailed emails: {failed_sends}"
                f"\nSuccess rate: {(successful_sends/total_emails)*100:.1f}%"
                if total_emails > 0
                else "\nNo emails to process"
            )
        )

    def process_batch(self, email_batch: List[Dict]) -> tuple:
        """Process a single batch of emails"""
        successful_sends = 0
        failed_sends = 0

        for email_data in email_batch:
            try:
                user = email_data["user"]
                company = email_data["company"]
                reason = email_data["reason"]
                active_modules = email_data["active_module_count"]

                if self.send_notification_email(user, company, reason, active_modules):
                    successful_sends += 1
                    self.stdout.write(
                        f"  ✓ Sent email to {user.email} for {company.company_name} ({reason}: {active_modules} modules)"
                    )
                else:
                    failed_sends += 1
                    self.stdout.write(
                        f"  ✗ Failed to send email to {user.email} for {company.company_name}"
                    )

            except Exception as e:
                failed_sends += 1
                self.stdout.write(
                    self.style.ERROR(f"  ✗ Error sending email: {str(e)}")
                )

        return successful_sends, failed_sends

    def send_notification_email(
        self, user: User, company: Company, reason: str, active_modules: int
    ) -> bool:
        """Send notification email to a user for a specific company"""
        try:
            # Validate user data
            if not user.email:
                self.stdout.write(
                    self.style.WARNING(f"Skipping {user.username}: No email address")
                )
                return False

            if self.dry_run:
                return True

            # Send email using the existing send_email function
            result = send_email(
                recipient=user.email,
                subject=self.EMAIL_SUBJECT,
                template_dir=self.EMAIL_TEMPLATE,
                use_template=True,
                company_name=company.company_name,
                user_name=user.get_full_name() or user.username,
                # Additional context that might be useful in template
                active_modules_count=active_modules,
                reason=reason,
            )

            # Check if email was sent successfully
            return result == "EMAIL SENT"

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f"Exception sending email to {user.email} for {company.company_name}: {str(e)}"
                )
            )
            return False

    def get_user_companies_summary(self, user: User) -> Dict:
        """Get summary of user's companies and their subscription status (for debugging)"""
        summary = {
            "user_email": user.email,
            "total_companies": 0,
            "companies_needing_notification": 0,
            "companies_details": [],
        }

        try:
            user_companies = Company.objects.filter(
                user=user, is_deleted=False, is_active=True
            )

            summary["total_companies"] = user_companies.count()

            required_modules = Module.objects.filter(name__in=self.REQUIRED_MODULES)

            for company in user_companies:
                active_module_count = (
                    ModuleSubscription.objects.filter(
                        company_subscription__company=company,
                        company_subscription__status__in=["active", "trial"],
                        module__in=required_modules,
                        status="active",
                        is_active=True,
                    )
                    .values("module")
                    .distinct()
                    .count()
                )

                company_detail = {
                    "company_name": company.company_name,
                    "active_modules": active_module_count,
                    "needs_notification": active_module_count < 4,
                }

                summary["companies_details"].append(company_detail)

                if company_detail["needs_notification"]:
                    summary["companies_needing_notification"] += 1

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error getting user summary: {str(e)}"))

        return summary