import logging
from django.utils import timezone
from decimal import Decimal
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand, CommandError

from subscription_and_invoicing.models import (
    SubscriptionPlan,
    Module,
    CompanySubscription,
    ModuleSubscription,
    SubscriptionAudit,
    Invoice,
)

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = "Create expired subscriptions for Test111 company after clearing existing subscriptions"

    def handle(self, *args, **options):
        # Hardcoded values
        email = "<EMAIL>"
        company_name = "Test111"

        try:
            # Get the user by email
            user = User.objects.get(email=email)
            self.stdout.write(self.style.SUCCESS(f"Found user: {user.email}"))

            # Find the company by name
            companies = user.company_set.filter(company_name=company_name)
            if not companies.exists():
                raise CommandError(f"Company '{company_name}' not found for user {email}.")

            company = companies.first()

            # Verify the company belongs to the user
            if company.created_by != user:
                raise CommandError(f"Company '{company_name}' does not belong to user {email}.")

            self.stdout.write(
                self.style.SUCCESS(f"Processing expired subscriptions for company: {company.company_name}"))

            # Delete existing subscriptions for this company
            existing_subscriptions = CompanySubscription.objects.filter(company=company)

            # Log and count deletions
            subscription_count = existing_subscriptions.count()
            if subscription_count > 0:
                # Get the related module subscriptions to log the count
                module_subs_count = ModuleSubscription.objects.filter(
                    company_subscription__in=existing_subscriptions
                ).count()

                # Delete company subscriptions (will cascade delete module subscriptions)
                existing_subscriptions.delete()

                self.stdout.write(
                    self.style.WARNING(
                        f"Deleted {subscription_count} existing company subscriptions and "
                        f"{module_subs_count} module subscriptions for {company.company_name}."
                    )
                )
            else:
                self.stdout.write(self.style.WARNING(f"No existing subscriptions found for {company.company_name}."))

            # Define modules for expired subscriptions (using at least 2 modules)
            modules = Module.objects.filter(id__in=[1, 2])  # Using only 2 modules
            plan = SubscriptionPlan.objects.get(id=1)  # 6-month plan

            # Set expired subscription dates
            end_date = timezone.now() - timezone.timedelta(days=30)  # Expired 30 days ago
            start_date = end_date - timezone.timedelta(
                days=30 * plan.duration_months)  # Start date based on plan duration

            # Create the expired company subscription
            subscription = CompanySubscription.objects.create(
                company=company,
                access_type="paid",
                status="expired",  # Set status as expired
                created_by=user,
            )

            # Create expired subscriptions for each module
            for module in modules:
                ModuleSubscription.objects.create(
                    company_subscription=subscription,
                    module=module,
                    plan=plan,
                    is_active=False,  # Set as inactive since expired
                    start_date=start_date,
                    end_date=end_date,
                    status="expired",  # Set status as expired
                )

            # Generate an invoice for this expired subscription
            Invoice.objects.create(
                company=company,
                company_subscription=subscription,
                total_amount=plan.price,
                amount_paid=plan.price,
                balance_due=Decimal("0"),
                payment_status="paid",
                start_date=start_date,
                expiry_date=end_date,
            )

            # Log the subscription audit
            SubscriptionAudit.objects.create(
                company=company,
                subscription=subscription,
                action="Expired subscription created for testing",
                details={
                    "plan": plan.name,
                    "status": "expired",
                    "modules": [module.name for module in modules],
                    "expired_on": end_date.isoformat(),
                },
            )

            logger.info(f"Successfully created expired subscription for {company.company_name}")
            self.stdout.write(
                self.style.SUCCESS(
                    f"Expired subscriptions created successfully for {company.company_name} "
                    f"(Expired on: {end_date.strftime('%Y-%m-%d')})"
                )
            )

        except User.DoesNotExist:
            raise CommandError(f"User with email {email} does not exist.")
        except SubscriptionPlan.DoesNotExist:
            raise CommandError("Required subscription plan does not exist.")
        except Exception as e:
            logger.error(f"Error creating expired subscriptions for {company_name}: {e}")
            raise CommandError(f"Failed to create expired subscriptions: {e}")