from django.utils import timezone
from django.db.models import Q
from subscription_and_invoicing.models import (
    CompanySubscription,
    ModuleSubscription,
    AccessPath,
)


class SubscriptionService:
    def __init__(self, instance):
        self.instance = instance

    def get_subscription_info(self, representation):
        try:
            now = timezone.now()

            # Get all company subscriptions ordered by creation date
            # Use select_related to prefetch the plan relationship
            company_subscriptions = (
                CompanySubscription.objects.filter(company=self.instance)
                .select_related("plan")
                .order_by("-created_at")
            )

            # Initialize tracking variables for regular subscriptions
            active_subscriptions = []
            not_started_subscriptions = []
            has_any_active_module = False
            has_any_trial = False
            has_any_paid = False

            # Initialize tracking variables for token subscriptions
            token_subscriptions = []
            has_token_subscription = False
            token_balance_info = {
                "current_balance": 0,
                "previous_balance": 0,
                "status": "inactive",
                "total_purchased": 0,
            }

            for subscription in company_subscriptions:
                # Check if this is a token subscription
                is_token_subscription = subscription.access_type == AccessPath.TOKEN

                # Get module subscriptions for this company subscription
                # Use select_related to prefetch the module and plan relationships
                module_subs = ModuleSubscription.objects.filter(
                    company_subscription=subscription
                ).select_related("module", "plan")

                current_modules = []
                subscription_has_active_module = False
                subscription_has_not_started_module = False

                for ms in module_subs:
                    # Determine module status based on dates, status fields, and subscription type
                    # First check the model's is_active field
                    is_active = ms.is_active

                    # If is_active is False, check if it should be active based on status and dates
                    if not is_active:
                        # For token subscriptions, check differently
                        if is_token_subscription:
                            # Token subscriptions are active if:
                            # 1. Company subscription status is active
                            # 2. Token balance > 0 (if applicable)
                            # 3. Module subscription is not explicitly not_started
                            is_active = (
                                subscription.status == "active"
                                and ms.status != "not_started"
                                and (subscription.token_current_balance or 0) > 0
                            )
                        else:
                            # Regular subscription logic
                            is_active = (
                                ms.status == "active"
                                and ms.start_date
                                and ms.end_date
                                and ms.start_date <= now < ms.end_date
                            )

                    # Determine if this is a not_started module
                    if is_token_subscription:
                        # For token subscriptions, not_started is only based on status
                        is_not_started = ms.status == "not_started"
                    else:
                        # For regular subscriptions, check both status and dates
                        is_not_started = ms.status == "not_started" or (
                            ms.start_date and ms.start_date > now
                        )

                    # Handle special access types (BYPASS, TRIAL)
                    if subscription.access_type in [
                        AccessPath.BYPASS,
                        AccessPath.TRIAL,
                    ]:
                        # For bypass/trial, active if subscription status is active/trial AND module isn't not_started
                        if (
                            subscription.status in ["active", "trial"]
                            and not is_not_started
                        ):
                            is_active = True

                    # Get module plan data safely
                    module_plan_name = None
                    module_plan_id = None

                    # First try to get plan from module subscription directly
                    if ms.plan_id is not None:
                        try:
                            module_plan_name = ms.plan.name
                            module_plan_id = ms.plan.id
                        except (AttributeError, TypeError):
                            pass

                    # If that fails, try to get from subscription's plan
                    if (
                        module_plan_name is None or module_plan_id is None
                    ) and subscription.plan_id is not None:
                        try:
                            module_plan_name = subscription.plan.name
                            module_plan_id = subscription.plan.id
                        except (AttributeError, TypeError):
                            pass

                    # For token subscriptions, set plan info appropriately
                    if is_token_subscription and not module_plan_name:
                        module_plan_name = "Token Access"
                        module_plan_id = "token"

                    module_data = {
                        "module_subscription_id": ms.id,
                        "module": {
                            "id": ms.module.id,
                            "name": ms.module.name,
                            "code": ms.module.code,
                            "description": ms.module.description,
                            "is_premium": ms.module.is_premium,
                        },
                        "plan_name": module_plan_name,
                        "plan_id": module_plan_id,
                        "start_date": ms.start_date,
                        "end_date": ms.end_date,
                        "status": ms.status,
                        "is_active": is_active,
                        "is_not_started": is_not_started,
                        "access_type": subscription.access_type,
                        "is_token_based": is_token_subscription,
                    }

                    current_modules.append(module_data)

                    if is_active:
                        subscription_has_active_module = True
                        has_any_active_module = True

                    if is_not_started:
                        subscription_has_not_started_module = True

                # Handle subscription type tracking
                if is_token_subscription:
                    has_token_subscription = True

                    # Update token balance information
                    token_balance_info.update(
                        {
                            "current_balance": float(
                                subscription.token_current_balance or 0
                            ),
                            "previous_balance": float(
                                subscription.token_previous_balance or 0
                            ),
                            "status": subscription.token_status or "inactive",
                            "total_purchased": float(
                                subscription.token_current_balance or 0
                            )
                            + float(subscription.token_previous_balance or 0),
                        }
                    )

                    # Get subscription plan data safely
                    subscription_plan_name = None
                    subscription_plan_id = None

                    if subscription.plan_id is not None:
                        try:
                            subscription_plan_name = subscription.plan.name
                            subscription_plan_id = subscription.plan.id
                        except (AttributeError, TypeError):
                            pass

                    # For token subscriptions without specific plan
                    if not subscription_plan_name:
                        subscription_plan_name = "Token Subscription"
                        subscription_plan_id = "token"

                    token_subscription_data = {
                        "id": subscription.id,
                        "status": subscription.status,
                        "access_type": subscription.access_type,
                        "plan": subscription_plan_name,
                        "plan_id": subscription_plan_id,
                        "created_at": subscription.created_at,
                        "token_current_balance": float(
                            subscription.token_current_balance or 0
                        ),
                        "token_previous_balance": float(
                            subscription.token_previous_balance or 0
                        ),
                        "token_status": subscription.token_status or "inactive",
                        "modules": current_modules,
                        "has_active_modules": subscription_has_active_module,
                        "has_not_started_modules": subscription_has_not_started_module,
                        "is_token_subscription": True,
                    }

                    token_subscriptions.append(token_subscription_data)

                else:
                    # Regular subscription handling
                    is_trial = subscription.access_type == AccessPath.TRIAL
                    is_paid = subscription.access_type == AccessPath.PAID

                    if is_trial and subscription_has_active_module:
                        has_any_trial = True
                    if is_paid and subscription_has_active_module:
                        has_any_paid = True

                    # Get subscription plan data safely
                    subscription_plan_name = None
                    subscription_plan_id = None

                    if subscription.plan_id is not None:
                        try:
                            subscription_plan_name = subscription.plan.name
                            subscription_plan_id = subscription.plan.id
                        except (AttributeError, TypeError):
                            pass

                    subscription_data = {
                        "id": subscription.id,
                        "status": subscription.status,
                        "access_type": subscription.access_type,
                        "plan": subscription_plan_name,
                        "plan_id": subscription_plan_id,
                        "overall_sub_days": subscription.overall_sub_days,
                        "created_at": subscription.created_at,
                        "pos_included": subscription.pos_included,
                        "modules": current_modules,
                        "has_active_modules": subscription_has_active_module,
                        "has_not_started_modules": subscription_has_not_started_module,
                        "is_token_subscription": False,
                    }

                    # Add subscription to appropriate list(s)
                    if subscription_has_active_module:
                        active_subscriptions.append(subscription_data)

                    if subscription_has_not_started_module:
                        not_started_subscriptions.append(subscription_data)

                # Set the representation data for regular subscriptions
            representation["is_subscription"] = has_any_active_module
            representation["is_free_trial"] = has_any_trial
            representation["is_paid_subscription"] = has_any_paid
            representation["active_subscriptions"] = active_subscriptions
            representation["not_started_subscriptions"] = not_started_subscriptions

            # Set the representation data for token subscriptions
            representation["is_token_subscription"] = has_token_subscription
            representation["token_subscriptions"] = token_subscriptions
            representation["token_balance_info"] = token_balance_info

            # Add combined subscription status
            representation["has_any_subscription"] = (
                has_any_active_module or has_token_subscription
            )

        except Exception as e:
            # Enhanced error handling
            representation["is_subscription"] = False
            representation["is_free_trial"] = False
            representation["is_paid_subscription"] = False
            representation["active_subscriptions"] = []
            representation["not_started_subscriptions"] = []
            representation["is_token_subscription"] = False
            representation["token_subscriptions"] = []
            representation["token_balance_info"] = {
                "current_balance": 0,
                "previous_balance": 0,
                "status": "inactive",
                "total_purchased": 0,
            }
            representation["has_any_subscription"] = False
            representation["subscription_error"] = str(e)

        return representation
