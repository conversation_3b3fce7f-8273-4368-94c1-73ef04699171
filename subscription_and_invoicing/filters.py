from django.core.management.base import BaseCommand
from django.utils import timezone
from subscription_and_invoicing.models import Invoice

# from subscription_and_invoicing.views import subscription_renewal
from django.contrib.auth.models import User

from subscription_and_invoicing.views import SubscriptionRenewalAPIView


class Command(BaseCommand):
    help = "Checks for subscriptions due for renewal and triggers renewal process"

    def handle(self, *args, **options):
        due_invoices = Invoice.objects.filter(
            is_active=True,
            expiry_date__lte=timezone.now().date() + timezone.timedelta(days=7),
        )

        for invoice in due_invoices:
            self.stdout.write(f"Processing renewal for Invoice {invoice.id}")

            # Simulate a request object
            class RequestSimulator:
                def __init__(self):
                    self.data = {
                        "company_id": invoice.company.id,
                        "module_id": invoice.module.id,
                    }
                    self.user = User.objects.get(
                        username="admin"
                    )  # or however you want to set this

            request = RequestSimulator()

            view = SubscriptionRenewalAPIView.as_view()
            response = view(request)

            if response.status_code == 201:
                self.stdout.write(
                    self.style.SUCCESS(f"<PERSON><PERSON> initiated for Invoice {invoice.id}")
                )
            else:
                self.stdout.write(
                    self.style.ERROR(
                        f"Renewal failed for Invoice {invoice.id}: {response.data}"
                    )
                )
