from django.views.decorators.csrf import csrf_exempt
from rest_framework import serializers
from datetime import datetime, timedelta, date

# from django.contrib.auth.models import User
from django.utils import timezone
from django.db.models import Sum
from django.db import models

from core.models import PaystackPayment
from helpers.reusable_functions import is_valid_uuid
from invoicing import models as inv
from invoicing.models import InvoiceItem
from requisition.models import Company
from sales_app.models import Customer
from stock_inventory.models import Branch
from subscription_and_invoicing import models as subscription
from performance_sales_metrics_dashboard import models as perf
from django.conf import settings
from django.db import transaction
from decimal import Decimal
from django.contrib.auth import get_user_model

from subscription_and_invoicing.models import (
    Invoice,
    InvoiceAudit,
    InvoiceModuleDetail,
    InvoiceTransaction,
    Module,
    SubscriptionPlan,
    UserCompanyDetails,
    ModuleSubscription,
    CompanySubscription,
    AccessPath,
    TokenStatus,
    TokenStatus,
)

from subscription_and_invoicing.models import UserCompanyDetails

User = get_user_model()


class SubscriptionPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionPlan
        fields = "__all__"


valid_plans = SubscriptionPlan.objects.filter(is_active=True)
valid_durations = valid_plans.values_list("duration_months", flat=True).distinct()


class SubscriptionModuleSerializer(serializers.Serializer):
    module_id = serializers.PrimaryKeyRelatedField(
        queryset=Module.objects.all(), source="module"
    )
    # Use IntegerField instead of PrimaryKeyRelatedField to avoid early validation
    plan = serializers.IntegerField(required=False, allow_null=True)

    def validate(self, attrs):
        # Get the parent serializer context to check if this is a token subscription
        # self.parent is ListSerializer, so we need self.parent.parent to get CreateInvoiceSerializer
        main_serializer = self.parent.parent if self.parent else None
        parent_data = main_serializer.initial_data if main_serializer else {}
        is_token = parent_data.get("is_token", False)

        if not is_token:
            # For regular subscriptions, validate and convert plan ID to plan object
            plan_id = attrs.get("plan")
            if plan_id is None:
                raise serializers.ValidationError(
                    "Plan is required for regular subscriptions."
                )

            try:
                plan = SubscriptionPlan.objects.get(id=plan_id, is_active=True)
                if plan.duration_months not in valid_durations:
                    raise serializers.ValidationError(
                        f"Invalid duration. Only {list(valid_durations)} months are allowed."
                    )
                attrs["plan"] = plan  # Convert ID to object for regular subscriptions
            except SubscriptionPlan.DoesNotExist:
                raise serializers.ValidationError(
                    f'Invalid plan ID "{plan_id}" - object does not exist.'
                )
        else:
            # For token subscriptions, remove plan from attrs (we don't need it)
            attrs.pop("plan", None)

        return attrs


class CreateInvoiceSerializer(serializers.Serializer):
    company_id = serializers.UUIDField(required=False, allow_null=True)
    subscription_modules = SubscriptionModuleSerializer(many=True)
    upgrade = serializers.BooleanField(required=False, default=False)
    existing_subscription_id = serializers.UUIDField(required=False, allow_null=True)
    is_token = serializers.BooleanField(required=False, default=False)
    token_units = serializers.DecimalField(
        max_digits=10, decimal_places=2, required=False, allow_null=True
    )
    force_cleanup = serializers.BooleanField(
        required=False, default=False
    )  # New field for cleanup

    # Token pricing constant
    TOKEN_PRICE_PER_UNIT = 10  # 10 naira per token unit

    def validate_subscription_modules(self, value):
        """Field-level validation for subscription_modules"""
        if not value:
            raise serializers.ValidationError("At least one module must be selected.")
        return value

    def validate(self, attrs):
        """Object-level validation with conditional logic based on is_token"""
        is_token = attrs.get("is_token", False)
        subscription_modules = attrs.get("subscription_modules", [])

        if is_token:
            # For token subscriptions: validate token-specific fields
            token_units = attrs.get("token_units")
            if token_units is None or token_units <= 0:
                raise serializers.ValidationError(
                    {
                        "token_units": "Token units must be provided and greater than 0 for token subscriptions."
                    }
                )

            # For token subscriptions, ensure modules exist
            for module_data in subscription_modules:
                if "module" not in module_data:
                    raise serializers.ValidationError(
                        {
                            "subscription_modules": "Module is required for each subscription module."
                        }
                    )

        else:
            # For regular subscriptions: validate that both module and plan are provided
            for module_data in subscription_modules:
                if "plan" not in module_data or module_data["plan"] is None:
                    raise serializers.ValidationError(
                        {
                            "subscription_modules": "Plan is required for regular subscriptions."
                        }
                    )

        # Now proceed with the main validation and creation logic
        return self._process_subscription_creation(attrs)

    @transaction.atomic
    def _process_subscription_creation(self, attrs):
        """Main method to handle subscription creation logic"""
        request = self.context.get("request")
        user = request.user
        is_token = attrs.get("is_token", False)

        # Step 1: Resolve company
        company = attrs.get("company_id")
        if company:
            try:
                company = Company.objects.get(id=company)
            except Company.DoesNotExist:
                raise serializers.ValidationError(
                    {"company_id": "Invalid company ID provided."}
                )
        else:
            company = user.default_company

        if not company:
            raise serializers.ValidationError({"company_id": "No company available."})

        # Step 1.5: Check for existing subscriptions and handle cleanup
        existing_subscriptions = (
            CompanySubscription.objects.filter(
                company=company, status__in=["active", "inactive"]
            )
            .select_related()
            .prefetch_related("modules")
        )

        if existing_subscriptions:
            existing_sub = existing_subscriptions.first()

            if existing_sub.status == "active":
                # Get module names for better error message
                module_names = [module.name for module in existing_sub.modules.all()]
                raise serializers.ValidationError(
                    {
                        "company": {
                            "message": "Company already has an active subscription.",
                            "existing_subscription": {
                                "id": str(existing_sub.id),
                                "status": existing_sub.status,
                                "access_type": existing_sub.access_type,
                                "modules": module_names,
                                "created_date": (
                                    existing_sub.created_at.isoformat()
                                    if hasattr(existing_sub, "created_at")
                                    else None
                                ),
                            },
                            "options": [
                                "Use the upgrade option to add more modules",
                                "Cancel the existing subscription first",
                                "Wait for the current subscription to expire",
                            ],
                        }
                    }
                )
            elif existing_sub.status == "inactive":
                # There's a pending subscription (unpaid invoice)
                module_names = [module.name for module in existing_sub.modules.all()]
                raise serializers.ValidationError(
                    {
                        "company": {
                            "message": "Company has a pending subscription with unpaid invoice.",
                            "existing_subscription": {
                                "id": str(existing_sub.id),
                                "status": existing_sub.status,
                                "access_type": existing_sub.access_type,
                                "modules": module_names,
                                "invoice_ref": existing_sub.invoice_ref,
                                "created_date": (
                                    existing_sub.created_at.isoformat()
                                    if hasattr(existing_sub, "created_at")
                                    else None
                                ),
                            },
                            "options": [
                                "Complete payment for the pending subscription",
                                "Cancel the pending subscription first",
                            ],
                        }
                    }
                )

        # Additional check: Look for orphaned ModuleSubscriptions that might cause conflicts
        # This handles cases where CompanySubscription was deleted but ModuleSubscriptions remain
        requested_modules = [
            module_data["module"] for module_data in attrs["subscription_modules"]
        ]
        force_cleanup = attrs.get("force_cleanup", False)

        # Check for any existing ModuleSubscriptions for this company with the requested modules
        conflicting_module_subs = ModuleSubscription.objects.filter(
            company_subscription__company=company, module__in=requested_modules
        ).select_related("company_subscription", "module")

        if conflicting_module_subs.exists():
            if force_cleanup:
                # Clean up orphaned module subscriptions
                conflicting_module_subs.delete()
                # Also clean up any orphaned company subscriptions without module subscriptions
                orphaned_company_subs = (
                    CompanySubscription.objects.filter(company=company)
                    .annotate(module_count=models.Count("modulesubscription"))
                    .filter(module_count=0)
                )
                orphaned_company_subs.delete()
            else:
                conflicting_info = []
                for mod_sub in conflicting_module_subs:
                    conflicting_info.append(
                        {
                            "module_name": mod_sub.module.name,
                            "module_id": mod_sub.module.id,
                            "company_subscription_id": mod_sub.company_subscription.id,
                            "status": mod_sub.company_subscription.status,
                        }
                    )

                raise serializers.ValidationError(
                    {
                        "modules": {
                            "message": "Some requested modules already have existing subscriptions.",
                            "conflicting_modules": conflicting_info,
                            "solution": "Add 'force_cleanup': true to automatically clean up orphaned data, or contact support.",
                        }
                    }
                )

        # Step 2: Resolve branch and customer
        branch = (
            company.company_branch.filter(is_super_branch=True).first()
            or company.company_branch.first()
        )
        if not branch:
            raise serializers.ValidationError(
                "No branch found for the selected company."
            )

        customer = Customer.objects.filter(
            company=company, branch=branch, email=user.email
        ).last()
        if not customer:
            customer = Customer.objects.create(
                company=company,
                branch=branch,
                email=user.email,
                created_by=user,
                name=user.get_full_name(),
                phone=getattr(user, "phone_number", None),
            )

        # Route to appropriate implementation based on is_token
        if is_token:
            return self._handle_token_subscription(attrs, company, customer, user)
        else:
            return self._handle_regular_subscription(attrs, company, customer, user)

    # Replace your _handle_token_subscription method with this improved version

    def _handle_regular_subscription(self, attrs, company, customer, user):
        """Handle regular subscription creation (existing implementation)"""
        # Step 3: Handle upgrade
        is_upgrade = attrs.get("upgrade", False)
        existing_sub = None
        if is_upgrade:
            existing_id = attrs.get("existing_subscription_id")
            if not existing_id:
                raise serializers.ValidationError(
                    "Existing subscription ID required for upgrade."
                )
            try:
                existing_sub = CompanySubscription.objects.get(
                    id=existing_id, company=company, status="active"
                )
            except CompanySubscription.DoesNotExist:
                raise serializers.ValidationError(
                    "Invalid or inactive subscription for upgrade."
                )

        # Step 4: Create company subscription (always inactive at this stage)
        total_price = 0
        batch_id = Invoice.generate_batch_id()

        company_subscription = CompanySubscription.objects.create(
            company=company,
            status="inactive",
            created_by=user,
            is_upgrade=is_upgrade,
            existing_subscription=existing_sub,
            access_type=AccessPath.PAID,  # explicitly setting paid
        )

        # Step 5: Create module subscriptions (not active yet)
        modules_to_add = []
        for module_data in attrs["subscription_modules"]:
            module = module_data["module"]
            plan = module_data[
                "plan"
            ]  # For regular subscriptions, plan is already validated

            start_date = (
                existing_sub.end_date if is_upgrade and existing_sub else timezone.now()
            )
            end_date = start_date + timedelta(days=plan.duration_months * 30)

            ModuleSubscription.objects.create(
                company_subscription=company_subscription,
                module=module,
                plan=plan,
                is_active=False,
                status="not_started",
                start_date=start_date,
                end_date=end_date,
                pending_balance=plan.price,
                invoice_ref=batch_id,
            )
            modules_to_add.append(module)
            total_price += plan.price

        company_subscription.modules.set(modules_to_add)
        company_subscription.invoice_ref = batch_id
        company_subscription.save()

        # Step 6: Create invoice
        invoice, payment_link = self.create_invoice(
            company, attrs["subscription_modules"], customer, user, batch_id
        )

        return {
            "subscription_type": "regular",
            "invoice_reference": invoice.invoice_reference,
            "amount_payable": invoice.balance_due,
            "total_price": total_price,
            "subscription_modules": [
                {
                    "name": item.module.name,
                    "price": item.unit_price,
                    "total_price": item.total_price,
                }
                for item in invoice.invoicemoduledetail_set.all()
            ],
            "customer_name": customer.name,
            "start_date": invoice.start_date,
            "expiry_date": invoice.expiry_date,
            "payment_link": payment_link,
        }

    def _handle_token_subscription(self, attrs, company, customer, user):
        """Handle token-based subscription creation with proper balance handling"""
        token_units = attrs.get("token_units")

        # CORRECT TOKEN COST CALCULATION: token_units × 10 naira per token
        token_cost = float(token_units) * self.TOKEN_PRICE_PER_UNIT

        # Generate batch ID for token invoice
        batch_id = Invoice.generate_batch_id()

        # Get current token balance before creating new subscription
        current_company_sub = CompanySubscription.objects.filter(
            company=company,
            access_type=AccessPath.TOKEN,
            status__in=["active", "inactive"],
        ).first()

        previous_token_balance = 0
        if current_company_sub:
            previous_token_balance = current_company_sub.token_current_balance or 0

        # Create company subscription with token access (inactive until payment)
        company_subscription = CompanySubscription.objects.create(
            company=company,
            status="inactive",  # Token subscriptions start inactive until payment
            created_by=user,
            access_type=AccessPath.TOKEN,
            token_current_balance=float(token_units),
            token_previous_balance=previous_token_balance,  # Previous balance before this request
            token_status=TokenStatus.INACTIVE,  # Inactive until payment
            invoice_ref=batch_id,
        )

        # Collect all requested modules (for token subscriptions, plan is ignored)
        modules_to_add = []
        for module_data in attrs["subscription_modules"]:
            module = module_data[
                "module"
            ]  # Only module is needed for token subscriptions
            modules_to_add.append(module)

        # AUTOMATICALLY ADD SALES MODULE FOR TOKEN SUBSCRIPTIONS
        try:
            sales_module = Module.objects.get(code="SALES")
            # Check if SALES module is not already in the list
            if sales_module not in modules_to_add:
                modules_to_add.append(sales_module)
        except Module.DoesNotExist:
            raise serializers.ValidationError(
                "SALES module not found. Token subscriptions require SALES module access."
            )

        # Set all modules for the subscription
        company_subscription.modules.set(modules_to_add)
        company_subscription.save()

        # Create module subscriptions for token access with proper date handling
        start_date = timezone.now()

        # Create module subscriptions for ALL modules (including SALES)
        for module in modules_to_add:
            ModuleSubscription.objects.create(
                company_subscription=company_subscription,
                module=module,
                plan=None,  # No specific plan for token subscriptions
                is_active=False,  # Will be activated after payment
                status="not_started",
                start_date=start_date,
                end_date=None,  # Set to None for unlimited token access
                pending_balance=0,  # Token cost covers all modules, no individual module balance
                invoice_ref=batch_id,
            )

        # Create token invoice
        invoice, payment_link = self.create_token_invoice(
            company, token_units, token_cost, customer, user, batch_id, modules_to_add
        )

        return {
            "subscription_type": "token",
            "invoice_reference": invoice.invoice_reference,
            "token_units": float(token_units),
            "token_cost": token_cost,
            "price_per_token": self.TOKEN_PRICE_PER_UNIT,
            "amount_payable": invoice.balance_due,
            "subscription_modules": [
                {
                    "name": module.name,
                    "code": module.code,
                    "access_type": "token",
                    "duration": "unlimited",  # Until tokens are exhausted
                    "auto_included": module.code
                    == "SALES",  # Flag to show which was auto-included
                }
                for module in modules_to_add
            ],
            "customer_name": customer.name,
            "start_date": start_date,
            "expiry_date": None,  # None represents unlimited token-based access
            "payment_link": payment_link,
            "status": "inactive",
            "message": f"Token subscription created successfully. {int(token_units)} tokens at ₦{self.TOKEN_PRICE_PER_UNIT}/token = ₦{token_cost:,.2f}. SALES module included automatically. Payment required to activate unlimited access until tokens are exhausted.",
            "note": "Token subscriptions provide unlimited access duration until tokens are consumed.",
            "token_balance_info": {
                "current_balance": float(token_units),
                "previous_balance": previous_token_balance,
                "total_balance_after_payment": previous_token_balance
                + float(token_units),
            },
        }

    @staticmethod
    @transaction.atomic
    def create_token_invoice(
        company, token_units, token_cost, customer, created_by, batch_id, modules
    ):
        """Create invoice specifically for token purchases with proper payment link generation"""

        # Create the internal invoice record
        invoice = Invoice.objects.create(
            company=company,
            created_by=created_by,
            total_amount=token_cost,
            balance_due=token_cost,
            start_date=timezone.now(),
            expiry_date=timezone.now() + timedelta(days=12 * 30),
            batch_id=batch_id,
        )

        # Get the first module as a representative for token subscription
        representative_module = Module.objects.first()

        if not representative_module:
            raise serializers.ValidationError("No modules found in the system.")

        # Get all module names for description
        all_modules = Module.objects.all()
        all_module_names = [module.name for module in all_modules]

        # Create single InvoiceModuleDetail representing token access to all modules
        invoice_detail = InvoiceModuleDetail.objects.create(
            invoice=invoice,
            module=representative_module,
            quantity=int(token_units),
            unit_price=CreateInvoiceSerializer.TOKEN_PRICE_PER_UNIT,
            discount=0.0,
            total_price=token_cost,
            plan_label=f"Token Subscription - {int(token_units)} tokens for ALL modules ({', '.join(all_module_names)})",
        )

        # Prepare items for external invoice generation
        items = [
            {
                "item_description": f"Token Subscription - {int(token_units)} tokens for unlimited access to ALL modules: {', '.join(all_module_names)}",
                "unit_price": CreateInvoiceSerializer.TOKEN_PRICE_PER_UNIT,
                "quantity": int(token_units),
                "discount": 0.0,
            }
        ]

        # Generate the invoice through InvoiceItem
        generated_invoice = InvoiceItem.generate_invoice(
            company=company,
            items=items,
            created_by=created_by,
            customer=customer,
            tax_rate=0.0,
            description=f"Token Subscription Invoice - {int(token_units)} tokens at ₦{CreateInvoiceSerializer.TOKEN_PRICE_PER_UNIT}/token for unlimited access to ALL system modules",
            discount_value=0.0,
            delivery_fee=0.0,
            received_part_payment=False,
            amount_received=0.0,
            branch=customer.branch,
            batch_id=batch_id,
        )

        # Generate payment link using the correct email and better error handling
        payment_link = None
        try:
            from core.models import PaystackPayment

            # Use customer email if available, otherwise use company user email
            email_for_payment = (
                customer.email if (customer and customer.email) else company.user.email
            )

            if email_for_payment:
                payment_methods = PaystackPayment.generate_payment_link(
                    email=email_for_payment,
                    amount=float(token_cost),
                    reference=batch_id,
                )

                # Better handling of payment link extraction
                if isinstance(payment_methods, dict):
                    payment_link = payment_methods.get(
                        "payment_link"
                    ) or payment_methods.get("link")
                elif hasattr(payment_methods, "payment_link"):
                    payment_link = payment_methods.payment_link
                elif hasattr(payment_methods, "link"):
                    payment_link = payment_methods.link
                else:
                    # If it's a string, use it directly
                    payment_link = str(payment_methods) if payment_methods else None

            # Debug logging - remove in production
            print(f"Debug: Payment methods response: {payment_methods}")
            print(f"Debug: Final payment link: {payment_link}")

        except Exception as e:
            print(f"Error generating payment link: {e}")
            # Don't raise error, just log and continue without payment link

        return invoice, payment_link

    # Additional method to handle payment link generation for regular subscriptions
    @staticmethod
    @transaction.atomic
    def create_invoice(company, subscription_modules, customer, created_by, batch_id):
        """Create invoice for regular subscriptions with improved payment link handling"""
        total_amount_due = 0

        invoice = Invoice.objects.create(
            company=company,
            created_by=created_by,
            total_amount=0,
            balance_due=0,
            start_date=timezone.now(),
            expiry_date=timezone.now() + timedelta(days=12 * 30),
            batch_id=batch_id,
        )

        items = []
        for module_data in subscription_modules:
            module = module_data["module"]
            plan = module_data["plan"]

            total_price = plan.price
            total_amount_due += total_price

            InvoiceModuleDetail.objects.create(
                invoice=invoice,
                module=module,
                quantity=1,
                unit_price=total_price,
                discount=0.0,
                total_price=total_price,
            )

            items.append(
                {
                    "item_description": module.name,
                    "unit_price": total_price,
                    "quantity": 1,
                    "discount": 0.0,
                }
            )

        invoice.total_amount = total_amount_due
        invoice.balance_due = max(0, total_amount_due - invoice.settled_amount)
        invoice.save()

        generated_invoice = InvoiceItem.generate_invoice(
            company=company,
            items=items,
            created_by=created_by,
            customer=customer,
            tax_rate=0.0,
            description="Subscription Invoice",
            discount_value=0.0,
            delivery_fee=0.0,
            received_part_payment=False,
            amount_received=0.0,
            branch=customer.branch,
            batch_id=batch_id,
        )

        # Generate payment link with better error handling
        payment_link = None
        try:
            from core.models import PaystackPayment

            # Use customer email if available, otherwise use company user email
            email_for_payment = (
                customer.email if (customer and customer.email) else company.user.email
            )

            if email_for_payment:
                payment_methods = PaystackPayment.generate_payment_link(
                    email=email_for_payment,
                    amount=float(total_amount_due),
                    reference=batch_id,
                )

                # Better handling of payment link extraction
                if isinstance(payment_methods, dict):
                    payment_link = payment_methods.get(
                        "payment_link"
                    ) or payment_methods.get("link")
                elif hasattr(payment_methods, "payment_link"):
                    payment_link = payment_methods.payment_link
                elif hasattr(payment_methods, "link"):
                    payment_link = payment_methods.link
                else:
                    # If it's a string, use it directly
                    payment_link = str(payment_methods) if payment_methods else None

        except Exception as e:
            print(f"Error generating payment link: {e}")
            # Don't raise error, just log and continue without payment link

        return invoice, payment_link


class TheSubscriptionModuleSerializer(serializers.Serializer):
    module_id = serializers.IntegerField()
    plan = serializers.IntegerField()


class TransferCreateInvoiceSerializer(serializers.Serializer):
    company_id = serializers.UUIDField(required=False, allow_null=True)
    subscription_modules = TheSubscriptionModuleSerializer(many=True)

    def validate_subscription_modules(self, value):
        if not value:
            raise serializers.ValidationError("At least one module must be selected.")
        return value

    @transaction.atomic
    def validate(self, attrs):
        request = self.context.get("request")
        user = request.user

        company = None
        if "company_id" in attrs and attrs["company_id"] is not None:
            try:
                company = Company.objects.get(id=attrs["company_id"])
            except Company.DoesNotExist:
                raise serializers.ValidationError(
                    {"company_id": "Invalid company ID provided."}
                )
        else:
            company = user.default_company

        if not company:
            raise serializers.ValidationError(
                {"company_id": "No company found. Provide one or set a default."}
            )

        branch = user.default_branch
        if not branch:
            raise serializers.ValidationError("User's default branch is not set up.")

        batch_id = Invoice.generate_batch_id()

        company_subscription = CompanySubscription.objects.create(
            company=company, status="inactive", created_by=user
        )

        module_subscriptions = []

        # Validate and prepare data
        subscription_data = []
        for item in attrs["subscription_modules"]:
            try:
                module = Module.objects.get(id=item["module_id"])
                plan = SubscriptionPlan.objects.get(id=item["plan"])
            except (Module.DoesNotExist, SubscriptionPlan.DoesNotExist):
                raise serializers.ValidationError(f"Invalid module_id or plan: {item}")

            # Create ModuleSubscription
            ModuleSubscription.objects.create(
                company_subscription=company_subscription,
                module=module,
                plan=plan,
                is_active=False,
                start_date=timezone.now(),
                end_date=timezone.now() + timedelta(days=plan.duration_months * 30),
                pending_balance=plan.price,
                status="not_started",
            )

            module_subscriptions.append(module)
            subscription_data.append({"module": module, "plan": plan})

        company_subscription.modules.set(module_subscriptions)
        company_subscription.invoice_ref = batch_id
        company_subscription.save()

        ModuleSubscription.objects.filter(
            company_subscription=company_subscription
        ).update(invoice_ref=batch_id)

        invoice = self.create_invoice(company, subscription_data, user, batch_id)

        from account.helpers.core_banking import CoreBankingService
        import json

        # one_time_account_resp = CoreBankingService.get_one_time_account(request_reference=batch_id)
        # one_time_account = one_time_account_resp.filter("data", {}).get("account_number")
        one_time_account_resp = CoreBankingService().get_one_time_account(
            request_reference=batch_id
        )
        # print("ONE TIME ACCOUNT DATA ::::::::::::", one_time_account_resp)

        if isinstance(one_time_account_resp, str):
            try:
                one_time_account_resp = json.loads(one_time_account_resp)
            except json.JSONDecodeError:
                raise serializers.ValidationError(
                    "Unexpected response from Core Banking. Contact support."
                )

        account_data = one_time_account_resp.get("data")
        if not account_data or "account_number" not in account_data:
            raise serializers.ValidationError(
                "Core banking account number not returned. Please retry."
            )

        one_time_account = account_data["account_number"]

        return {
            "invoice_reference": invoice.invoice_reference,
            "amount_payable": invoice.balance_due,
            "one_time_account": account_data.get(
                "account_number"
            ),  # Or just use `one_time_account`
            "bank_name": account_data.get("bank_name"),
            "account_number": account_data.get("account_number"),
            "account_name": account_data.get("account_name"),
            "subscription_modules": [
                {
                    "name": item.module.name,
                    "plan": item.plan.name,
                    "price": item.unit_price,
                }
                for item in invoice.invoicemoduledetail_set.all()
            ],
            "start_date": invoice.start_date,
            "expiry_date": invoice.expiry_date,
        }

    @staticmethod
    @transaction.atomic
    def create_invoice(company, subscription_data, created_by, batch_id):
        total_amount_due = 0

        invoice = Invoice.objects.create(
            company=company,
            created_by=created_by,
            total_amount=0,
            balance_due=0,
            start_date=timezone.now(),
            expiry_date=timezone.now() + timedelta(days=12 * 30),
            batch_id=batch_id,
        )

        for data in subscription_data:
            module = data["module"]
            plan = data["plan"]

            unit_price = plan.price
            quantity = plan.duration_months
            total_price = unit_price * quantity

            InvoiceModuleDetail.objects.create(
                invoice=invoice,
                module=module,
                quantity=quantity,
                unit_price=unit_price,
                discount=0.0,
                total_price=total_price,
                plan_label=str(plan),
            )

            total_amount_due += total_price

        invoice.total_amount = total_amount_due
        invoice.balance_due = max(0, total_amount_due - invoice.settled_amount)
        invoice.save()

        return invoice


class ModuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Module
        fields = "__all__"


# class SubscriptionModuleSerializer(serializers.ModelSerializer):
#     subscription_type_name = serializers.CharField(source='subscription_type.name')
#
#     class Meta:
#         model = subscription.SubscriptionModule
#         fields = ['subscription_type_name', 'duration_in_months', 'start_date', 'invoice']


class InvoiceSerializer(serializers.ModelSerializer):
    modules = SubscriptionModuleSerializer(many=True, read_only=True)

    class Meta:
        model = subscription.Invoice
        # fields = [
        #     'id', 'company', 'modules', 'amount', 'amount_due',
        #     'start_date', 'expiry_date', 'sales_officer',
        #     'amount_brought_forward', 'used_excess_payment',
        #     'payment_status'
        # ]
        exclude = [
            "module",
            "settled_amount",
            "invoice_reference",
        ]


# class InvoiceSerializer(serializers.ModelSerializer):
#     modules = SubscriptionModuleSerializer(many=True, read_only=True)
#     amount_due = serializers.SerializerMethodField()
#
#     class Meta:
#         model = subscription.Invoice
#         exclude = ["module", "settled_amount", "invoice_reference", ]
#
#     def get_amount_due(self, obj):
#         # Assuming you want to calculate amount_due based on some related data.
#         # Here's a simplified example. Adjust the query according to your actual data structure.
#
#         # Example: Querying related `SubscriptionModule` instances linked to this invoice
#         sub_invoice = subscription.Invoice.objects.filter(id=obj.id).first()
#
#         if sub_invoice:
#             # Your logic for amount_due based on the sub_invoice
#             # For example, summing prices from related modules
#             total_amount = sub_invoice.modules.aggregate(total=models.Sum('subscription_type__price'))['total'] or 0
#             amount_due = max(0, total_amount - sub_invoice.settled_amount)
#             return amount_due
#         return None


# class InvoiceTransactionSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = subscription.InvoiceTransaction
#         fields = '__all__'
#


class InvoiceTransactionSerializer(serializers.ModelSerializer):
    payment_details = serializers.SerializerMethodField()

    class Meta:
        model = subscription.InvoiceTransaction
        fields = "__all__"

    def get_payment_details(self, obj):
        # This method retrieves the payment details from the context if available.
        return self.context.get("payment_details")


class GetInvoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = subscription.Invoice
        fields = "__all__"


class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = "__all__"


# class SubscriptionModuleSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = subscription.SubscriptionModule
#         fields = '__all__'


class SubscriptionTypesSerializer(serializers.ModelSerializer):
    class Meta:
        model = subscription.SubscriptionType
        fields = "__all__"


class CommissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = subscription.Commission
        fields = "__all__"


class InvoiceUpdateSerializer(serializers.ModelSerializer):
    subscription_types = serializers.ListField(
        child=serializers.IntegerField(), write_only=True
    )
    duration_in_months = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = subscription.Invoice
        fields = [
            "subscription_types",
            "duration_in_months",
        ]  # Include other fields if necessary

    def update(self, instance, validated_data):
        subscription_types = validated_data.get("subscription_types", [])
        duration_in_months = validated_data.get(
            "duration_in_months", 3
        )  # Default to 3 months if not provided

        # Current modules associated with the invoice
        existing_modules = subscription.SubscriptionModule.objects.filter(
            invoice=instance
        )

        # List of existing subscription type IDs
        existing_subscription_type_ids = [
            module.subscription_type.id for module in existing_modules
        ]

        # Remove modules that are no longer in the payload
        modules_to_remove = [
            module
            for module in existing_modules
            if module.subscription_type.id not in subscription_types
        ]
        for module in modules_to_remove:
            instance.amount -= module.subscription_type.price
            instance.amount_due -= module.subscription_type.price
            module.delete()

        # Add or update the modules
        for subscription_type_id in subscription_types:
            if subscription_type_id not in existing_subscription_type_ids:
                subscription_type = subscription.SubscriptionType.objects.get(
                    id=subscription_type_id
                )
                subscription.SubscriptionModule.objects.create(
                    subscription_type=subscription_type,
                    duration_in_months=duration_in_months,  # Use the provided duration
                    invoice=instance,
                )
                instance.amount += subscription_type.price
                instance.amount_due += subscription_type.price
            else:
                # Update the duration for existing modules if needed
                module = subscription.SubscriptionModule.objects.get(
                    subscription_type_id=subscription_type_id, invoice=instance
                )
                if module.duration_in_months != duration_in_months:
                    module.duration_in_months = duration_in_months
                    module.save()

        # Ensure amount_due is correctly calculated
        instance.recalculate_amount_due()
        instance.save()

        return instance

    def to_representation(self, instance):
        return InvoiceSerializer(instance).data


# class InvoiceUpdateSerializer(serializers.Serializer):
#     subscription_types = serializers.ListField()
#
#     def update(self, instance, validated_data):
#         subscription_types = validated_data.get('subscription_types')
#         subscription_type_qs = subscription.SubscriptionType.objects.filter(id__in=subscription_types)
#         duration = instance.subscriptionmodule_set.first().subscritpion_in_months
#         for subscription_type in subscription_type_qs:
#             module = models.SubscriptionModule.objects.get_or_create(subscritpion_type=subscription_type,
#                                                               subscritpion_in_months=duration, invoice=instance)
#         instance.recalculate_amount_due()
#
#         return instance

# class Meta:
#     model = Invoice
#     fields = ['amount', 'start_date', 'expiry_date', 'is_active', 'paid','module']
#     read_only_fields = ['company', 'module', 'settled_amount', 'invoice_reference', 'paid']

# def validate(self, data):
#     if 'amount' in data and data['amount'] <= 0:
#         raise serializers.ValidationError("Amount must be greater than zero.")
#     return data

# def validate_module(self, value):
#     if value is not None and not isinstance(value, SubscriptionModule):
#         try:
#             return SubscriptionModule.objects.get(pk=value)
#         except SubscriptionModule.DoesNotExist:
#             raise serializers.ValidationError("Invalid module ID")
#     return value


class InvoiceListSerializer(serializers.ModelSerializer):
    company_name = serializers.CharField(source="company.name", read_only=True)

    class Meta:
        model = subscription.Invoice
        fields = [
            "id",
            "invoice_reference",
            "company_name",
            "amount",
            "amount_due",
            "settled_amount",
            "start_date",
            "expiry_date",
            "paid",
            "is_active",
        ]


class UserCompanyDetailsSerializer(serializers.Serializer):
    first_name = serializers.CharField(max_length=225, allow_blank=True, required=False)
    last_name = serializers.CharField(max_length=225, allow_blank=True, required=False)
    email = serializers.EmailField()
    phone_number = serializers.CharField(
        max_length=13, allow_blank=True, required=False
    )
    username = serializers.CharField(max_length=225, allow_blank=True, required=False)
    state = serializers.CharField(max_length=225, allow_blank=True, required=False)
    lga = serializers.CharField(max_length=225, allow_blank=True, required=False)
    street = serializers.CharField(max_length=225, allow_blank=True, required=False)
    nearest_landmark = serializers.CharField(
        max_length=225, allow_blank=True, required=False
    )
    company_name = serializers.CharField(
        max_length=225, allow_blank=True, required=False
    )
    industry = serializers.CharField(max_length=225, allow_blank=True, required=False)
    size = serializers.IntegerField()
    channel = serializers.CharField(max_length=225, allow_blank=True, required=False)
    cac_num = serializers.CharField(max_length=225, allow_blank=True, required=False)
    company_wallet_type = serializers.CharField(
        max_length=225, allow_blank=True, required=False
    )
    implementation_steps = serializers.ChoiceField(
        choices=UserCompanyDetails.STEP_CHOICES, default="user_creation"
    )
    # user_creation_data = serializers.CharField()
    # verify_account_data = serializers.CharField()
    # create_transaction_pin_data = serializers.CharField()
    # create_paybox_user_data = serializers.CharField()
    # create_company_data = serializers.CharField()


class ModuleSubscriptionSerializer(serializers.ModelSerializer):
    module_name = serializers.CharField(source="module.name", read_only=True)

    class Meta:
        model = ModuleSubscription
        fields = [
            "id",
            "module_name",
            "status",
            "is_active",
            "start_date",
            "end_date",
            "pending_balance",
            "invoice_ref",
            "module_overall_sub_days",
        ]


class CompanySubscriptionSerializer(serializers.ModelSerializer):
    company_name = serializers.CharField(source="company.company_name", read_only=True)
    modules = ModuleSubscriptionSerializer(source="modulesubscription_set", many=True)

    class Meta:
        model = CompanySubscription
        fields = [
            "id",
            "company_name",
            "access_type",
            "status",
            "overall_sub_days",
            "invoice_ref",
            "pos_included",
            "created_at",
            "updated_at",
            "modules",
        ]
