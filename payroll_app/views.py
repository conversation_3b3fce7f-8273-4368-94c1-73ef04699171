import calendar
import os
import re
from datetime import datetime, timedelta

# Create your views here.
from django.conf import settings
from django.contrib.auth import get_user_model

# Create your views here.
from django.db import transaction
from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Count,
    Max,
    Min,
    OuterRef,
    Q,
    Subquery,
    Sum,
    Value,
    When,
    Avg,
    F, 
    ExpressionWrapper, 
    fields
)
from django.db.models.functions import Lower, TruncMonth, Now, Extract
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from account.enums import AcctProvider, TransactionType
from account.tasks import transfer_commission
from django_filters.rest_framework import DjangoFilterBackend
from payroll_app.apis.enable_payroll_settings import enable_payroll_settings_calculator, enable_payroll_settings_calculator_check, enable_payroll_settings_check
from payroll_app.apis.pencco import PenccoAPI
from payroll_app.apis.pfa_schedule import pension_data
from rest_framework import filters, generics, status
from rest_framework.exceptions import ValidationError
from rest_framework.filters import SearchFilter
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.models import (
    AccountSystem,
    DebitCreditRecordOnAccount,
    Transaction,
    Wallet,
)
from core.auth.custom_auth import CustomUserAuthentication, IsStaff
from core.models import ConstantTable
from core.tasks import send_email, update_kyc_details, upload_file_aws_s3_bucket
from helpers.custom_permissions import CanInitiateTransfer
from payroll_app.apis.func import (
    PayrollHistoryPaginator,
    PayrollPaginator,
    WalletHistoryPaginator,
    check_employee_onboarding_percentage,
    convert_string_to_date,
    filter_by_date_two,
    generate_one_click,
    round_amount,
)
from payroll_app.apis.payroll_report import company_report
from payroll_app.apis.employee_verification import address_match_percentage, generate_guarantor_invite_id, is_utility_bill_expired, send_verification_sms
from payroll_app.filters import PayrollTableDateFilter, TransactionDateFilter

from payroll_app.models import Beneficiary, BenefitComponentSettings, CompanyAnnouncement, CompanyDepartmentSettings, CompanyDetailsData, CompanyEmployeeAccountDetails, CompanyEmployeeCertifications, CompanyEmployeeEducationDetails, CompanyEmployeeExperienceDetails, CompanyEmployeeList, CompanyEmployeeOnboardingForm, CompanyPayGradeSettings, CompanyPayGroupSettings, CompanyPayrollSettings, CompanyTaxBand, EmployeeCharge, InstantWagePayroll, ManageDepartmentRole, ManagePermissionsRole, OneClickTransaction, CustomComponentSettings, OtherDeductionSettings, PayrollTable, PensionFundAdminSettings, PreviousCompanyEmployeeVerifications, SalaryComponentSettings, SavingPayrollEditData, SendPensionData, VerificationData
from payroll_app.permissions import (AdminPermission, CanAddEmployeePermission, CanCreateDepartment, CanCreateDepartmentRoles, CanCreatePermissionRoles, CanDeleteDepartment, CanDeleteDepartmentRoles, CanDeletePermissionRoles, CanDeployDepartment, CanDeployDepartmentRole, CanDeployEmployeeRole, CanEditDepartment, CanEditDepartmentRoles, CanEditEmployeePermission, CanEditPayrollSettings, CanEditPermissionRoles, CompanyPermission, DeletePayrollPermission, EmployeeIsActive, EmployeePermission, PayrollApprovalPermission, PayrollDisbursePermission,
                                     CanRunPayrollPermission, UserIsActive, UserHasPin, CheckPin, VerifyCompanyEmployeeSendMoney)
from payroll_app.serializers import (AcceptInstantWageSerializer, AddBonusExistingPayrollSerializer, AddEmployeeExistingPayrollSerializer, AddEmployeePensionFundAdminSerializer, AddEmployeeProfilePictureSerializer, AddEmployeeStaffTypeSerializer, AddEmployeeWorkTypeSerializer, AddPayrollSerializer, AddPayrollSettingsLogoSerializer, AddSelectedPayrollSerializer, AdminEmployeeDepartmentRoleSerializer, AdminPromoteEmployeeSerializer, AdminRedeployEmployeeDepartmentSerializer, AdminResetEmployeePayrollPINSerializer, AllBeneficiarySerializer, AllBenefitComponentSerializer, AllDepartmentEmployeeListSerializer, AllDepartmentSerializer, AllCustomComponentSerializer, AllOtherDeductionSerializer, AllPFAEmployeeListSerializer, AllPayGradeSerializer, AllPayGroupSerializer, AllPensionFundAdminSerializer, AllSalaryComponentSerializer, ApplyDeductionBonusSerializer, BeneficiarySerializer, BenefitComponentSerializer, ChangePayrollPINSerializer, CompanyAnnouncementSerializer, CompanyEmployeeListSerializer, CompanyPayOutSerializer, CompanyPayrollCalculatorSerializer, CompanyPayrollSettingSerializer, CompanyPensionSettingSerializer, CompanyPensionTransactionHistorySerializer, CompanyTransactionHistorySerializer, CreateBeneficiarySerializer, CreateDepartmentRoleSerializer, CreateEmployeeAccountSerializer, CreateEmployeeInviteSerializer, CreateEmployeeSerializer,
                                     CreatePayrollPINSerializer, CreatePermissionRoleSerializer, DepartmentModuleSerializer, DepartmentSerializer, DisbursePayrollRecordSerializer, DisbursePayrollSerializer, EditDepartmentRoleSerializer, EditEmployeeAccountSerializer, EditEmployeeDataFromSavingsSerializer, EditEmployeeFamilyContactSerializer,
                                     EditEmployeePermissionSerializer, EditEmployeeProfileSerializer, EditInstantWageRequestSerializer, EditPermissionRoleSerializer, EditTaxBandSerializer, EmployeeAccountSerializer, EmployeeApprovalInfoSerializer, EmployeeCertificationSerializer, EmployeeDepartmentSerializer, EmployeeEducationSerializer, EmployeeExperienceSerializer, EmployeeFamilyContactSerializer, EmployeeInvitedOnboardingSerializer, EmployeeOnboardingSerializer,
                                     EmployeeProfileSerializer, EmployeeVerificationSerializer, FetchDepartmentRoleSerializer, FetchPermissionsSerializer, InstantWageRequestSerializer, InstantWageWithdrawSerializer, ListCompanyEmployeeDataSerializer, ListCompanyEmployeePermissionSerializer,
                                     ListCompanyEmployeeSerializer, ListCompanySerializer, ListDepartmentSerializer, ListEmployeePFASerializer, ListPreviousEmployerVerificationSerializer, ManualPensionSerializer, MultipleDepartmentSerializer, MultipleEmployeeDepartmentRoleSerializer, MultipleEmployeeDepartmentSerializer, MultipleEmployeePensionFundAdminSerializer, MultipleEmployeeRoleSerializer, MultipleEmployeeUserRoleSerializer, MultiplePayGroupSerializer, MultiplePensionFundAdminSerializer, OnboardPhoneUserSerializer, OnboardUserSerializer, OneClickHistorySerializer, CustomComponentSerializer, OtherDeductionSerializer, PayGradeSerializer, PayGroupSerializer, PayOutSerializer,
                                     PayrollApprovalSerializer, PayrollHistorySerializer,
                                     PayrollSerializer, PayrollTransactionHistorySerializer,
                                     PayrollTransactionSerializer, PayrollTypeSerializer, PayrollUserSerializer, PreviousEmployerEmailSerializer, PreviousEmployerVerificationSerializer, ResendPensionSerializer,
                                     RunApprovalPayrollSerializer, SalaryComponentSerializer, SearchEmployeeSerializer, SearchPayrollApprovalSerializer, SearchPayrollDisburseSerializer, TaxBandSerializer,
                                     TransactionHistorySerializer, OmitSerializer, TurnOnOffInstantWageForCompanyEmployeeSerializer, UniquePayrollSerializer, UserWalletHistorySerializer, VerifyGuarantorUtilityBillSerializer, VerifyUtilityBillSerializer, ViewEditEmployeeSerializer)

from payroll_app.services import CompanyChart, EmployeeOnboarding, valid_uuid_check
from payroll_app.tasks import (
    manual_send_automated_pfa,
    manual_send_automated_pfa_no_payment,
    payout_task,
    send_automated_pfa,
    send_manual_pencco_pfa_schedule,
    send_money_one_click_account_task,
    send_money_pay_account_task,
    send_money_pay_buddy_task,
    send_pencco_pfa_schedule,
)
from requisition.models import Company
from accounting.utils import categorize_transaction

User = get_user_model()
logger = settings.LOGGER

class CustomPagination(PageNumberPagination):
    page_size = 100
    page_size_query_param = "page_size"
    max_page_size = 200
    page_query_param = "page"


class CreateEmployeeAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanAddEmployeePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        """Create a new payroll pin"""
        company_uuid = request.query_params.get("company_uuid")
        serializer = CreateEmployeeSerializer(
            data=request.data, context={"company_id": company_uuid}
        )
        serializer.is_valid(raise_exception=True)
        all_employee = serializer.validated_data.get("data")

        company_ins = Company.objects.filter(id=company_uuid)
        employee_data = CompanyEmployeeList.create_employee(
            company_ins, all_employee, company_uuid, request.user
        )

        return Response(employee_data, status=status.HTTP_200_OK)


class CompanyEmployeesAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListCompanyEmployeeSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "employee_status",
        "is_work_anniversary_enabled",
        "is_birthday_enabled",
        "employee_department",
        "employee_pay_grade",
    )
    search_fields = (
        "employee_first_name",
        "employee_last_name",
        "employee_email",
        "employee_phone_number",
        "employee_staff_id"
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID"})

        company = Company.objects.filter(id=company_id).first()
        CompanyPayrollSettings.create_company_payroll_settings(company=company)

        employees = CompanyEmployeeList.objects.filter(
            company__id=company_id, is_deleted=False
        ).order_by(Lower("employee_last_name"))
        return employees

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "EMPLOYEES_DATA",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class CompanyEmployeesDataAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListCompanyEmployeeDataSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "employee_department",
    )
    search_fields = (
        "employee_first_name",
        "employee_last_name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID"})

        company = Company.objects.filter(id=company_id).first()

        employees = CompanyEmployeeList.objects.filter(
            company__id=company_id, is_deleted=False
        ).order_by(Lower("employee_last_name"))
        return employees

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "EMPLOYEES_DATA",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class CompanyEmployeesOffBoardAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListCompanyEmployeeSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "can_disburse",
        "can_approve",
        "can_add_member",
        "can_edit_member",
        "can_delete_member",
        "can_delete_payroll",
        "can_run_payroll",
        "employee_status",
        "is_work_anniversary_enabled",
        "is_birthday_enabled",
    )
    search_fields = (
        "employee_first_name",
        "employee_last_name",
        "employee_email",
        "employee_phone_number",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID"})

        company = Company.objects.filter(id=company_id).first()
        CompanyPayrollSettings.create_company_payroll_settings(company=company)

        employees = CompanyEmployeeList.objects.filter(
            company__id=company_id, is_deleted=True
        ).order_by(Lower("employee_last_name"))
        return employees

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "EMPLOYEES_DATA",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class CompanyEmployeeListAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        EmployeePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    @staticmethod
    def get(request):
        page = request.GET.get("page", 1)
        company_uuid = request.query_params["company_uuid"]

        employees = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, is_deleted=False
        ).order_by(Lower("employee_last_name"))
        employees_list = PayrollPaginator.paginate(request, employees, page)
        company = Company.objects.filter(id=company_uuid).first()
        CompanyPayrollSettings.create_company_payroll_settings(company=company)
        serializer = ListCompanyEmployeeSerializer(employees, many=True)

        # return Response(serializer.data)
        # serializer = PayrollHistorySerializer(payroll_history, many=True)
        try:

            data = {
                "data_type": "EMPLOYEES_DATA",
                "company_name": company.company_name,
                "data": serializer.data,
                "total_page": employees_list.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": employees_list.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "EMPLOYEES_DATA",
                "company_name": company.company_name,
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class CreatePayrollPinAPIView(APIView):
    serializer_class = CreatePayrollPINSerializer
    permission_classes = [IsAuthenticated, UserIsActive, CheckPin]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        """Create a new payroll pin"""
        #     serializer = SignupSerializer(data=request.data)
        #     serializer.is_valid(raise_exception=True)

        serializer = self.serializer_class(data=request.data)

        serializer.is_valid(raise_exception=True)
        pincode = serializer.validated_data.get("requisition_pin")
        pincode_retry = serializer.validated_data.get("requisition_pin_retry")

        if len(pincode) != 4 or len(pincode_retry) != 4:

            response = {
                "status": "error",
                "message": "Requisition Pin must be four digits",
            }
            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

        else:
            if pincode == pincode_retry:
                pin_created = User.create_payroll_pin(
                    user=request.user, pincode=pincode
                )
                if pin_created:
                    response = {
                        "status": "success",
                        "message": "Requisition Pin Created",
                    }
                    return Response(response, status=status.HTTP_201_CREATED)
                else:
                    response = {
                        "status": "error",
                        "message": "Requisition Pin Could Not Be Created At This Time. Please Try Again",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
            else:
                response = {
                    "status": "error",
                    "message": "Requisition Pins Entered Do Not Match",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)


class PayrollPinUpdateAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, UserHasPin]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        user = request.user
        if user.requisition_transaction_pin is None:
            return Response(
                {
                    "message": "User does not have a Requisition PIN please create a new pin!"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = ChangePayrollPINSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        old_pin = serializer.validated_data.get("old_pin")
        pin = serializer.validated_data.get("pin")
        repeat_pin = serializer.validated_data.get("repeat_pin")
        check_pin = User.check_sender_payroll_pin(user=user, pincode=old_pin)
        if not check_pin:
            return Response(
                {"message": "User old pin is incorrect!"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if len(pin) != 4 or len(repeat_pin) != 4:

            response = {
                "status": "error",
                "message": "Requisition Pin must be four digits",
            }
            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

        if pin != repeat_pin:
            return Response(
                {"message": "Requisition Pin's must be equal!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            with transaction.atomic():
                # remove pin
                user.requisition_transaction_pin = None
                user.save()
                update_user_pin = User.create_payroll_pin(user, pin)

                return Response(
                    {"message": "Requisition pin been has been changed successfully"},
                    status=status.HTTP_200_OK,
                )
        except:
            return Response(
                {"message": "Something went wrong, please try again!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class RunPayrollAPIVIew(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        UserHasPin,
        CanRunPayrollPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        company_uuid = request.query_params.get("company_uuid")

        serializer = AddPayrollSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        payroll_pin = serializer.validated_data.get("payroll_pin")
        payroll_date = serializer.validated_data.get("payroll_date")
        # is_recurring = serializer.validated_data.get("is_recurring")
        narration = serializer.validated_data.get("narration")
        payroll_month = serializer.validated_data.get("payroll_month")
        payroll_year = serializer.validated_data.get("payroll_year")

        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=payroll_pin
        )
        if not check_pin:
            return Response(
                {"status": False, "message": "Payroll pin is incorrect!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            month_map = {
                "january": 1,
                "february": 2,
                "march": 3,
                "april": 4,
                "may": 5,
                "june": 6,
                "july": 7,
                "august": 8,
                "september": 9,
                "october": 10,
                "november": 11,
                "december": 12,
            }
            if payroll_month not in month_map:
                return Response(
                    {"message": "payroll month not in correct format"},
                    status.HTTP_400_BAD_REQUEST,
                )

            # company_ins = Company.objects.get(id=company_uuid)
            run_payroll = PayrollTable.create_approval_payroll(
                user=request.user,
                company_uuid=company_uuid,
                payroll_date=payroll_date,
                is_recurring=False,
                narration=narration,
                payroll_month=payroll_month,
                payroll_year=payroll_year,
                employees=[],
                multiple_payroll=False,
            )
            if not run_payroll["status"]:
                return Response(run_payroll, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(run_payroll, status=status.HTTP_200_OK)


class RunApprovalPayrollAPIVIew(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        UserHasPin,
        PayrollApprovalPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")

        serializer = RunApprovalPayrollSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        payroll_pin = serializer.validated_data.get("payroll_pin")

        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=payroll_pin
        )
        if not check_pin:
            return Response(
                {"status": False, "message": "Payroll pin is incorrect!"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        else:
            run_approval = PayrollTable.run_approval_payroll(
                user=request.user, company_uuid=company_uuid
            )
            if not run_approval["status"]:
                return Response(run_approval, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(run_approval, status=status.HTTP_200_OK)
            
class RunMultipleApprovalPayrollAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        UserHasPin,
        PayrollApprovalPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        bulk_id = request.query_params.get("bulk_id")

        serializer = RunApprovalPayrollSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        payroll_pin = serializer.validated_data.get("payroll_pin")

        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=payroll_pin
        )
        if not check_pin:
            return Response(
                {"status": False, "message": "Payroll pin is incorrect!"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        else:
            run_approval = PayrollTable.run_approval_payroll(
                user=request.user, company_uuid=company_uuid, bulk_id=bulk_id
            )
            if not run_approval["status"]:
                return Response(run_approval, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(run_approval, status=status.HTTP_200_OK)


class GetApprovePayrollAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = PayrollApprovalSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    filterset_fields = (
        "payroll_type",
        "status",
        "employee__employee_department"
    )
    search_fields = (
        "first_name",
        "last_name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")

        queryset = PayrollTable.objects.filter(
            company__id=company_id, status="APPROVAL", payroll_deleted=False
        ).order_by(Lower("last_name"))

        return queryset

    def list(self, request, *args, **kwargs):
        company_id = request.query_params.get("company_uuid")
        queryset = self.filter_queryset(self.get_queryset())

        get_data = queryset.first()
        if get_data:
            bulk_id = get_data.bulk_id
            company_data = CompanyDetailsData.objects.filter(
                company__id=company_id, bulk_id=bulk_id, is_deleted=False
            ).first()
            if company_data:
                data_initiated = company_data.payroll_date
                payroll_month = data_initiated.strftime("%b").upper()
            else:
                data_initiated = None
                payroll_month = None
        else:
            bulk_id = ""
            data_initiated = None
            payroll_month = None

        total_amount = (
            queryset.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
        )
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "total_amount": total_amount,
            "employee_count": queryset.aggregate(Count("id"))["id__count"] or 0,
            "status": "APPROVAL",
            "payroll_month": payroll_month,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "date_initiated": data_initiated,
            "bulk_id": bulk_id,
            # "data": serializer.data,
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class GetMultipleApprovePayrollAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = PayrollApprovalSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    filterset_fields = (
        "payroll_type",
        "status",
        "employee__employee_department"
    )
    search_fields = (
        "first_name",
        "last_name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        bulk_id = self.request.query_params.get("bulk_id")

        queryset = PayrollTable.objects.filter(
            company__id=company_id, bulk_id=bulk_id, status="APPROVAL", payroll_deleted=False
        ).order_by(Lower("last_name"))

        return queryset

    def list(self, request, *args, **kwargs):
        company_id = request.query_params.get("company_uuid")
        queryset = self.filter_queryset(self.get_queryset())

        get_data = queryset.first()
        if get_data:
            bulk_id = get_data.bulk_id
            company_data = CompanyDetailsData.objects.filter(
                company__id=company_id, bulk_id=bulk_id, is_deleted=False
            ).first()
            if company_data:
                data_initiated = company_data.payroll_date
                payroll_month = data_initiated.strftime("%b").upper()
            else:
                data_initiated = None
                payroll_month = None
        else:
            bulk_id = ""
            data_initiated = None
            payroll_month = None

        total_amount = (
            queryset.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
        )
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "total_amount": total_amount,
            "employee_count": queryset.aggregate(Count("id"))["id__count"] or 0,
            "status": "APPROVAL",
            "payroll_month": payroll_month,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "date_initiated": data_initiated,
            "bulk_id": bulk_id,
            # "data": serializer.data,
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetEditDelApprovePayrollAPIVIew(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        PayrollApprovalPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):

        company_uuid = request.query_params.get("company_uuid")
        queryset = PayrollTable.objects.filter(
            company__id=company_uuid, payroll_deleted=False, status="APPROVAL"
        ).order_by(Lower("last_name"))
        total_amount = (
            queryset.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
        )

        company_data = CompanyDetailsData.objects.filter(
            company__id=company_uuid, is_deleted=False
        ).last()
        if company_data:
            data_initiated = company_data.payroll_date
            payroll_month = data_initiated.strftime("%b").upper()
        else:
            data_initiated = None
            payroll_month = None

        serializer = PayrollApprovalSerializer(queryset, many=True)
        return Response(
            {
                "employee_count": queryset.aggregate(Count("id"))["id__count"] or 0,
                "total_amount": total_amount,
                "date_initiated": data_initiated,
                "payroll_month": payroll_month,
                "status": "APPROVAL",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    def put(self, request):
        bulk_id = request.query_params.get("bulk_id")
        payroll_id = request.query_params.get("payroll_id")
        company_uuid = request.query_params.get("company_uuid")

        instance = PayrollTable.objects.filter(
            id=payroll_id,
            company__id=company_uuid,
            bulk_id=bulk_id,
            status="APPROVAL",
            payroll_deleted=False,
        ).first()
        if instance:
            data = request.data
            data["payroll_admin"] = request.user.id
            serializer = PayrollApprovalSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            phone_number = User.format_number_from_back_add_234(
                serializer.validated_data.get("phone_number")
            )
            serializer.validated_data["phone_number"] = phone_number
            serializer.save()
            return Response(
                {"message": "payroll updated successfully"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "payroll data not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def delete(self, request):
        bulk_id = request.query_params.get("bulk_id")
        payroll_id = request.query_params.get("payroll_id")
        company_uuid = request.query_params.get("company_uuid")

        instance = PayrollTable.objects.filter(
            id=payroll_id,
            company__id=company_uuid,
            bulk_id=bulk_id,
            status="APPROVAL",
            payroll_deleted=False,
        ).first()
        if instance:
            instance.payroll_deleted = True
            instance.save()
            return Response(
                {"message": "user deleted from payroll successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "payroll data not found"},
                status=status.HTTP_404_NOT_FOUND,
            )


class GetDisbursePayrollAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = PayrollApprovalSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "payroll_type",
        "status",
        "employee__employee_department"
    )
    search_fields = (
        "first_name",
        "last_name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID"})

        queryset = PayrollTable.objects.filter(
            company__id=company_id, status="DISBURSE", payroll_deleted=False
        ).order_by(Lower("last_name"))

        return queryset

    def list(self, request, *args, **kwargs):
        company_id = request.query_params.get("company_uuid")
        queryset = self.filter_queryset(self.get_queryset())

        get_data = queryset.first()
        if get_data:
            bulk_id = get_data.bulk_id
            company_data = CompanyDetailsData.objects.filter(
                company__id=company_id, bulk_id=bulk_id, is_deleted=False
            ).first()
            if company_data:
                data_initiated = company_data.payroll_date
                payroll_month = data_initiated.strftime("%b").upper()
            else:
                data_initiated = None
                payroll_month = None
        else:
            bulk_id = ""
            data_initiated = None
            payroll_month = None

        total_amount = (
            queryset.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
        )
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "total_amount": total_amount,
            "employee_count": queryset.aggregate(Count("id"))["id__count"] or 0,
            "status": "DISBURSE",
            "payroll_month": payroll_month,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "date_initiated": data_initiated,
            "bulk_id": bulk_id,
            # "data": serializer.data,
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class GetMultipleDisbursePayrollAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = PayrollApprovalSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "payroll_type",
        "status",
        "employee__employee_department"
    )
    search_fields = (
        "first_name",
        "last_name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        bulk_id = self.request.query_params.get("bulk_id")

        queryset = PayrollTable.objects.filter(
            company__id=company_id, bulk_id=bulk_id, status="DISBURSE", payroll_deleted=False
        ).order_by(Lower("last_name"))

        return queryset

    def list(self, request, *args, **kwargs):
        company_id = request.query_params.get("company_uuid")
        queryset = self.filter_queryset(self.get_queryset())

        get_data = queryset.first()
        if get_data:
            bulk_id = get_data.bulk_id
            company_data = CompanyDetailsData.objects.filter(
                company__id=company_id, bulk_id=bulk_id, is_deleted=False
            ).first()
            if company_data:
                data_initiated = company_data.payroll_date
                payroll_month = data_initiated.strftime("%b").upper()
            else:
                data_initiated = None
                payroll_month = None
        else:
            bulk_id = ""
            data_initiated = None
            payroll_month = None

        total_amount = (
            queryset.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
        )
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "total_amount": total_amount,
            "employee_count": queryset.aggregate(Count("id"))["id__count"] or 0,
            "status": "DISBURSE",
            "payroll_month": payroll_month,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "date_initiated": data_initiated,
            "bulk_id": bulk_id,
            # "data": serializer.data,
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetEditDelDisbursePayrollAPIVIew(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        PayrollApprovalPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")

        queryset = PayrollTable.objects.filter(
            company__id=company_uuid, status="DISBURSE", payroll_deleted=False
        ).order_by(Lower("last_name"))
        # print(queryset)
        serializer = PayrollApprovalSerializer(queryset, many=True)
        total_amount = queryset.aggregate(Sum("payable_amount"))["payable_amount__sum"]
        company_data = CompanyDetailsData.objects.filter(
            company__id=company_uuid, is_deleted=False
        ).last()
        if company_data:
            data_initiated = company_data.payroll_date
            payroll_month = data_initiated.strftime("%b").upper()
        else:
            data_initiated = None
            payroll_month = None
        serializer = PayrollApprovalSerializer(queryset, many=True)
        return Response(
            {
                "employee_count": queryset.aggregate(Count("id"))["id__count"],
                "total_amount": total_amount,
                "date_initiated": data_initiated,
                "payroll_month": payroll_month,
                "status": "DISBURSE",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    def put(self, request):
        bulk_id = request.query_params.get("bulk_id")
        payroll_id = request.query_params.get("payroll_id")
        company_uuid = request.query_params.get("company_uuid")
        instance = PayrollTable.objects.filter(
            id=payroll_id,
            company__id=company_uuid,
            bulk_id=bulk_id,
            status="DISBURSE",
            payroll_deleted=False,
        ).first()
        if instance:
            data = request.data
            data["payroll_admin"] = request.user.id
            serializer = PayrollApprovalSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            phone_number = User.format_number_from_back_add_234(
                serializer.validated_data.get("phone_number")
            )
            serializer.validated_data["phone_number"] = phone_number
            serializer.save()
            return Response(
                {"message": "payroll updated successfully"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "payroll data not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def delete(self, request):
        bulk_id = request.query_params.get("bulk_id")
        payroll_id = request.query_params.get("payroll_id")
        company_uuid = request.query_params.get("company_uuid")

        instance = PayrollTable.objects.filter(
            id=payroll_id,
            company__id=company_uuid,
            bulk_id=bulk_id,
            status="DISBURSE",
            payroll_deleted=False,
        ).first()
        if instance:
            instance.payroll_deleted = True
            instance.save()
            return Response(
                {"message": "user deleted from payroll successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "payroll data not found"},
                status=status.HTTP_404_NOT_FOUND,
            )


class AllPayrollDataAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        PayrollDisbursePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        # serializer_class = PayrollSerializer
        page = request.GET.get("page", 1)
        serializer = PayrollTypeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        payroll_type = serializer.validated_data.get("payroll_type")
        payroll_id_qs = (
            PayrollTable.objects.order_by("id")
            .values("bulk_id")
            .distinct()
            .exclude(bulk_id=None)
            .filter(payroll_deleted=False, status=payroll_type)
        )
        payroll_data = PayrollHistoryPaginator.paginate(request, payroll_id_qs, page)
        # expected_amount = queryset.aggregate(Sum("payable_amount"))["payable_amount__sum"]
        # successful_payout = queryset.filter(status="DISBURSED").aggregate(Sum("payable_amount"))["payable_amount__sum"]
        # failed_payout = queryset.filter(status="DISBURSE").aggregate(Sum("payable_amount"))["payable_amount__sum"]
        # serializer = PayrollApprovalSerializer(queryset, many=True)
        # return Response({
        #     "employee_count": queryset.aggregate(Count('id'))['id__count'],
        #     "expected_payout": expected_amount,
        #     "successful_payout": successful_payout,
        #     "failed_payout": failed_payout,
        #     "data":serializer.data
        # }, status=status.HTTP_200_OK)
        serializer = PayrollSerializer(payroll_data, many=True)
        try:
            data = {
                "data_type": "PAYROLL DATA",
                "data": serializer.data,
                "total_page": payroll_data.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": payroll_data.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "PAYROLL DATA",
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class UserPaySlipDataAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        # serializer_class = PayrollSerializer
        company_uuid = request.query_params.get("company_uuid")
        page = request.GET.get("page", 1)
        payroll_qs = (
            PayrollTable.objects.filter(
                payroll_user=request.user,
                payroll_deleted=False,
                company__id=company_uuid,
            )
            .filter(Q(status="DISBURSE") | Q(status="DISBURSED"))
            .order_by("-id")
        )
        payroll_data = PayrollPaginator.paginate(request, payroll_qs, page)
        serializer = PayrollUserSerializer(payroll_data, many=True)
        try:
            data = {
                "data_type": "PAYSLIP DATA",
                "data": serializer.data,
                "total_page": payroll_data.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": payroll_data.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "PAYSLIP DATA",
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class EmployeePermissionsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanEditEmployeePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        
        instance = CompanyEmployeeList.objects.filter(company__id=company_uuid, id=employee_id, is_deleted=False).first()
        if instance:
            serializer = EditEmployeePermissionSerializer(
                instance,
                data=request.data,
                partial=True,
                context={"employee_instance": instance},
            )
            serializer.is_valid(raise_exception=True)
            phone_number = User.format_number_from_back_add_234(
                serializer.validated_data.get("employee_phone_number")
            )
            serializer.validated_data["employee_phone_number"] = phone_number
            serializer.save()
            return Response(
                {"message": "employee data updated successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "user not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def patch(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")

        instance = CompanyEmployeeList.objects.filter(company__id=company_uuid, id=employee_id, is_deleted=False).first()
        if instance:
            instance.is_active = False
            instance.is_suspended = True
            instance.employee_status = "SUSPENDED"
            instance.save()
            return Response(
                {"message": "employee suspended successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "user not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")


        instance = CompanyEmployeeList.objects.filter(company__id=company_uuid, id=employee_id, is_deleted=False).first()
        if instance:
            instance.is_active = False
            instance.is_deleted = True
            instance.is_suspended = False
            instance.employee_status = "DELETED"
            instance.save()
            return Response(
                {"message": "employee deleted successfully"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "user not found"},
                status=status.HTTP_404_NOT_FOUND,
            )


class DisbursePayrollAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        CanInitiateTransfer,
        UserIsActive,
        UserHasPin,
        PayrollDisbursePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        bulk_id = request.query_params.get("bulk_id")
        company_uuid = request.query_params.get("company_uuid")

        request_user = request.user

        serializer = DisbursePayrollSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        payroll_pin = serializer.validated_data.get("payroll_pin")
        payroll_choice = serializer.validated_data.get("payroll_choice")

        # Check Transaction Pin
        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=payroll_pin
        )
        if not check_pin:
            return Response(
                {
                    "error": "error",
                    "message": "Incorrect Pin",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        CONST = ConstantTable.get_constant_instance()
        if payroll_choice == "ACCOUNT":
            if CONST.send_money is False:
                response = {
                    "error": "02",
                    "message": "Service is currently not available",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

        elif payroll_choice == "BUDDY":
            if CONST.send_money_via_buddy is False:
                response = {
                    "error": "02",
                    "message": "Service is currently not available",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
        else:
            response = {
                "error": "02",
                "message": "Service is currently not available",
            }
            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
        
        # company charge
        company_settings = CompanyPayrollSettings.objects.filter(company__id=company_uuid).first()
        if company_settings:
            get_payroll_charge = company_settings.charge
            employer_code = company_settings.employer_pension_code 
        else:
            get_payroll_charge = 2000
            employer_code = None

        # pfa_switch = False
        pfa_switch = CompanyPayrollSettings.pfa_switch(company_uuid)
        if pfa_switch:
            if not CONST.send_automated_pfa:
                response = {
                    "error": "03",
                    "message": "Automated PFA not available, please switch off to continue payroll"
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
            
            if employer_code is None:
                response = {
                    "error": "05",
                    "message": "Please add your Pension Employer Code in Settings to continue"
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
            
        main_data = []
        all_payable_id = []
        bulk_id_qs = PayrollTable.objects.filter(
            company__id=company_uuid,
            bulk_id=bulk_id,
            status="DISBURSE",
            payroll_deleted=False,
            payable_amount__gt=0
        )
        if bulk_id_qs:
            for single_data in bulk_id_qs:
                instant_wage_amount = single_data.instant_wage_deduction
                salary_amount = single_data.payable_amount
                pay_amount = round_amount(salary_amount - instant_wage_amount)
                # print(pay_amount, "PAY AMOUNT")
                if pay_amount > 0:
                    main_data.append(
                        {
                            "receiver_ins_id": single_data.payroll_user.id,
                            "receiver_ins": single_data.payroll_user,
                            "payroll_id": single_data.id,
                            "amount": pay_amount,
                            "payable_amount": salary_amount,
                            "instant_wage_amount": single_data.instant_wage_deduction,
                            "bank_name": single_data.bank_name,
                            "account_number": single_data.account_number,
                            "account_name": single_data.account_name,
                            "bank_code": single_data.bank_code,
                        }
                    )
                    all_payable_id.append(single_data.id)
                else:
                    # single_data.status = "DISBURSED"
                    # single_data.payroll_status = "00"
                    # single_data.payroll_disburse = True
                    # single_data.save()
                    pass

        else:
            return Response(
                {
                    "error": "error",
                    "message": "No payroll to disburse",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # print(bulk_id, main_data, "BULK ID\n\n\n\n\n")

        # Get wallet type
        company_data = CompanyDetailsData.objects.filter(
            company__id=company_uuid, bulk_id=bulk_id, is_deleted=False
        ).first()
        if not company_data:
            return Response(
                {
                    "error": "error",
                    "message": "No available payroll to disburse",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
       
        narration = company_data.narration
        this_payroll_date = f"{company_data.payroll_year}-{company_data.payroll_month}-01"
        last_parts = this_payroll_date.split("-")
        month_map = {
            "january": 1,
            "february": 2,
            "march": 3,
            "april": 4,
            "may": 5,
            "june": 6,
            "july": 7,
            "august": 8,
            "september": 9,
            "october": 10,
            "november": 11,
            "december": 12,
        }
        # Convert the month name to numerical value
        last_date_month = month_map[last_parts[1].lower()]
        # Create a datetime object
        last_date_object = datetime(
            int(last_parts[0]), last_date_month, int(last_parts[2])
        )

        company_data.disbursed_by = request_user
        company_data.save()

        # get account and wallet instance of sender
        company_ins = Company.objects.get(id=company_uuid)

        if company_ins.company_wallet_type == "CORPORATE":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="PAYROLL"
                )
            except AccountSystem.DoesNotExist:
                account_ins = None
        elif company_ins.company_wallet_type == "MAIN":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="NON_CORP_PAYROLL"
                )
            except AccountSystem.DoesNotExist:
                account_ins = None
                wallet_balance = 0
        else:
            return Response(
                {
                    "error": "171",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not account_ins:
            return Response(
                {
                    "error": "172",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # try:
        wallet_ins = Wallet.get_wallet_instance(account_ins.user, account_ins)
        if wallet_ins is None:
            return Response(
                {
                    "error": "173",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # get_payroll_charge = EmployeeCharge.get_payroll_charges(len(main_data))
        wallet_balance = wallet_ins.balance
        get_total_amount = 0
        get_total_payable_amount = 0
        total_commission = 0
        payroll_commission = 0
        payroll_charge = (
            (get_payroll_charge + 150)
            if payroll_choice == "ACCOUNT"
            else get_payroll_charge
        )
        for data in main_data:
            amount = data["amount"]
            get_total_charged_amount = round_amount(amount + payroll_charge)
            get_total_amount += get_total_charged_amount
            total_commission += payroll_charge
            payroll_commission += get_payroll_charge

            payable_amount = data["payable_amount"]
            get_total_payable_charged_amount = round_amount(payable_amount + payroll_charge)
            get_total_payable_amount += get_total_payable_charged_amount

            if wallet_balance < get_total_payable_amount:
                response = {
                    "error": "161",
                    "message": "You do not have sufficient balance to make this transaction",
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            if get_total_payable_amount <= 0.0:
                response = {
                    "error": "162",
                    "message": "amount must be greater than 0",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            else:
                get_total_payable_amount = get_total_payable_amount

        
        if pfa_switch:
            if CONST.send_pension_from != "NONE":
                pfa_count, pfa_amount = PensionFundAdminSettings.company_pfa_data(company_uuid, bulk_id)
            else:
                pfa_count = 0
                pfa_amount = 0
        else:
            pfa_count = 0
            pfa_amount = 0

        total_pfa_charges = pfa_count * 150
        total_pfa_amount = total_pfa_charges + pfa_amount

        overall_total_amount = get_total_payable_amount + total_pfa_amount + 150

        if wallet_balance < overall_total_amount:
                response = {
                    "error": "169",
                    "message": "You do not have sufficient balance to make this transaction",
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)


        payroll_type = (
            "PAYROLL"
            if company_ins.company_wallet_type == "CORPORATE"
            else "NON_CORP_PAYROLL"
        )
    

        # create commision transaction
        commission_beneficiary_nuban = settings.COMMISSION_ACCOUNT_NUMBER_VFD
        float_beneficiary_nuban = settings.FLOAT_ACCOUNT_NUMBER_VFD
        reference = Transaction.create_unique_transaction_ref(suffix="paybx360-comm-")

        user = company_ins.user
        commision_transaction_instance_created = Transaction.objects.create(
            user=user,
            company_name="LIBERTY ASSURED COMMISSION",
            user_full_name=user.full_name,
            commission_from=bulk_id,
            user_email=user.email,
            payout_type="COMMISSION",
            amount=payroll_commission,
            commission_ref=reference,
            debit_amount=(payroll_commission + 150),
            beneficiary_account_number=commission_beneficiary_nuban,
            bank_name=AcctProvider.VFD,
            narration="SEND TO COMMISSION WALLET",
            bank_code="999999",
            transfer_provider=AcctProvider.VFD,
            source_account_name="Liberty credi float",
            source_account_number=float_beneficiary_nuban,
            transaction_type=TransactionType.COMMISSION,
            company_id=company_uuid
        )

        Wallet.charge_wallet(
                wallet_instance=wallet_ins,
                amount=commision_transaction_instance_created.debit_amount,
                transaction_instance=commision_transaction_instance_created,
            )
        
        try:
            categorize_transaction(
                commision_transaction_instance_created,
                source="hr",
                transaction_type="payroll_expenses"
            )
        except Exception as e:
            logger.info(f"PAYROLL EMPLOYEE CHARGE -> {str(e)}")

        transfer_commission.delay(
            transaction_id=commision_transaction_instance_created.id
        )
        

        if payroll_choice == "ACCOUNT":
            for data in main_data:
                account_number = data["account_number"]
                account_name = data["account_name"]
                bank_name = data["bank_name"]
                bank_code = data["bank_code"]
                amount = data["amount"]
                payroll_id = data["payroll_id"]
                receiver_id = data["receiver_ins_id"]
                instant_amount = data["instant_wage_amount"]

                # Send Money
                send_money_pay_account_task.apply_async(
                    queue="sendbulkpayroll",
                    kwargs={
                        "user": account_ins.user.id,
                        "payroll_type": payroll_type,
                        "account_ins": account_ins.id,
                        "payroll_pin": payroll_pin,
                        "payroll_id": payroll_id,
                        "account_number": account_number,
                        "account_name": account_name,
                        "bank_name": bank_name,
                        "bank_code": bank_code,
                        "amount": amount,
                        "narration": narration,
                        "bulk_id": bulk_id,
                        "charge_fee": 100,
                        "instant_amount": instant_amount,
                        "receiver_id": receiver_id,
                    },
                )
            
            # automate pfa
            if pfa_switch:
                if CONST.send_pension_from == "PENCCO":
                    send_pencco_pfa_schedule.delay(
                        company_id=company_uuid, 
                        payroll_date=last_date_object, 
                        user_id=account_ins.user.id,
                        account_id=account_ins.id,
                        all_payable_id=all_payable_id
                    )
                    
                elif CONST.send_pension_from == "DEFAULT":
                    send_automated_pfa.delay(
                        company_id=company_uuid, 
                        payroll_date=last_date_object, 
                        company_details_id=company_data.id,
                        user_id=account_ins.user.id,
                        account_id=account_ins.id,
                        all_payable_id=all_payable_id
                    )

            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": overall_total_amount,
                    "processed_transaction_count": len(main_data),
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)

        if payroll_choice == "BUDDY":

            # check if employee has wallet
            for data in main_data:
                receiver = data["receiver_ins"]
                receiver_wallet_type = "PERSONAL_PAYROLL"
                receiver_wallet_instance = Wallet.objects.filter(
                    user=receiver, wallet_type=receiver_wallet_type
                ).first()
                if receiver_wallet_instance is None:
                    response = {
                        "error": "04",
                        "message": f"{receiver.email} does not have a personal payroll wallet",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            for data in main_data:
                amount = data["amount"]
                payroll_id = data["payroll_id"]
                receiver_id = data["receiver_ins_id"]
                instant_amount = data["instant_wage_amount"]
                # Send Money
                send_money_pay_buddy_task.apply_async(
                    queue="sendbulkpayroll",
                    kwargs={
                        "user": account_ins.user.id,
                        "payroll_type": payroll_type,
                        "account_ins": account_ins.id,
                        "payroll_pin": payroll_pin,
                        "payroll_id": payroll_id,
                        "amount": amount,
                        "narration": narration,
                        "bulk_id": bulk_id,
                        "charge_fee": 0,
                        "receiver_id": receiver_id,
                        "instant_amount": instant_amount,
                    },
                )

            # automate pfa
            if pfa_switch:
                if CONST.send_pension_from == "PENCCO":
                    send_pencco_pfa_schedule.delay(
                        company_id=company_uuid, 
                        payroll_date=last_date_object, 
                        user_id=account_ins.user.id,
                        account_id=account_ins.id,
                        all_payable_id=all_payable_id
                    )
                    
                elif CONST.send_pension_from == "DEFAULT":
                    send_automated_pfa.delay(
                        company_id=company_uuid, 
                        payroll_date=last_date_object, 
                        company_details_id=company_data.id,
                        user_id=account_ins.user.id,
                        account_id=account_ins.id,
                        all_payable_id=all_payable_id
                    )

            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": overall_total_amount,
                    "processed_transaction_count": len(main_data),
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)


# class TransactionHistory(generics.ListAPIView):
#     permission_classes = [IsAuthenticated, UserIsActive]
#     authentication_classes = [CustomUserAuthentication]

#     serializer_class = TransactionHistorySerializer
#     pagination_class = CustomPagination
#     search_fields = ['narration']
#     filter_backends = [DjangoFilterBackend, SearchFilter]
#     filterset_class = TransactionDateFilter

#     def get_queryset(self):
#         request_user = self.request.user
#         return Transaction.objects.filter(
#             Q(user=request_user, payout_type="PERSONAL_PAYROLL")
#         ).order_by("-date_created")


class TransactionHistory(generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    serializer_class = PayrollTransactionHistorySerializer
    pagination_class = CustomPagination
    search_fields = ["narration"]
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = PayrollTableDateFilter

    def get_queryset(self):
        request_user = self.request.user
        return PayrollTable.objects.filter(
            Q(payroll_user=request_user, status="DISBURSED")
        ).order_by("-date_created")


class CompanyTransactionHistory(generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    serializer_class = TransactionHistorySerializer
    pagination_class = CustomPagination
    search_fields = ["narration"]
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = TransactionDateFilter

    def get_queryset(self):
        request_user = self.request.user
        return Transaction.objects.filter(
            Q(user=request_user, payout_type="PAYROLL")
        ).order_by("-date_created")


class PayrollHistoryAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        page = request.GET.get("page", 1)
        company_uuid = request.query_params.get("company_uuid")
        payroll_history_qs = (
            PayrollTable.objects.filter(
                company__id=company_uuid, status="DISBURSED", payroll_deleted=False
            )
            .values_list("bulk_id", flat=True)
            .distinct()
            .order_by("-payroll_date")
        )
        payroll_history = PayrollPaginator.paginate(request, payroll_history_qs, page)
        serializer = PayrollHistorySerializer(payroll_history, many=True)
        try:
            data = {
                "data_type": "PAYROLL_HISTORY",
                "data": serializer.data,
                "total_page": payroll_history.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": payroll_history.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "PAYROLL_HISTORY",
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class CompanyChartAPIView(APIView):
    serializer_class = PayrollTransactionSerializer
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, timezone=timezone)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = CompanyChart.get_company_chart(
                request, start_date=start_date, end_date=end_date
            )
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
class CompanyEmploymentTrendChartAPIView(APIView):
    serializer_class = PayrollTransactionSerializer
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, timezone=timezone)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = CompanyChart.employment_trend_chart(
                request, start_date=start_date, end_date=end_date
            )
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CompanyList(generics.ListAPIView):
    serializer_class = PayrollTransactionSerializer
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    @staticmethod
    def get(request):
        all_company = Company.objects.filter(
            user=request.user, is_active=True, is_deleted=False
        )
        employee_company = CompanyEmployeeList.objects.filter(
            employee=request.user, is_deleted=False
        )
        if employee_company:
            for company in employee_company:
                all_company |= Company.objects.filter(
                    id=company.company.id, is_active=True, is_deleted=False
                )

        # Use the custom serializer with context
        serializer = ListCompanySerializer(
            all_company,
            many=True,
            context={'request': request}
        )

        return Response(serializer.data, status=status.HTTP_200_OK)


class CompanyListDashboard(generics.ListAPIView):
    serializer_class = PayrollTransactionSerializer
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    @staticmethod
    def get(request):
        user_companies = Company.objects.filter(
            Q(user=request.user) | 
            Q(id__in=CompanyEmployeeList.objects.filter(
                employee=request.user, is_deleted=False
            ).values_list('company_id', flat=True)),
            is_active=True, is_deleted=False
        ).distinct()

        # Count total companies
        number_of_company = user_companies.count()

        # Count total employees across all companies
        num_of_employees = CompanyEmployeeList.objects.filter(
            company__in=user_companies, is_active=True, is_deleted=False
        ).count()

        # Calculate total wallet balance
        wallet_balance = Wallet.objects.filter(
            account__company__in=user_companies, account__is_active=True
        ).aggregate(total_balance=Sum('balance'))['total_balance'] or 0

        response = {
            "total_company": number_of_company,
            "total_employees": num_of_employees,
            "total_wallet_balance": wallet_balance,
        }
        return Response(response, status=status.HTTP_200_OK)


class CompanyPayrollSummaryAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        PayrollApprovalPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        bulk_id = request.query_params.get("bulk_id")
        queryset = PayrollTable.objects.filter(
            company__id=company_uuid, status="DISBURSE", bulk_id=bulk_id, payroll_deleted=False
        )
        employee_count = queryset.aggregate(Count("id"))["id__count"] or 0
        
        expected_amount = (
            queryset.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
        )
        used_instant_wage = (
            queryset.aggregate(Sum("instant_wage_deduction"))[
                "instant_wage_deduction__sum"
            ]
            or 0
        )

        # company charge
        company_settings = CompanyPayrollSettings.objects.filter(company__id=company_uuid).first()
        if company_settings:
            get_payroll_charge = company_settings.charge
        else:
            get_payroll_charge = 2000

        pay_amount = round_amount(used_instant_wage)
        company_data = CompanyDetailsData.objects.filter(
            company__id=company_uuid, bulk_id=bulk_id, is_deleted=False
        ).first()
        if company_data:
            data_initiated = company_data.payroll_month
            payroll_month = data_initiated.title()
            narration = company_data.narration
        else:
            payroll_month = None
            narration = "SALARY"
        
        CONST = ConstantTable.get_constant_instance()

        pfa_switch = CompanyPayrollSettings.pfa_switch(company_uuid)
        # pfa_switch = False
        if pfa_switch:
            if CONST.send_pension_from != "NONE":
                pfa_count, pfa_amount = PensionFundAdminSettings.company_pfa_data(company_uuid, bulk_id)
            else:
                pfa_count = 0
                pfa_amount = 0
        else:
            pfa_count = 0
            pfa_amount = 0

        # get_payroll_charge = EmployeeCharge.get_payroll_charges(employee_count)

        total_pfa_charges = pfa_count * 150
        total_pfa_amount = total_pfa_charges + pfa_amount
        
        total_charges = (get_payroll_charge * employee_count) + 150
        total_account_charges = ((get_payroll_charge + 150) * employee_count) + 150
        company_ins = Company.objects.get(id=company_uuid)

        if company_ins.company_wallet_type == "CORPORATE":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="PAYROLL"
                )
                wallet_balance = Wallet.get_wallet_balance(
                    company_ins.user, account_ins
                )
            except AccountSystem.DoesNotExist:
                wallet_balance = 0
        elif company_ins.company_wallet_type == "MAIN":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="NON_CORP_PAYROLL"
                )
                wallet_balance = Wallet.get_wallet_balance(
                    company_ins.user, account_ins
                )
            except AccountSystem.DoesNotExist:
                wallet_balance = 0
        else:
            wallet_balance = 0

        for users in queryset:
            if users.payroll_user:
                try:
                    update_kyc_details(user_id=users.payroll_user.id)
                except Exception as e:
                    print(f"Error updating KYC details {users.payroll_user.email}: {e}")


        response = {
            "wallet_balance": round_amount(wallet_balance),
            "employee_count": employee_count,
            "salary_amount": round_amount(expected_amount),
            "salary_month": payroll_month,
            "total_charges": round_amount(total_charges),
            "total_acount_charges": round_amount(total_account_charges),
            "bulk_id": bulk_id,
            "narration": narration,
            "pension_amount": round_amount(total_pfa_amount),
            "instant_wage": round_amount(pay_amount),
            "total_amount": round_amount(total_charges + expected_amount + total_pfa_amount),
            "total_account_amount": round_amount(total_account_charges + expected_amount + total_pfa_amount)
        }
        return Response(response, status=status.HTTP_200_OK)


class UserTypeAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        try:
            company_ins = Company.objects.get(id=company_uuid)
        except Company.DoesNotExist:
            return Response(
                {"error": "Company does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )
        if company_ins.user == request.user:
            response = {"role": "OWNER"}
        else:

            instance = CompanyEmployeeList.objects.filter(company__id=company_uuid, employee=request.user, is_deleted=False).first()

            if instance:
                response = {
                    "role": instance.user_role,
                    "permissions": {
                        "can_disburse": instance.can_disburse,
                        "can_approve": instance.can_approve,
                        "can_add_member": instance.can_add_member,
                        "can_edit_member": instance.can_edit_member,
                        "can_delete_member": instance.can_delete_member,
                        "can_delete_payroll": instance.can_delete_payroll,
                        "can_run_payroll": instance.can_run_payroll,
                    },
                }
            else:
                return Response(
                    {"error": "user does not exist"}, status=status.HTTP_400_BAD_REQUEST
                )

        return Response(response, status=status.HTTP_200_OK)


class WalletHistoryAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):

        page = request.GET.get("page", 1)
        wallet_history_qs = DebitCreditRecordOnAccount.objects.filter(
            user=request.user, requisition_type="PERSONAL_PAYROLL"
        ).order_by("-id")
        wallet_data = WalletHistoryPaginator.paginate(request, wallet_history_qs, page)
        serializer = UserWalletHistorySerializer(wallet_data, many=True)
        try:
            data = {
                "data_type": "WALLET_DATA",
                "data": serializer.data,
                "total_page": wallet_data.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": wallet_data.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "WALLET_DATA",
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class CompanyWalletHistoryAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = UserWalletHistorySerializer

    def get_queryset(self):
        company_uuid = self.request.query_params.get("company_uuid")
        company_ins = Company.objects.get(id=company_uuid)
        CompanyPayrollSettings.create_company_payroll_settings(company=company_ins)

        if company_ins.company_wallet_type == "CORPORATE":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="PAYROLL"
                )
                requisition_type = "PAYROLL"
            except AccountSystem.DoesNotExist:
                account_ins = None
                requisition_type = "PAYROLL"
        elif company_ins.company_wallet_type == "MAIN":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="NON_CORP_PAYROLL"
                )
                requisition_type = "NON_CORP_PAYROLL"
            except AccountSystem.DoesNotExist:
                account_ins = None
                requisition_type = "NON_CORP_PAYROLL"

        try:
            wallet_ins = Wallet.objects.get(account=account_ins)
        except Wallet.DoesNotExist:
            wallet_ins = None

        wallet_history_qs = DebitCreditRecordOnAccount.objects.filter(
            wallet=wallet_ins, requisition_type=requisition_type
        ).order_by("-id")
        return wallet_history_qs

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "COMPANY_WALLET_HISTORY",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class DashBoardDataAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_ins = Company.objects.get(id=company_uuid)
        CompanyPayrollSettings.create_company_payroll_settings(company=company_ins)

        if company_ins.company_wallet_type == "CORPORATE":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="PAYROLL"
                )
                wallet_balance = Wallet.get_wallet_balance(
                    company_ins.user, account_ins
                )
            except AccountSystem.DoesNotExist:
                wallet_balance = 0
                account_ins = None
        elif company_ins.company_wallet_type == "MAIN":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="NON_CORP_PAYROLL"
                )
                wallet_balance = Wallet.get_wallet_balance(
                    company_ins.user, account_ins
                )
            except AccountSystem.DoesNotExist:
                wallet_balance = 0
                account_ins = None
        else:
            wallet_balance = 0
            account_ins = None

        company_employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employer=company_ins.user, is_active=True
        )
        company_employee_count = company_employee.aggregate(Count("id"))["id__count"]
        net_amount = (
            company_employee.aggregate(Sum("employee_net_amount"))[
                "employee_net_amount__sum"
            ]
            or 0
        )
        gross_amount = (
            company_employee.aggregate(Sum("employee_gross_amount"))[
                "employee_gross_amount__sum"
            ]
            or 0
        )
        payable_amount = (
            company_employee.aggregate(Sum("employee_payable_amount"))[
                "employee_payable_amount__sum"
            ]
            or 0
        )
        deduction_amount = (
            company_employee.aggregate(Sum("employee_other_deductions"))[
                "employee_other_deductions__sum"
            ]
            or 0
        )
        pension_amount = (
            company_employee.aggregate(Sum("employee_pension_amount"))[
                "employee_pension_amount__sum"
            ]
            or 0
        )
        life_insurance_amount = (
            company_employee.aggregate(Sum("employee_life_insurance_amount"))[
                "employee_life_insurance_amount__sum"
            ]
            or 0
        )
        hmo_amount = (
            company_employee.aggregate(Sum("employee_hmo_amount"))[
                "employee_hmo_amount__sum"
            ]
            or 0
        )
        tax_amount = (
            company_employee.aggregate(Sum("employee_tax_amount"))[
                "employee_tax_amount__sum"
            ]
            or 0
        )
        instant_wage_request = company_employee.filter(
            employee_instant_wage_status="PENDING"
        ).aggregate(Count("id"))["id__count"]
        total_deducted_amount = (
            deduction_amount
            + pension_amount
            + life_insurance_amount
            + hmo_amount
            + tax_amount
        )
        data = {
            "wallet_balance": wallet_balance,
            "account_details": {
                "account_name": account_ins.account_name if account_ins else "",
                "account_number": account_ins.account_number if account_ins else "",
                "bank_name": account_ins.bank_name if account_ins else "",
            },
            "employee_count": company_employee_count,
            "gross_amount": gross_amount,
            "net_amount": net_amount,
            "payable_amount": payable_amount,
            "deduction_amount": total_deducted_amount,
            "instant_wage_request": instant_wage_request,
        }
        return Response(data, status=status.HTTP_200_OK)


class EmployeeInstantWageRequestAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CompanyPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")

        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=request.user, is_active=True
        ).first()
        if employee_ins:
            if employee_ins.employee_instant_daily_wage:
                if (
                    employee_ins.employee_instant_wage_status == "OFF"
                    or employee_ins.employee_instant_wage_status == "REJECTED"
                ):
                    employee_ins.employee_instant_wage_status = "PENDING"
                    employee_ins.save()
                    return Response(
                        {"message": "Request sent successfully"},
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {"error": "Request already sent"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                return Response(
                    {"error": "you cannot access instant wage at this time"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "Employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class AllEmployeeInstantWageRequest(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CompanyPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    @staticmethod
    def get(request):
        page = request.GET.get("page", 1)
        company_uuid = request.query_params.get("company_uuid")
        employees = CompanyEmployeeList.objects.filter(
            company__id=company_uuid,
            employee_instant_wage_status="PENDING",
            is_active=True,
        )

        employees_list = PayrollPaginator.paginate(request, employees, page)
        serializer = ListCompanyEmployeeSerializer(employees, many=True)
        try:

            data = {
                "data_type": "INSTANT_WAGES_DATA",
                "data": serializer.data,
                "total_page": employees_list.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": employees_list.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "INSTANT_WAGES_DATA",
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class PayrollTypeAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        payroll_qs = PayrollTable.objects.filter(
            company__id=company_uuid, payroll_deleted=False)
        approval = payroll_qs.filter(status="APPROVAL")
        disburse = payroll_qs.filter(status="DISBURSE")
        omitted = payroll_qs.filter(status="OMITTED")
        if approval:
            data = {"payroll_status": "APPROVAL"}
        else:
            if disburse:
                data = {"payroll_status": "DISBURSE"}
            else:
                if omitted:
                    data = {"payroll_status": "OMITTED"}
                else:
                    data = {"payroll_status": "OFF"}

        return Response(data, status=status.HTTP_200_OK)


class OmitManyAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        PayrollApprovalPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")

        queryset = PayrollTable.objects.filter(
            company__id=company_uuid, status="OMITTED"
        ).order_by(Lower("last_name"))
        # print(queryset)
        serializer = PayrollApprovalSerializer(queryset, many=True)
        total_amount = queryset.aggregate(Sum("payable_amount"))["payable_amount__sum"]
        company_data = CompanyDetailsData.objects.filter(
            company__id=company_uuid, is_deleted=False
        ).last()
        if company_data:
            data_initiated = company_data.payroll_date
            payroll_month = data_initiated.strftime("%b").upper()
        else:
            data_initiated = None
            payroll_month = None
        serializer = PayrollApprovalSerializer(queryset, many=True)
        return Response(
            {
                "employee_count": queryset.aggregate(Count("id"))["id__count"],
                "total_amount": total_amount,
                "date_initiated": data_initiated,
                "payroll_month": payroll_month,
                "status": "OMITTED",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = OmitSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data.get("data")
        for user_id in data:
            employee = PayrollTable.objects.filter(
                id=user_id,
                company__id=company_uuid,
                status="DISBURSE",
                payroll_deleted=False,
            ).first()
            if employee:
                employee.status = "OMITTED"
                employee.save()
        return Response(
            {"message": "users payroll has been omitted"}, status=status.HTTP_200_OK
        )


class OmitDisburseManyAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, PayrollApprovalPermission]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = OmitSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data.get("data")
        for user_id in data:
            # print(user_id)
            employee = PayrollTable.objects.filter(
                id=user_id,
                company__id=company_uuid,
                status="OMITTED",
                payroll_deleted=False,
            ).first()
            if employee:
                employee.status = "DISBURSE"
                employee.save()
        return Response(
            {"message": "users payroll has been returned to disburse"},
            status=status.HTTP_200_OK,
        )


class EmployeeProfileAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            serializer = EmployeeProfileSerializer(employee)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            serializer = ViewEditEmployeeSerializer(
                employee,
                data=request.data,
                partial=True,
                context={"employee_instance": employee},
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(
                {"message": "employee updated successfully"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            serializer = AddEmployeeProfilePictureSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            profile_picture = serializer.validated_data.get("profile_picture")
            uploaded_file = upload_file_aws_s3_bucket(
                model_instance_id=employee.id,
                file=profile_picture,
                model_name="EmployeeProfilePicture",
            )
            response = {
                "status": "Success",
                "message": "profile picture updated successfully",
                "file_url": uploaded_file if uploaded_file else "",
            }
            return Response(response, status=status.HTTP_200_OK)

        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class EmployerGroupPayrollHistoryAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")

        company_data_base_query = CompanyDetailsData.objects.filter(
            company__id=company_uuid, is_deleted=False
        )
        payroll_base_query = PayrollTable.objects.filter(
            company__id=company_uuid, payroll_deleted=False
        ).order_by("payroll_date")
        payroll_history_qs = company_data_base_query.values(
            "payroll_month", "payroll_year"
        ).distinct()
        # Define a custom sorting key function
        payroll_history_qs = sorted(
            payroll_history_qs,
            key=lambda x: (
                int(x["payroll_year"]),
                [
                    "january",
                    "february",
                    "march",
                    "april",
                    "may",
                    "june",
                    "july",
                    "august",
                    "september",
                    "october",
                    "november",
                    "december",
                ].index(x["payroll_month"].lower()),
            ),
            reverse=True,
        )
        # print(payroll_history_qs)
        if len(payroll_history_qs) == 1:
            current_month = payroll_history_qs[0]["payroll_month"]
            current_year = payroll_history_qs[0]["payroll_year"]
            payroll_history_current_qs = company_data_base_query.filter(
                payroll_month=current_month, payroll_date__year=current_year
            )
            current_bulk_id_list = []
            [
                current_bulk_id_list.append(payroll.bulk_id)
                for payroll in payroll_history_current_qs
            ]
            current_amount = 0
            average_count = 0
            for current_bulk in current_bulk_id_list:
                current_amount += (
                    payroll_base_query.filter(
                        bulk_id=current_bulk, status="DISBURSED"
                    ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                    or 0
                )
                average_count += (
                    payroll_base_query.filter(
                        bulk_id=current_bulk, status="DISBURSED"
                    ).count()
                    or 0
                )

            try:
                average_payroll = int(current_amount / average_count)
            except ZeroDivisionError:
                average_payroll = 0

            data = {
                "current_month": current_amount,
                "current_percentage": 0,
                "previous_month": 0,
                "previous_percentage": 0,
                "average_pay": average_payroll,
            }
        elif len(payroll_history_qs) == 2:
            # custom_sort_group_history(payroll_history_qs)
            # payroll_history_qs = sorted(data, key=custom_sort_group_history, reverse=True)
            current_month = payroll_history_qs[0]["payroll_month"]
            current_year = payroll_history_qs[0]["payroll_year"]

            previous_month = payroll_history_qs[1]["payroll_month"]
            previous_year = payroll_history_qs[1]["payroll_year"]

            # print(current_month, current_year, previous_month, previous_year)
            payroll_history_current_qs = company_data_base_query.filter(
                payroll_month=current_month, payroll_date__year=current_year
            )
            payroll_history_previous_qs = company_data_base_query.filter(
                payroll_month=previous_month, payroll_date__year=previous_year
            )
            current_bulk_id_list = []
            previous_bulk_id_list = []
            [
                current_bulk_id_list.append(payroll.bulk_id)
                for payroll in payroll_history_current_qs
            ]
            [
                previous_bulk_id_list.append(payroll.bulk_id)
                for payroll in payroll_history_previous_qs
            ]

            current_amount = 0
            previous_amount = 0
            average_count = 0
            for current_bulk in current_bulk_id_list:
                current_amount += (
                    payroll_base_query.filter(
                        bulk_id=current_bulk, status="DISBURSED"
                    ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                    or 0
                )
                average_count += (
                    payroll_base_query.filter(
                        bulk_id=current_bulk, status="DISBURSED"
                    ).count()
                    or 0
                )

            for previous_bulk in previous_bulk_id_list:
                previous_amount += (
                    payroll_base_query.filter(
                        bulk_id=previous_bulk, status="DISBURSED"
                    ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                    or 0
                )

            try:
                current_percentage = ((current_amount / previous_amount) * 100) - 100
            except ZeroDivisionError:
                current_percentage = 0
            try:
                previous_percentage = (
                    (previous_amount) / (current_amount + previous_amount) * 100
                )
            except ZeroDivisionError:
                previous_percentage = 0
            try:
                # print(average_count)
                average_payroll = int(current_amount / average_count)
            except ZeroDivisionError:
                average_payroll = 0
            data = {
                "current_month": current_amount,
                "current_percentage": current_percentage,
                "previous_month": previous_amount,
                "previous_percentage": 0,
                "average_pay": average_payroll,
            }
        elif len(payroll_history_qs) > 2:
            # custom_sort_group_history(payroll_history_qs)
            # payroll_history_qs = sorted(data, key=custom_sort_group_history, reverse=True)
            current_month = payroll_history_qs[0]["payroll_month"]
            current_year = payroll_history_qs[0]["payroll_year"]

            previous_month = payroll_history_qs[1]["payroll_month"]
            previous_year = payroll_history_qs[1]["payroll_year"]

            difference_month = payroll_history_qs[2]["payroll_month"]
            difference_year = payroll_history_qs[2]["payroll_year"]

            # print(current_month, current_year, previous_month, previous_year)
            payroll_history_current_qs = company_data_base_query.filter(
                payroll_month=current_month, payroll_date__year=current_year
            )
            payroll_history_previous_qs = company_data_base_query.filter(
                payroll_month=previous_month, payroll_date__year=previous_year
            )
            payroll_history_difference_qs = company_data_base_query.filter(
                payroll_month=difference_month, payroll_date__year=difference_year
            )
            current_bulk_id_list = []
            previous_bulk_id_list = []
            difference_bulk_id_list = []
            [
                current_bulk_id_list.append(payroll.bulk_id)
                for payroll in payroll_history_current_qs
            ]
            [
                previous_bulk_id_list.append(payroll.bulk_id)
                for payroll in payroll_history_previous_qs
            ]
            [
                difference_bulk_id_list.append(payroll.bulk_id)
                for payroll in payroll_history_difference_qs
            ]

            current_amount = 0
            previous_amount = 0
            difference_amount = 0
            average_count = 0
            for current_bulk in current_bulk_id_list:
                current_amount += (
                    payroll_base_query.filter(
                        bulk_id=current_bulk, status="DISBURSED"
                    ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                    or 0
                )
                average_count += (
                    payroll_base_query.filter(
                        bulk_id=current_bulk, status="DISBURSED"
                    ).count()
                    or 0
                )

            for previous_bulk in previous_bulk_id_list:
                previous_amount += (
                    payroll_base_query.filter(
                        bulk_id=previous_bulk, status="DISBURSED"
                    ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                    or 0
                )

            for difference_bulk in difference_bulk_id_list:
                difference_amount += (
                    payroll_base_query.filter(
                        bulk_id=difference_bulk, status="DISBURSED"
                    ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                    or 0
                )
            try:
                current_percentage = ((current_amount / previous_amount) * 100) - 100
            except ZeroDivisionError:
                current_percentage = 0
            try:
                previous_percentage = (
                    (previous_amount / difference_amount) * 100
                ) - 100
            except ZeroDivisionError:
                previous_percentage = 0
            try:
                # print(average_count)
                average_payroll = int(current_amount / average_count)
            except ZeroDivisionError:
                average_payroll = 0
            print(difference_amount, "DIFF AMOUNT")
            data = {
                "current_month": current_amount,
                "current_percentage": current_percentage,
                "previous_month": previous_amount,
                "previous_percentage": previous_percentage,
                "average_pay": average_payroll,
            }
        else:
            data = {
                "current_month": 0,
                "current_percentage": 0,
                "previous_month": 0,
                "previous_percentage": 0,
                "average_pay": 0,
            }

        return Response(data, status=status.HTTP_200_OK)


class PayrollSpreadAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")

        today = datetime.now().today()
        first = today.replace(day=1)
        session = first - timedelta(days=1)
        session_month = session.strftime("%m")
        # print(session_month, type(session_month), "month")
        session_year = session.strftime("%Y")
        payroll_spread_qs = CompanyDetailsData.objects.filter(
            company__id=company_uuid,
            payroll_date__month=session_month,
            payroll_date__year=session_year,
            is_deleted=False
        ).last()
        if payroll_spread_qs:
            # print(payroll_spread_qs.bulk_id, "bulk_id")
            # 30000 - 100000

            thirty_to_hundred = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=30000,
                    payable_amount__lte=100000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            thirty_to_hundred_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=30000,
                payable_amount__lte=100000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            # 100000 - 300000

            hundred_to_three_hundred = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=100000,
                    payable_amount__lte=300000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            hundred_to_three_hundred_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=100000,
                payable_amount__lte=300000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            # 300000 - 600000

            three_hundred_to_six_hundred = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=300000,
                    payable_amount__lte=600000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            three_hundred_to_six_hundred_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=300000,
                payable_amount__lte=600000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            # 600000 - 900000

            six_hundred_to_nine_hundred = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=600000,
                    payable_amount__lte=900000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            six_hundred_to_nine_hundred_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=600000,
                payable_amount__lte=900000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            # 900000 and above

            nine_hundred_and_above = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=900000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            nine_hundred_and_above_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=900000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            total_month_payroll = (
                thirty_to_hundred
                + hundred_to_three_hundred
                + three_hundred_to_six_hundred
                + six_hundred_to_nine_hundred
                + nine_hundred_and_above
            )
            total_payroll_employee = (
                thirty_to_hundred_count
                + hundred_to_three_hundred_count
                + three_hundred_to_six_hundred_count
                + six_hundred_to_nine_hundred_count
                + nine_hundred_and_above_count
            )

            try:
                thirty_to_hundred_percent = (
                    thirty_to_hundred / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                thirty_to_hundred_percent = 0

            try:
                hundred_to_three_hundred_percent = (
                    hundred_to_three_hundred / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                hundred_to_three_hundred_percent = 0

            try:
                three_hundred_to_six_hundred_percent = (
                    three_hundred_to_six_hundred / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                three_hundred_to_six_hundred_percent = 0

            try:
                six_hundred_to_nine_hundred_percent = (
                    six_hundred_to_nine_hundred / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                six_hundred_to_nine_hundred_percent = 0

            try:
                nine_hundred_and_above_percent = (
                    nine_hundred_and_above / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                nine_hundred_and_above_percent = 0

            try:
                thirty_to_hundred_count_percent = (
                    thirty_to_hundred_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                thirty_to_hundred_count_percent = 0

            try:
                hundred_to_three_hundred_count_percent = (
                    hundred_to_three_hundred_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                hundred_to_three_hundred_count_percent = 0

            try:
                three_hundred_to_six_hundred_count_percent = (
                    three_hundred_to_six_hundred_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                three_hundred_to_six_hundred_count_percent = 0

            try:
                six_hundred_to_nine_hundred_count_percent = (
                    six_hundred_to_nine_hundred_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                six_hundred_to_nine_hundred_count_percent = 0

            try:
                nine_hundred_and_above_count_percent = (
                    nine_hundred_and_above_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                nine_hundred_and_above_count_percent = 0

            data = {
                "thirty_to_hundred": {
                    "employee_count": thirty_to_hundred_count,
                    "payroll_amount": thirty_to_hundred,
                    "payroll_percentage": thirty_to_hundred_percent,
                    "employee_percentage": thirty_to_hundred_count_percent,
                },
                "hundred_to_three_hundred": {
                    "employee_count": hundred_to_three_hundred_count,
                    "payroll_amount": hundred_to_three_hundred,
                    "payroll_percentage": hundred_to_three_hundred_percent,
                    "employee_percentage": hundred_to_three_hundred_count_percent,
                },
                "three_hundred_to_six_hundred": {
                    "employee_count": three_hundred_to_six_hundred_count,
                    "payroll_amount": three_hundred_to_six_hundred,
                    "payroll_percentage": three_hundred_to_six_hundred_percent,
                    "employee_percentage": three_hundred_to_six_hundred_count_percent,
                },
                "six_hundred_to_nine_hundred": {
                    "employee_count": six_hundred_to_nine_hundred_count,
                    "payroll_amount": six_hundred_to_nine_hundred,
                    "payroll_percentage": six_hundred_to_nine_hundred_percent,
                    "employee_percentage": six_hundred_to_nine_hundred_count_percent,
                },
                "nine_hundred_and_above": {
                    "employee_count": nine_hundred_and_above_count,
                    "payroll_amount": nine_hundred_and_above,
                    "payroll_percentage": nine_hundred_and_above_percent,
                    "employee_percentage": nine_hundred_and_above_count_percent,
                },
            }
            return Response(data, status=status.HTTP_200_OK)
        else:
            data = {
                "thirty_to_hundred": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
                "hundred_to_three_hundred": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
                "three_hundred_to_six_hundred": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
                "six_hundred_to_nine_hundred": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
                "nine_hundred_and_above": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
            }
            return Response(data, status=status.HTTP_200_OK)


class PayrollSpreadByMonthAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        try:
            month = request.query_params.get("month")
        except AttributeError:
            return Response(
                {"error": "month is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        today = datetime.now().today()
        session_year = today.strftime("%Y")
        payroll_spread_qs = CompanyDetailsData.objects.filter(
            company__id=company_uuid,
            payroll_date__month=month,
            payroll_date__year=session_year,
            is_deleted=False
        ).first()
        if payroll_spread_qs:
            # print(payroll_spread_qs.bulk_id, "bulk_id")

            # 30000 - 100000

            thirty_to_hundred = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=30000,
                    payable_amount__lte=100000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            thirty_to_hundred_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=30000,
                payable_amount__lte=100000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            # 100000 - 300000

            hundred_to_three_hundred = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=100000,
                    payable_amount__lte=300000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            hundred_to_three_hundred_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=100000,
                payable_amount__lte=300000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            # 300000 - 600000

            three_hundred_to_six_hundred = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=300000,
                    payable_amount__lte=600000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            three_hundred_to_six_hundred_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=300000,
                payable_amount__lte=600000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            # 600000 - 900000

            six_hundred_to_nine_hundred = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=600000,
                    payable_amount__lte=900000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            six_hundred_to_nine_hundred_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=600000,
                payable_amount__lte=900000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            # 900000 and above

            nine_hundred_and_above = (
                PayrollTable.objects.filter(
                    company__id=company_uuid,
                    bulk_id=payroll_spread_qs.bulk_id,
                    payable_amount__gte=900000,
                    status="DISBURSED",
                ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                or 0
            )
            nine_hundred_and_above_count = PayrollTable.objects.filter(
                company__id=company_uuid,
                bulk_id=payroll_spread_qs.bulk_id,
                payable_amount__gte=900000,
                status="DISBURSED",
            ).aggregate(Count("id"))["id__count"]

            total_month_payroll = (
                thirty_to_hundred
                + hundred_to_three_hundred
                + three_hundred_to_six_hundred
                + six_hundred_to_nine_hundred
                + nine_hundred_and_above
            )
            total_payroll_employee = (
                thirty_to_hundred_count
                + hundred_to_three_hundred_count
                + three_hundred_to_six_hundred_count
                + six_hundred_to_nine_hundred_count
                + nine_hundred_and_above_count
            )

            try:
                thirty_to_hundred_percent = (
                    thirty_to_hundred / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                thirty_to_hundred_percent = 0

            try:
                hundred_to_three_hundred_percent = (
                    hundred_to_three_hundred / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                hundred_to_three_hundred_percent = 0

            try:
                three_hundred_to_six_hundred_percent = (
                    three_hundred_to_six_hundred / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                three_hundred_to_six_hundred_percent = 0

            try:
                six_hundred_to_nine_hundred_percent = (
                    six_hundred_to_nine_hundred / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                six_hundred_to_nine_hundred_percent = 0

            try:
                nine_hundred_and_above_percent = (
                    nine_hundred_and_above / total_month_payroll
                ) * 100
            except ZeroDivisionError:
                nine_hundred_and_above_percent = 0

            try:
                thirty_to_hundred_count_percent = (
                    thirty_to_hundred_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                thirty_to_hundred_count_percent = 0

            try:
                hundred_to_three_hundred_count_percent = (
                    hundred_to_three_hundred_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                hundred_to_three_hundred_count_percent = 0

            try:
                three_hundred_to_six_hundred_count_percent = (
                    three_hundred_to_six_hundred_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                three_hundred_to_six_hundred_count_percent = 0

            try:
                six_hundred_to_nine_hundred_count_percent = (
                    six_hundred_to_nine_hundred_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                six_hundred_to_nine_hundred_count_percent = 0

            try:
                nine_hundred_and_above_count_percent = (
                    nine_hundred_and_above_count / total_payroll_employee
                ) * 100
            except ZeroDivisionError:
                nine_hundred_and_above_count_percent = 0

            data = {
                "thirty_to_hundred": {
                    "employee_count": thirty_to_hundred_count,
                    "payroll_amount": thirty_to_hundred,
                    "payroll_percentage": thirty_to_hundred_percent,
                    "employee_percentage": thirty_to_hundred_count_percent,
                },
                "hundred_to_three_hundred": {
                    "employee_count": hundred_to_three_hundred_count,
                    "payroll_amount": hundred_to_three_hundred,
                    "payroll_percentage": hundred_to_three_hundred_percent,
                    "employee_percentage": hundred_to_three_hundred_count_percent,
                },
                "three_hundred_to_six_hundred": {
                    "employee_count": three_hundred_to_six_hundred_count,
                    "payroll_amount": three_hundred_to_six_hundred,
                    "payroll_percentage": three_hundred_to_six_hundred_percent,
                    "employee_percentage": three_hundred_to_six_hundred_count_percent,
                },
                "six_hundred_to_nine_hundred": {
                    "employee_count": six_hundred_to_nine_hundred_count,
                    "payroll_amount": six_hundred_to_nine_hundred,
                    "payroll_percentage": six_hundred_to_nine_hundred_percent,
                    "employee_percentage": six_hundred_to_nine_hundred_count_percent,
                },
                "nine_hundred_and_above": {
                    "employee_count": nine_hundred_and_above_count,
                    "payroll_amount": nine_hundred_and_above,
                    "payroll_percentage": nine_hundred_and_above_percent,
                    "employee_percentage": nine_hundred_and_above_count_percent,
                },
            }
        else:
            data = {
                "thirty_to_hundred": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
                "hundred_to_three_hundred": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
                "three_hundred_to_six_hundred": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
                "six_hundred_to_nine_hundred": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
                "nine_hundred_and_above": {
                    "employee_count": 0,
                    "payroll_amount": 0,
                    "payroll_percentage": 0,
                    "employee_percentage": 0,
                },
            }
        return Response(data, status=status.HTTP_200_OK)


class PayrollSpreadByRangeAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        range_lt = request.query_params.get("range_lt")
        range_gt = request.query_params.get("range_gt")
        try:
            month = request.query_params.get("month")
        except AttributeError:
            return Response(
                {"error": "month is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        if range_lt and range_gt and month:
            if int(range_lt) <= int(range_gt):
                today = datetime.now().today()
                session_year = today.strftime("%Y")
                payroll_spread_qs = CompanyDetailsData.objects.filter(
                    company__id=company_uuid,
                    payroll_date__month=month,
                    payroll_date__year=session_year,
                    is_deleted=False
                ).first()
                if payroll_spread_qs:
                    # print(payroll_spread_qs.bulk_id, "bulk_id")

                    payroll_amount = (
                        PayrollTable.objects.filter(
                            company__id=company_uuid,
                            bulk_id=payroll_spread_qs.bulk_id,
                            payable_amount__gte=int(range_lt),
                            payable_amount__lte=int(range_gt),
                            status="DISBURSED",
                        ).aggregate(Sum("payable_amount"))["payable_amount__sum"]
                        or 0
                    )
                    employee_count = PayrollTable.objects.filter(
                        company__id=company_uuid,
                        bulk_id=payroll_spread_qs.bulk_id,
                        payable_amount__gte=int(range_lt),
                        payable_amount__lte=int(range_gt),
                        status="DISBURSED",
                    ).aggregate(Count("id"))["id__count"]

                    data = {
                        "employee_count": employee_count,
                        "payroll_amount": payroll_amount,
                        "payroll_percentage": 0,
                        "employee_percentage": 0,
                    }
                else:
                    data = {
                        "employee_count": 0,
                        "payroll_amount": 0,
                        "payroll_percentage": 0,
                        "employee_percentage": 0,
                    }
                return Response(data, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"error": "range_lt must be less than range_gt"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "range_lt, range_gt and month are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AcceptRejectInstantWageRequestAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CompanyPermission,
        EmployeePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        user_id = request.query_params.get("user_id")
        serializer = InstantWageRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        instant_wage_request = serializer.validated_data.get("request_type")
        # print(instant_wage_request, "request_type")

        start_date = datetime.today()
        month = start_date.month
        year = start_date.year
        month_days = calendar.monthrange(year, month)[1]
        todays_date = month_days - start_date.day
        instant_net_wage_date = int(start_date.day)

        employee = CompanyEmployeeList.objects.filter(
            id=user_id, company__id=company_uuid, is_active=True
        ).first()
        if employee:
            if employee.employee_instant_daily_wage:

                if employee.employee_instant_wage_status == "PENDING":
                    if instant_wage_request == "SUCCESSFUL":
                        try:
                            daily_wage_amount = round_amount(
                                employee.set_instant_wage_amount / month_days
                            )
                            net_available_wage = round_amount(
                                instant_net_wage_date * daily_wage_amount
                            )
                            collected_wage = round_amount(
                                instant_net_wage_date * daily_wage_amount
                            )
                            current_daily_wage = round_amount(
                                employee.set_instant_wage_amount - collected_wage
                            )
                            employee.employee_instant_wage_status = "SUCCESSFUL"
                            employee.employee_instant_daily_wage_date = start_date
                            employee.employee_instant_daily_wage_amount = (
                                daily_wage_amount
                            )
                            employee.employee_instant_daily_remaining_wage_amount = (
                                current_daily_wage
                            )
                            # employee.employee_instant_used_wage_amount=0
                            employee.employee_instant_daily_collected_wage_amount = (
                                collected_wage
                            )
                            employee.employee_instant_net_available_wage = (
                                net_available_wage
                            )
                            employee.save()
                        except ZeroDivisionError:
                            return Response(
                                {"error": "cannot use instant wage at this time"},
                                status=status.HTTP_400_BAD_REQUEST,
                            )
                        return Response(
                            {"message": f"instant wage {instant_wage_request}"},
                            status=status.HTTP_200_OK,
                        )
                    elif instant_wage_request == "REJECTED":
                        employee.employee_instant_wage_status = instant_wage_request
                        employee.save()
                        return Response(
                            {"message": f"instant wage {instant_wage_request}"},
                            status=status.HTTP_200_OK,
                        )
                    else:
                        return Response(
                            {"error": "invalid choice"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                else:
                    return Response(
                        {"error": "employee did not make instant wage request"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                return Response(
                    {"error": "employee instant wage is not available"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        user_id = request.query_params.get("user_id")
        serializer = EditInstantWageRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        instant_wage_request = serializer.validated_data.get("request_type")
        # print(instant_wage_request, "request_type")
        employee = CompanyEmployeeList.objects.filter(
            id=user_id, company__id=company_uuid, is_active=True
        ).first()
        if employee:
            if employee.employee_instant_wage_status == "SUCCESSFUL":
                employee.employee_instant_wage_status = instant_wage_request
                employee.save()
                return Response(
                    {"message": f"instant wage {instant_wage_request}"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": "employee instant wage already off"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class AcceptAllInstantWageRequestAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CompanyPermission,
        EmployeePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        # make instant wage turn on for exist instant wage so that their money do not clear off
        start_date = datetime.today()
        month = start_date.month
        year = start_date.year
        month_days = calendar.monthrange(year, month)[1]
        todays_date = month_days - start_date.day
        instant_net_wage_date = int(start_date.day)

        serializer = AcceptInstantWageSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        instant_wage_type = serializer.validated_data.get("instant_wage_type")
        settings = serializer.validated_data.get("settings")
        failed_employees = []
        already_running_employees = []
        employee_list = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, is_active=True, employee_instant_daily_wage=True
        )
        if instant_wage_type == "DEFAULT":

            if employee_list:
                for employee in employee_list:
                    if employee.set_instant_wage_amount <= 0:
                        failed_employees.append(
                            {
                                "email": employee.employee_email,
                                "first_name": employee.employee_first_name,
                                "last_name": employee.employee_last_name,
                            }
                        )
                    else:
                        if employee.employee_instant_wage_status == "SUCCESSFUL":
                            already_running_employees.append(
                                {
                                    "email": employee.employee_email,
                                    "first_name": employee.employee_first_name,
                                    "last_name": employee.employee_last_name,
                                }
                            )
                        else:
                            try:
                                daily_wage_amount = round_amount(
                                    employee.set_instant_wage_amount / month_days
                                )
                                net_available_wage = round_amount(
                                    instant_net_wage_date * daily_wage_amount
                                )
                                collected_wage = round_amount(
                                    instant_net_wage_date * daily_wage_amount
                                )
                                current_daily_wage = round_amount(
                                    employee.set_instant_wage_amount - collected_wage
                                )
                                employee.employee_instant_wage_status = "SUCCESSFUL"
                                employee.employee_instant_daily_wage_date = start_date
                                employee.employee_instant_daily_wage_amount = (
                                    daily_wage_amount
                                )
                                employee.employee_instant_daily_remaining_wage_amount = (
                                    current_daily_wage
                                )
                                # employee.employee_instant_used_wage_amount=0
                                employee.employee_instant_daily_collected_wage_amount = (
                                    collected_wage
                                )
                                employee.employee_instant_net_available_wage = (
                                    net_available_wage
                                )
                                employee.save()
                            except ZeroDivisionError:
                                failed_employees.append(
                                    {
                                        "email": employee.employee_email,
                                        "first_name": employee.employee_first_name,
                                        "last_name": employee.employee_last_name,
                                    }
                                )

            response = {
                "message": "instant wage turn on for all employees",
                "failed_employees": failed_employees,
                "already_running": already_running_employees,
            }

        elif instant_wage_type == "SETTINGS":
            setting_percent = settings
            if employee_list:
                for employee in employee_list:
                    if employee.employee_payable_amount <= 0:
                        failed_employees.append(
                            {
                                "email": employee.employee_email,
                                "first_name": employee.employee_first_name,
                                "last_name": employee.employee_last_name,
                            }
                        )
                    else:
                        if employee.employee_instant_wage_status == "SUCCESSFUL":
                            already_running_employees.append(
                                {
                                    "email": employee.employee_email,
                                    "first_name": employee.employee_first_name,
                                    "last_name": employee.employee_last_name,
                                }
                            )
                        else:
                            settings_amount = (
                                round_amount(setting_percent / 100)
                                * employee.set_instant_wage_amount
                            )
                            daily_wage_amount = round_amount(
                                settings_amount / month_days
                            )
                            collected_wage = round_amount(
                                instant_net_wage_date * daily_wage_amount
                            )
                            current_daily_wage = round_amount(
                                settings_amount - collected_wage
                            )
                            employee.custom_daily_wage = True
                            employee.employee_instant_wage_status = "SUCCESSFUL"
                            employee.employee_instant_daily_remaining_wage_amount = (
                                current_daily_wage
                            )
                            # employee.employee_instant_used_wage_amount=0
                            employee.employee_instant_daily_collected_wage_amount = (
                                collected_wage
                            )
                            employee.employee_instant_net_available_wage = (
                                net_available_wage
                            )
                            employee.employee_instant_daily_wage_date = start_date
                            employee.custom_daily_wage_amount = daily_wage_amount
                            employee.custom_instant_wage_percentage = setting_percent
                            employee.save()
            response = {
                "message": "instant wage turn on for all employees",
                "failed_employees": failed_employees,
                "already_running": already_running_employees,
            }
        else:
            response = {
                "message": "instant wage not available for the selected choice",
                "failed_employees": failed_employees,
                "already_running": already_running_employees,
            }

        return Response(response, status=status.HTTP_200_OK)


class CheckUserAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        user_status = False
        # print(request.user)
        company_ins = Company.objects.filter(user=request.user)
        if company_ins:
            user_status = True
        else:
            employee_instance = CompanyEmployeeList.objects.filter(
                employee=request.user, is_active=True
            )
            if employee_instance:
                user_status = True
        return Response({"status": user_status}, status=status.HTTP_200_OK)


class EmployeeDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            account_ins = AccountSystem.objects.get(
                user=request.user, account_type="PERSONAL_PAYROLL"
            )
            wallet_balance = Wallet.get_wallet_balance(request.user, account_ins)
        except AccountSystem.DoesNotExist:
            wallet_balance = 0
        except AccountSystem.MultipleObjectsReturned:
            wallet_balance = 0

        data = {
            "wallet_balance": wallet_balance,
            "pension_balance": 0,
            "loan_balance": 0,
        }
        return Response(data, status=status.HTTP_200_OK)


class PayOutAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        CanInitiateTransfer,
        UserIsActive,
        UserHasPin,
        VerifyCompanyEmployeeSendMoney
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        request_user = request.user

        serializer = PayOutSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        disburse_pin = serializer.validated_data.get("disburse_pin")
        payout_choice = serializer.validated_data.get("payout_choice")
        disburse_type = serializer.validated_data.get("disburse_type")
        narration_des = serializer.validated_data.get("narration")

        if narration_des:
            narration = narration_des
        else:
            narration = f"PAYOUT-{payout_choice}"

        # Check Transaction Pin
        check_pin = User.check_sender_payroll_pin(
            user=request_user, pincode=disburse_pin
        )
        if not check_pin:
            return Response(
                {
                    "error": "error",
                    "message": "Incorrect Pin",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check if user has account
        try:
            account_ins = AccountSystem.objects.get(
                user=request_user, account_type=disburse_type
            )
        except AccountSystem.DoesNotExist:
            return Response(
                {
                    "error": "170",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check if user has wallet

        wallet_ins = Wallet.get_wallet_instance(request_user, account_ins)
        if wallet_ins is None:
            return Response(
                {
                    "error": "171",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # get wallet balance
        wallet_balance = wallet_ins.balance
        # get pay amount
        get_total_amount = 0
        amount = serializer.validated_data.get("amount")
        
        CONST = ConstantTable.get_constant_instance()
        # get charges for payout
        payout_charge = (
            CONST.withdrawal_fee + 50
            if payout_choice == "ACCOUNT"
            else 50
        )

        # get total payout amount with charges
        get_total_charged_amount = round_amount(amount + payout_charge)
        get_total_amount = get_total_charged_amount
        
        # check wallet balance
        if wallet_balance < get_total_amount:
            response = {
                "error": "161",
                "message": "You do not have sufficient balance to make this transaction",
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        if get_total_amount <= 0.0:
            response = {
                "error": "162",
                "message": "amount must be greater than 0",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        else:
            get_total_amount = get_total_amount

        # disburse_type = "PAYROLL" if payout_choice == "ACCOUNT" else "PERSONAL_PAYROLL"
        if payout_choice == "BUDDY":
            CONST = ConstantTable.get_constant_instance()
            if CONST.send_money_via_buddy is False:
                response = {
                    "error": "02",
                    "message": "Service is currently not available",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            # buddy account details
            account_number = request_user.account_no
            account_name = request_user.account_name
            bank_name = request_user.bank
            bank_code = request_user.bank_code

            if not account_number or not account_name or not bank_name or not bank_code:
                response = {"error": "103", "message": "incomplete account details"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if bank_code != "999999":
                response = {"error": "104", "message": "invalid buddy transfer"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # Send Payout Buddy
            payout_task.apply_async(
                queue="payout_transfer",
                kwargs={
                    "user": request_user.id,
                    "payout_type": disburse_type,
                    "account_ins": account_ins.id,
                    "disburse_pin": disburse_pin,
                    "account_number": account_number,
                    "account_name": account_name,
                    "bank_name": bank_name,
                    "bank_code": bank_code,
                    "amount": amount,
                    "narration": narration,
                    "charge_fee": None,
                },
            )
            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": amount,
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)

        elif payout_choice == "ACCOUNT":
            CONST = ConstantTable.get_constant_instance()
            if CONST.send_money is False:
                response = {
                    "error": "02",
                    "message": "Service is currently not available",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            # get other account details

            account_number = serializer.validated_data.get("account_number")
            account_name = serializer.validated_data.get("account_name")
            bank_name = serializer.validated_data.get("bank_name")
            bank_code = serializer.validated_data.get("bank_code")

            if not account_number or not account_name or not bank_name or not bank_code:
                response = {"error": "103", "message": "incomplete account details"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # Payout Account
            payout_task.apply_async(
                queue="payout_transfer",
                kwargs={
                    "user": request_user.id,
                    "payout_type": disburse_type,
                    "account_ins": account_ins.id,
                    "disburse_pin": disburse_pin,
                    "account_number": account_number,
                    "account_name": account_name,
                    "bank_name": bank_name,
                    "bank_code": bank_code,
                    "amount": amount,
                    "narration": narration,
                    "charge_fee": CONST.withdrawal_fee,
                },
            )
            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": amount,
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)

        else:
            response = {
                "message": "failed",
                "data": {
                    "message": "Transaction could not complete",
                    "amount_sent": 0,
                    "processed_transaction_count": 0,
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
class CompanyPayOutAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        CanInitiateTransfer,
        UserIsActive,
        UserHasPin,
        EmployeeIsActive,
        PayrollDisbursePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        request_user = request.user
        company_uuid = request.query_params.get("company_uuid")
        company_ins = Company.objects.get(id=company_uuid)

        serializer = CompanyPayOutSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        

        disburse_pin = serializer.validated_data.get("disburse_pin")
        payout_choice = serializer.validated_data.get("payout_choice")
        disburse_type = serializer.validated_data.get("disburse_type")
        narration_des = serializer.validated_data.get("narration")

        if narration_des:
            narration = narration_des
        else:
            narration = f"PAYOUT-{payout_choice}"

        # Check Transaction Pin
        check_pin = User.check_sender_payroll_pin(
            user=request_user, pincode=disburse_pin
        )
        if not check_pin:
            return Response(
                {
                    "error": "error",
                    "message": "Incorrect Pin",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check if user has account
        if company_ins.company_wallet_type == "CORPORATE":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="PAYROLL"
                )
                requisition_type = "PAYROLL"
            except AccountSystem.DoesNotExist:
                account_ins = None
                requisition_type = "PAYROLL"
        elif company_ins.company_wallet_type == "MAIN":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="NON_CORP_PAYROLL"
                )
                requisition_type = "NON_CORP_PAYROLL"
            except AccountSystem.DoesNotExist:
                account_ins = None
                requisition_type = "NON_CORP_PAYROLL"
        else:
            account_ins = None

        if account_ins is None:
            return Response(
                    {
                        "error": "170",
                        "message": "You do not have sufficient balance to make this transaction",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            

        # check if user has wallet
        wallet_ins = Wallet.get_wallet_instance(request_user, account_ins)
        if wallet_ins is None:
            return Response(
                {
                    "error": "171",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        # get wallet balance
        wallet_balance = wallet_ins.balance
        # get pay amount
        get_total_amount = 0
        amount = serializer.validated_data.get("amount")

        # get charges for payout
        payout_charge = (
            ConstantTable.get_constant_instance().withdrawal_fee + 50
            if payout_choice == "ACCOUNT"
            else 0
        )

        # get total payout amount with charges
        get_total_charged_amount = round_amount(amount + payout_charge)
        get_total_amount = get_total_charged_amount

        # check wallet balance
        if wallet_balance < get_total_amount:
            response = {
                "error": "161",
                "message": "You do not have sufficient balance to make this transaction",
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        if get_total_amount <= 0.0:
            response = {
                "error": "162",
                "message": "amount must be greater than 0",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        else:
            get_total_amount = get_total_amount

        # disburse_type = "PAYROLL" if payout_choice == "ACCOUNT" else "PERSONAL_PAYROLL"
        if payout_choice == "BUDDY":
            CONST = ConstantTable.get_constant_instance()
            if CONST.send_money_via_buddy is False:
                response = {
                    "error": "02",
                    "message": "Service is currently not available",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            # buddy account details
            account_number = request_user.account_no
            account_name = request_user.account_name
            bank_name = request_user.bank
            bank_code = request_user.bank_code

            if not account_number or not account_name or not bank_name or not bank_code:
                response = {"error": "103", "message": "incomplete account details"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if bank_code != "999999":
                response = {"error": "104", "message": "invalid buddy transfer"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # Send Payout Buddy
            payout_task.apply_async(
                queue="payout_transfer",
                kwargs={
                    "user": request_user.id,
                    "payout_type": disburse_type,
                    "account_ins": account_ins.id,
                    "disburse_pin": disburse_pin,
                    "account_number": account_number,
                    "account_name": account_name,
                    "bank_name": bank_name,
                    "bank_code": bank_code,
                    "amount": amount,
                    "narration": narration,
                    "charge_fee": payout_charge,
                },
            )
            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": get_total_amount,
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)

        elif payout_choice == "ACCOUNT":
            CONST = ConstantTable.get_constant_instance()
            if CONST.send_money is False:
                response = {
                    "error": "02",
                    "message": "Service is currently not available",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            # get other account details

            account_number = serializer.validated_data.get("account_number")
            account_name = serializer.validated_data.get("account_name")
            bank_name = serializer.validated_data.get("bank_name")
            bank_code = serializer.validated_data.get("bank_code")

            if not account_number or not account_name or not bank_name or not bank_code:
                response = {"error": "103", "message": "incomplete account details"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # Payout Account
            payout_task.apply_async(
                queue="payout_transfer",
                kwargs={
                    "user": request_user.id,
                    "payout_type": disburse_type,
                    "account_ins": account_ins.id,
                    "disburse_pin": disburse_pin,
                    "account_number": account_number,
                    "account_name": account_name,
                    "bank_name": bank_name,
                    "bank_code": bank_code,
                    "amount": amount,
                    "narration": narration,
                    "charge_fee": payout_charge,
                },
            )
            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": get_total_amount,
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)

        else:
            response = {
                "message": "failed",
                "data": {
                    "message": "Transaction could not complete",
                    "amount_sent": 0,
                    "processed_transaction_count": 0,
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class DisbursePayrollRecordAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        UserHasPin,
        PayrollDisbursePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        company_uuid = request.query_params.get("company_uuid")

        serializer = DisbursePayrollRecordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        payroll_pin = serializer.validated_data.get("payroll_pin")
        payroll_date = serializer.validated_data.get("payroll_date")
        narration = serializer.validated_data.get("narration")
        payroll_month = serializer.validated_data.get("payroll_month")
        payroll_year = serializer.validated_data.get("payroll_year")
        payroll_type = "RECORD"

        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=payroll_pin
        )
        if not check_pin:
            return Response(
                {"status": False, "message": "Payroll pin is incorrect!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            company_ins = Company.objects.get(id=company_uuid)
            run_payroll = PayrollTable.create_record_payroll(
                user=request.user,
                company_uuid=company_uuid,
                payroll_date=payroll_date,
                narration=narration,
                payroll_month=payroll_month,
                payroll_year=payroll_year,
            )
            if not run_payroll["status"]:
                return Response(run_payroll, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(run_payroll, status=status.HTTP_200_OK)


class InstantWageOverview(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CompanyPermission,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instant_wage_queryset = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, is_active=True
        )
        success_instant_wage_count = instant_wage_queryset.filter(
            employee_instant_wage_status="SUCCESSFUL"
        ).aggregate(Count("id"))["id__count"]

        pending_instant_wage_count = instant_wage_queryset.filter(
            employee_instant_wage_status="PENDING"
        ).aggregate(Count("id"))["id__count"]

        total_wage_paid = (
            instant_wage_queryset.filter(
                employee_instant_wage_status="SUCCESSFUL"
            ).aggregate(Sum("employee_instant_used_wage_amount"))[
                "employee_instant_used_wage_amount__sum"
            ]
            or 0
        )

        response = {
            "pending_request": pending_instant_wage_count,
            "successful_request": success_instant_wage_count,
            "total_wage_paid": total_wage_paid,
            "due_repayment": 0,
        }
        return Response(response, status=status.HTTP_200_OK)


class AnnouncementsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        today_date = timezone.now()
        announcement_range = today_date + timedelta(days=6)

        company_uuid = request.query_params.get("company_uuid")

        announcement_event_queryset = CompanyAnnouncement.objects.filter(
            company__id=company_uuid
        )
        announcement = announcement_event_queryset.filter(
            announcement_type="ANNOUNCEMENT",
            announcement_date__date__gte=today_date,
            announcement_date__date__lte=announcement_range,
        )
        announcement_serializer = CompanyAnnouncementSerializer(announcement, many=True)

        event = announcement_event_queryset.filter(
            announcement_type="EVENT",
            announcement_date__date__gte=today_date,
            announcement_date__date__lte=announcement_range,
        )
        event_serializer = CompanyAnnouncementSerializer(event, many=True)

        company_employees = CompanyEmployeeList.objects.filter(company__id=company_uuid)

        upcoming_birthday = company_employees.filter(
            employee_birth_date__gte=today_date,
            employee_birth_date__lte=announcement_range,
            is_birthday_enabled=True,
            is_active=True,
        )
        birthday_serializer = CompanyEmployeeListSerializer(
            upcoming_birthday, many=True
        )

        upcoming_work_anniversary = company_employees.filter(
            employee_start_date__gte=today_date,
            employee_start_date__lte=announcement_range,
            is_work_anniversary_enabled=True,
            is_active=True,
        )
        work_anniversary_serializer = CompanyEmployeeListSerializer(
            upcoming_work_anniversary, many=True
        )

        response = {
            "announcement": announcement_serializer.data,
            "event": event_serializer.data,
            "birthday": birthday_serializer.data,
            "work_anniversary": work_anniversary_serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)
    
class CreateAnnouncementsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")

        today_date = timezone.now()

        employee = CompanyEmployeeList.objects.filter(
            employee=request.user, company__id=company_uuid, is_deleted=False
        ).first()

        serializer = CompanyAnnouncementSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        announcement_type = serializer.validated_data.get("announcement_type")
        announcement_date = serializer.validated_data.get("announcement_date")
        announcement_title = serializer.validated_data.get("announcement_title")
        announcement_body = serializer.validated_data.get(
            "announcement_body"
        )

        if announcement_date < today_date:
            return Response(
                {
                    "error": "400",
                    "message": "Announcement date cannot be in the past",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        if announcement_type not in ["ANNOUNCEMENT", "EVENT"]:
            return Response(
                {
                    "error": "400",
                    "message": "Invalid announcement type. Must be either 'ANNOUNCEMENT' or 'EVENT'",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # Create the announcement
        announcement = CompanyAnnouncement.objects.create(
            company_id=company_uuid,
            announcement_type=announcement_type,
            announcement_date=announcement_date,
            announcement_title=announcement_title,
            announcement_body=announcement_body,
            created_by=employee,
        )
        response = {
            "message": "Event created successfully"
        }
        return Response(response, status=status.HTTP_200_OK)


class InstantWageWithdrawAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        CanInitiateTransfer,
        UserIsActive,
        EmployeeIsActive,
        UserHasPin,
        CompanyPermission,
        VerifyCompanyEmployeeSendMoney
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        company_uuid = request.query_params.get("company_uuid")
        request_user = request.user

        serializer = InstantWageWithdrawSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        withdraw_pin = serializer.validated_data.get("user_pin")
        withdraw_amount = serializer.validated_data.get("amount")
        withdraw_choice = serializer.validated_data.get("withdraw_choice")
        constant_table = ConstantTable.get_constant_instance()

        # Check Transaction Pin
        check_pin = User.check_sender_payroll_pin(
            user=request_user, pincode=withdraw_pin
        )
        if not check_pin:
            return Response(
                {
                    "error": "error",
                    "message": "Incorrect Pin",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if constant_table.send_money_via_buddy is False:
            response = {
                "error": "02",
                "message": "Service is currently not available",
            }
            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

        # check if user has account
        try:
            account_ins = AccountSystem.objects.get(
                user=request_user, account_type="INSTANT_WAGE"
            )
        except AccountSystem.DoesNotExist:
            return Response(
                response={
                    "error": "03",
                    "message": "Service is currently not available",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check if user has wallet

        wallet_ins = Wallet.get_wallet_instance(request_user, account_ins)
        if wallet_ins is None:
            response = {
                "error": "04",
                "message": "Service is currently not available",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid,
            employee=request_user,
            is_active=True,
            is_deleted=False,
        ).first()
        if not employee_ins:
            return Response(
                {
                    "error": "404",
                    "message": "User is not an employee of this company",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if employee_ins.employee_instant_wage_status != "SUCCESSFUL":
            return Response(
                {
                    "error": "905",
                    "message": "Instant wage has not been approved!",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not employee_ins.employee_instant_daily_wage:
            return Response(
                {
                    "error": "904",
                    "message": "Instant wage is currently not available",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        const_table_limit = (
            ConstantTable.get_constant_instance().instant_wage_withdrawal_percentage
        )
        try:
            get_withdrawal_percentage_limit = round_amount(
                (const_table_limit / 100)
                * employee_ins.employee_instant_net_available_wage
            )
        except ZeroDivisionError:
            return Response(
                {
                    "error": "904",
                    "message": "Instant wage cannot be accessed during this time",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        if (
            withdraw_amount + employee_ins.employee_instant_used_wage_amount
        ) > get_withdrawal_percentage_limit:

            return Response(
                {
                    "error": "905",
                    "message": f"You cannot withdraw more than {const_table_limit}% of your instant wage",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # get wallet balance
        instant_wage_balance = employee_ins.employee_instant_net_available_wage
        # print(employee_ins.employee_instant_net_available_wage, "BEFORE DEDUCTION")
        # wallet_balance = wallet_ins.balance
        # get pay amount
        get_total_amount = 0
        amount = withdraw_amount

        # get charges for payout
        payout_charge = (
            constant_table.instant_wage_withdrawal_fee
        )

        # get total payout amount with charges
        get_total_charged_amount = amount + payout_charge
        get_total_amount = get_total_charged_amount

        # check wallet balance
        if instant_wage_balance < get_total_amount:
            response = {
                "error": "161",
                "message": "You do not have sufficient available instant wage to make this transaction",
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        if get_total_amount <= 0.0:
            response = {
                "error": "162",
                "message": "amount must be greater than 0",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        else:
            get_total_amount = get_total_amount

        # disburse_type = "PAYROLL" if payout_choice == "ACCOUNT" else "PERSONAL_PAYROLL"

        remaining_balance = instant_wage_balance - get_total_amount
        employee_ins.employee_instant_net_available_wage = round_amount(
            remaining_balance
        )
        employee_ins.save()

        # today_date = timezone.now().date()

        # sender
        instant_wage_account_id = settings.INSTANT_WAGE_ACCOUNT_ID
        instant_wage_user_id = settings.INSTANT_WAGE_USER_ID

        instant_wage_user_ins = User.objects.get(id=instant_wage_user_id)
        instant_wage_account_inst = AccountSystem.objects.get(
            id=instant_wage_account_id
        )

        # receiver

        receiver_company = employee_ins.company
        receiver_employer_ins = employee_ins.employer

        payroll_type = "INSTANT_WAGE"

        instant_wage_payment = {
            "receiver": request_user.email,
            "receiver_company": receiver_company.company_name,
            "receiver_employer": receiver_employer_ins.email,
            "amount": get_total_amount,
        }

        # print(instant_wage_payment, "INSTANT WAGE PAYMENT")
        # print(employee_ins.employee_instant_net_available_wage, "AFTER DEDUCTION")
        charge_fee = payout_charge
        # withdraw from PAYBOX
        send_money = Wallet.send_money_via_instant_wage(
            withdraw_amount,
            charge_fee,
            get_total_amount,
            instant_wage_user_ins,
            instant_wage_account_inst,
            receiver_employer_ins,
            receiver_company,
            payroll_type,
            request_user,
        )
        instant_wage_payment.update(send_money)

        if "status_code" in send_money:
            if send_money["status_code"] == "00":

                used_wage_amount = (
                    employee_ins.employee_instant_used_wage_amount + get_total_amount
                )
                net_available_wage = (
                    employee_ins.employee_instant_daily_collected_wage_amount
                    - used_wage_amount
                )

                employee_ins.employee_instant_net_available_wage = net_available_wage
                employee_ins.employee_instant_used_wage_amount = used_wage_amount
                employee_ins.save()

                response = {
                    "message": "success",
                    "data": {
                        "message": "Transaction completed successfully",
                        "amount_sent": withdraw_amount,
                        "data": "instant_wage_payment",
                    },
                    "date_completed": datetime.now(),
                }
                return Response(response, status=status.HTTP_202_ACCEPTED)
            else:
                new_instant_wage = employee_ins.employee_instant_net_available_wage
                refund_balance = new_instant_wage + get_total_amount
                employee_ins.employee_instant_net_available_wage = round_amount(
                    refund_balance
                )
                employee_ins.save()

                # print(employee_ins.employee_instant_net_available_wage, "AFTER REFUND 1")
                response = {
                    "message": "failed",
                    "data": {
                        "message": "Transaction failed",
                        "amount_sent": 0,
                        "data": "instant_wage_payment",
                    },
                    "date_completed": datetime.now(),
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:
            new_instant_wage = employee_ins.employee_instant_net_available_wage
            refund_balance = new_instant_wage + get_total_amount
            employee_ins.employee_instant_net_available_wage = round_amount(
                refund_balance
            )
            employee_ins.save()
            # print(employee_ins.employee_instant_net_available_wage, "AFTER REFUND 2")
            response = {
                "message": "failed",
                "data": {
                    "message": "Transaction failed",
                    "amount_sent": 0,
                    "data": "instant_wage_payment",
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class EmployeeInstantWageDashboardAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CompanyPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=request.user, is_active=True
        ).first()
        if employee:
            net_available = employee.employee_instant_net_available_wage
            collected_wage = employee.employee_instant_daily_collected_wage_amount
            used_wage = employee.employee_instant_used_wage_amount
            remaining_wage_amount = (
                employee.employee_instant_daily_remaining_wage_amount
            )
            total_accessible_wage = employee.set_instant_wage_amount
            if employee.custom_daily_wage:
                daily_wage_amount = employee.custom_daily_wage_amount
                wage_percentage = employee.custom_instant_wage_percentage

            else:
                daily_wage_amount = employee.employee_instant_daily_wage_amount
                wage_percentage = 0
        else:
            net_available = 0
            collected_wage = 0
            used_wage = 0
            daily_wage_amount = 0
            remaining_wage_amount = 0
            wage_percentage = 0
            total_accessible_wage = 0

        const_table_limit = (
            ConstantTable.get_constant_instance().instant_wage_withdrawal_percentage
        )
        try:
            get_withdrawal_percentage_limit = (
                const_table_limit / 100
            ) * employee.employee_instant_net_available_wage
        except ZeroDivisionError:
            get_withdrawal_percentage_limit = 0

        try:
            account_ins = AccountSystem.objects.get(
                user=request.user, account_type="INSTANT_WAGE"
            )
            wallet_balance = Wallet.get_wallet_balance(request.user, account_ins)
        except AccountSystem.DoesNotExist:
            wallet_balance = 0

        data = {
            "wallet_balance": wallet_balance,
            "net_available_wage": net_available,
            "used_wage": used_wage,
            "collected_wage": collected_wage,
            "daily_wage_amount": daily_wage_amount,
            "remaining_wage_amount": remaining_wage_amount,
            "wage_percentage": wage_percentage,
            "eligible_wage_amount": total_accessible_wage,
            "withdrawal_limit": get_withdrawal_percentage_limit,
            "withdrawal_limit_percent": const_table_limit,
            "loan_balance": 0,
        }
        return Response(data, status=status.HTTP_200_OK)


class TurnOnOffInstantWageForCompanyEmployee(APIView):
    permission_classes = [IsAuthenticated, IsStaff]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        all_company_employees = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, is_active=True
        )

        serializer = TurnOnOffInstantWageForCompanyEmployeeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        toggle_type = serializer.validated_data.get("toggle_type")
        if toggle_type == "ON":
            for company_employee in all_company_employees:
                set_instant_wage_amount = round_amount(
                    company_employee.employee_payable_amount
                )
                company_employee.employee_instant_daily_wage = True
                company_employee.set_instant_wage_amount = set_instant_wage_amount
                company_employee.employee_instant_wage_status = "OFF"
                company_employee.employee_instant_daily_collected_wage_amount = 0
                company_employee.employee_instant_daily_remaining_wage_amount = 0
                company_employee.employee_instant_daily_wage_amount = 0
                # company_employee.employee_instant_used_wage_amount = 0
                company_employee.employee_instant_net_available_wage = 0
                company_employee.employee_instant_wage_completed = False
                company_employee.custom_daily_wage = False
                company_employee.custom_daily_wage_amount = 0
                company_employee.custom_instant_wage_percentage = 0
                company_employee.save()

            return Response(
                {"message": "instant wage turn on for all employees"},
                status=status.HTTP_200_OK,
            )

        elif toggle_type == "OFF":
            for company_employee in all_company_employees:
                company_employee.employee_instant_daily_wage = False
                company_employee.save()
            return Response(
                {"message": "instant wage turn off for all employees"},
                status=status.HTTP_200_OK,
            )

        elif toggle_type == "RESUME":
            for company_employee in all_company_employees:
                company_employee.employee_instant_daily_wage = True
                company_employee.save()

            return Response(
                {"message": "instant wage resumed for all employees"},
                status=status.HTTP_200_OK,
            )

        elif toggle_type == "RESET":
            for company_employee in all_company_employees:
                company_employee.employee_instant_daily_wage = False
                company_employee.employee_instant_wage_status = "OFF"
                company_employee.employee_instant_daily_collected_wage_amount = 0
                company_employee.employee_instant_daily_remaining_wage_amount = 0
                company_employee.set_instant_wage_amount = 0
                company_employee.employee_instant_daily_wage_amount = 0
                # company_employee.employee_instant_used_wage_amount = 0
                company_employee.employee_instant_net_available_wage = 0
                company_employee.employee_instant_wage_completed = False
                company_employee.custom_daily_wage = False
                company_employee.custom_daily_wage_amount = 0
                company_employee.custom_instant_wage_percentage = 0
                company_employee.save()

            return Response(
                {"message": "instant wage reset for all employees"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "invalid toggle type"}, status=status.HTTP_400_BAD_REQUEST
            )


class CreateEditDelBeneficiary(APIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):

        all_beneficiary = Beneficiary.objects.filter(
            user=request.user, is_deleted=False
        ).order_by("-id")
        beneficiary_list = AllBeneficiarySerializer(all_beneficiary, many=True)
        response = {"beneficiaries": beneficiary_list.data}
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):

        serializer = CreateBeneficiarySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        all_beneficiary = serializer.validated_data.get("beneficiary_data")
        disburse_pin = serializer.validated_data.get("disburse_pin")

        # Check Transaction Pin
        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=disburse_pin
        )
        if not check_pin:
            return Response(
                {
                    "error": "error",
                    "message": "Incorrect Pin",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        beneficiary = Beneficiary.create_single_multiple_beneficiary(
            request.user, all_beneficiary
        )

        return Response(beneficiary, status=status.HTTP_200_OK)

    def put(self, request):
        beneficiary_id = request.query_params.get("beneficiary_id")

        instance = Beneficiary.objects.filter(
            id=beneficiary_id, user=request.user, is_deleted=False
        ).last()
        if instance:
            serializer = AllBeneficiarySerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(
                {"message": "beneficiary updated successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": "beneficiary not found"},status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        beneficiary_id = request.query_params.get("beneficiary_id")

        instance = Beneficiary.objects.filter(
            id=beneficiary_id, user=request.user, is_deleted=False
        ).last()
        if instance:
            instance.is_deleted = True
            instance.save()
            return Response(
                {"message": "beneficiary deleted successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": "beneficiary not found"}, status=status.HTTP_400_BAD_REQUEST)


class OneClickSendMoneyAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        CanInitiateTransfer,
        UserIsActive,
        UserHasPin,
        VerifyCompanyEmployeeSendMoney
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = CreateBeneficiarySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        all_beneficiary = serializer.validated_data.get("beneficiary_data")
        disburse_pin = serializer.validated_data.get("disburse_pin")

        # Check Transaction Pin
        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=disburse_pin
        )
        if not check_pin:
            return Response(
                {
                    "error": "error",
                    "message": "Incorrect Pin",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        CONST = ConstantTable.get_constant_instance()
        if CONST.send_money is False:
            response = {
                "error": "02",
                "message": "Service is currently not available",
            }
            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

        # get account and wallet instance of sender
        try:
            account_ins = AccountSystem.objects.get(
                user=request.user, account_type="PERSONAL_PAYROLL"
            )
        except AccountSystem.DoesNotExist:
            return Response(
                {
                    "error": "170",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        wallet_ins = Wallet.get_wallet_instance(request.user, account_ins)
        if wallet_ins is None:
            return Response(
                {
                    "error": "171",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        wallet_balance = wallet_ins.balance
        charge_fee = CONST.one_click_charge_fee + 50
        one_click_amount = 0
        get_total_amount = 0
        for beneficiary in all_beneficiary:
            # account_number = beneficiary["account_number"]
            # account_name = beneficiary["account_name"]
            # bank_code = beneficiary["bank_code"]
            # narration = beneficiary["narration"]
            amount = beneficiary["amount"]
            get_total_charged_amount = amount + charge_fee
            get_total_amount += get_total_charged_amount
            one_click_amount += amount

            if wallet_balance < get_total_amount:
                response = {
                    "error": "161",
                    "message": "You do not have sufficient balance to make this transaction",
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            if get_total_amount <= 0.0:
                response = {
                    "error": "162",
                    "message": "amount must be greater than 0",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # one click send money
        one_click_type = "PERSONAL_PAYROLL"
        for beneficiary in all_beneficiary:
            account_number = beneficiary["account_number"]
            account_name = beneficiary["account_name"]
            bank_code = beneficiary["bank_code"]
            narration = beneficiary["narration"]
            amount = beneficiary["amount"]
            bank_name = beneficiary["bank_name"]

            transaction_ins = OneClickTransaction.objects.create(
                user=request.user,
                account_number=account_number,
                account_name=account_name,
                bank_code=bank_code,
                bank_name=bank_name,
                narration=narration,
                amount=amount,
                status="PENDING",
            )
            send_money_one_click_account_task.apply_async(
                queue="sendoneclick",
                kwargs={
                    "user": request.user.id,
                    "one_click_type": one_click_type,
                    "account_ins": account_ins.id,
                    "disburse_pin": disburse_pin,
                    "account_number": account_number,
                    "account_name": account_name,
                    "bank_name": bank_name,
                    "bank_code": bank_code,
                    "amount": amount,
                    "narration": narration,
                    "charge_fee": CONST.one_click_charge_fee,
                    "one_click_id": transaction_ins.transaction_id,
                },
            )
        response = {
            "message": "success",
            "data": {
                "message": "Transaction completed successfully",
                "amount_sent": one_click_amount,
                "processed_transaction_count": len(all_beneficiary),
            },
            "date_completed": datetime.now(),
        }
        return Response(response, status=status.HTTP_202_ACCEPTED)


class OneClickHistoryAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):

        page = request.GET.get("page", 1)
        one_click_history_qs = (
            OneClickTransaction.objects.filter(user=request.user)
        ).order_by("-id")
        one_click_data = WalletHistoryPaginator.paginate(
            request, one_click_history_qs, page
        )
        serializer = OneClickHistorySerializer(one_click_data, many=True)
        try:
            data = {
                "data_type": "ONE_CLICK_DATA",
                "data": serializer.data,
                "total_page": one_click_data.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": one_click_data.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "ONE_CLICK_DATA",
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class FixALLPayrollDate(APIView):
    permission_classes = [IsAuthenticated, IsStaff]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        all_bulk_ids = CompanyDetailsData.objects.all()
        month_map = {
            "january": 1,
            "february": 2,
            "march": 3,
            "april": 4,
            "may": 5,
            "june": 6,
            "july": 7,
            "august": 8,
            "september": 9,
            "october": 10,
            "november": 11,
            "december": 12,
        }

        if all_bulk_ids:
            for bulk_id in all_bulk_ids:
                current_payroll_date = (
                    f"{bulk_id.payroll_year}-{month_map[bulk_id.payroll_month]}-03"
                )
                all_payroll = PayrollTable.objects.filter(bulk_id=bulk_id.bulk_id)
                if all_payroll:
                    for payroll in all_payroll:
                        payroll.payroll_date = current_payroll_date
                        payroll.save()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class PayrollSettingsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company = Company.objects.get(id=company_uuid)
        CompanyPayrollSettings.create_company_payroll_settings(company=company)
        instance = CompanyPayrollSettings.objects.filter(
            company__id=company_uuid
        ).last()
        if instance:
            serializer = CompanyPayrollSettingSerializer(instance)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "payroll settings unavailable", "data": []}
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        
class PensionSettingsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company = Company.objects.get(id=company_uuid)
        CompanyPayrollSettings.create_company_payroll_settings(company=company)
        instance = CompanyPayrollSettings.objects.filter(
            company__id=company_uuid
        ).last()
        if instance:
            serializer = CompanyPensionSettingSerializer(instance)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "payroll settings unavailable", "data": []}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class EditPayrollSettingsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanEditPayrollSettings,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyPayrollSettings.objects.filter(
            company__id=company_uuid
        ).last()
        if instance:
            serializer = CompanyPayrollSettingSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            response = {
                "message": "payroll settings updated successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "payroll settings unavailable", "data": []}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = CompanyPayrollSettings.objects.filter(
            company__id=company_uuid
        ).last()
        if instance:
            serializer = AddPayrollSettingsLogoSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            company_logo = serializer.validated_data.get("company_logo")
            uploaded_file = upload_file_aws_s3_bucket(
                model_instance_id=instance.id,
                file=company_logo,
                model_name="PayrollSettings",
            )
            response = {
                "status": "Success",
                "message": "company logo updated successfully",
                "file_url": uploaded_file if uploaded_file else "",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "payroll settings unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

class EditPensionSettingsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanEditPayrollSettings,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyPayrollSettings.objects.filter(
            company__id=company_uuid
        ).last()
        if instance:
            serializer = CompanyPensionSettingSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            response = {
                "message": "pension settings updated successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pension settings unavailable", "data": []}
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        
class PayGroupAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = (
            CompanyPayGroupSettings.objects.values(
                "id", "pay_group_name", "pay_group_description"
            )
            .filter(company__id=company_uuid, is_deleted=False)
            .order_by("-created_at")
        )
        if instance:
            serializer = AllPayGroupSerializer(instance, many=True)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pay group unavailable", "data": []}
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = MultiplePayGroupSerializer(
            data=request.data, context={"company_uuid": company_uuid}
        )

        serializer.is_valid(raise_exception=True)
        pay_group_data = serializer.validated_data.get("pay_group_data")
        company_ins = Company.objects.get(id=company_uuid)
        for paygroup in pay_group_data:
            pay_group_name = paygroup.get("pay_group_name")
            pay_group_description = paygroup.get("pay_group_description")

            CompanyPayGroupSettings.objects.create(
                company=company_ins,
                employer=company_ins.user,
                pay_group_name=pay_group_name,
                pay_group_description=pay_group_description,
                added_by=request.user,
            )
        response = {"message": "pay group added successfully"}
        return Response(response, status=status.HTTP_200_OK)

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        pay_group_id = request.query_params.get("pay_group_id")
        if not valid_uuid_check(pay_group_id):
            return Response(
                {"message": "invalid pay_group_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyPayGroupSettings.objects.filter(
            id=pay_group_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            serializer = AllPayGroupSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["added_by"] = request.user
            data = CompanyPayGroupSettings.objects.filter(
                company__id=company_uuid,
                pay_group_name=request.data.get("pay_group_name"),
                is_deleted=False,
            ).exists()
            if data:
                response = {
                    "message": "pay group already exist",
                    "data": serializer.data,
                }
                return Response(response, status=status.HTTP_200_OK)
            serializer.save()
            response = {
                "message": "pay group updated successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pay group unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        pay_group_id = request.query_params.get("pay_group_id")
        if not valid_uuid_check(pay_group_id):
            return Response(
                {"message": "invalid pay_group_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyPayGroupSettings.objects.filter(
            id=pay_group_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            instance.is_deleted = True
            instance.save()
            response = {
                "message": "pay group deleted successfully",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pay group unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class PayGradeAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = CompanyPayGradeSettings.objects.filter(
            company__id=company_uuid, is_deleted=False
        ).order_by("-created_at")
        if instance:
            serializer = AllPayGradeSerializer(instance, many=True)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pay grade unavailable", "data": []}
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        pay_group_id = request.query_params.get("pay_group_id")
        if not valid_uuid_check(pay_group_id):
            return Response(
                {"message": "invalid pay_group_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        pay_group_instance = CompanyPayGroupSettings.objects.filter(
            id=pay_group_id, company__id=company_uuid, is_deleted=False
        ).last()

        if pay_group_instance:
            serializer = PayGradeSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            pay_grade_name = serializer.validated_data.get("pay_grade_name")
            amount = serializer.validated_data.get("amount")
            pay_schedule = serializer.validated_data.get("pay_schedule")

            company_ins = Company.objects.get(id=company_uuid)

            pay_grade = CompanyPayGradeSettings.objects.filter(
                company=company_ins,
                pay_grade_name=pay_grade_name,
                pay_group=pay_group_instance,
                is_deleted=False,
            ).exists()
            if pay_grade:
                response = {"message": "pay grade already exist"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                CompanyPayGradeSettings.objects.create(
                    company=company_ins,
                    employer=company_ins.user,
                    pay_group=pay_group_instance,
                    amount=amount,
                    pay_grade_name=pay_grade_name,
                    pay_schedule=pay_schedule,
                    added_by=request.user,
                )
                response = {"message": "pay grade added successfully"}
                return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "invalid pay group id"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        pay_group_id = request.query_params.get("pay_group_id")
        pay_grade_id = request.query_params.get("pay_grade_id")
        if not valid_uuid_check(pay_group_id):
            return Response(
                {"message": "invalid pay_group_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        if not valid_uuid_check(pay_grade_id):
            return Response(
                {"message": "invalid pay_grade_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyPayGradeSettings.objects.filter(
            id=pay_grade_id,
            pay_group__id=pay_group_id,
            company__id=company_uuid,
            is_deleted=False,
        ).last()
        if instance:
            serializer = AllPayGradeSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["added_by"] = request.user
            data = CompanyPayGradeSettings.objects.filter(
                pay_group__id=pay_group_id,
                company__id=company_uuid,
                pay_grade_name=request.data.get("pay_grade_name"),
                is_deleted=False,
            ).exists()
            if data:
                response = {
                    "message": "pay grade already exist",
                    "data": serializer.data,
                }
                return Response(response, status=status.HTTP_200_OK)
            serializer.save()
            response = {
                "message": "pay group updated successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pay grade unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        pay_group_id = request.query_params.get("pay_group_id")
        pay_grade_id = request.query_params.get("pay_grade_id")
        if not valid_uuid_check(pay_group_id):
            return Response(
                {"message": "invalid pay_group_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        if not valid_uuid_check(pay_grade_id):
            return Response(
                {"message": "invalid pay_grade_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        instance = CompanyPayGradeSettings.objects.filter(
            id=pay_grade_id,
            pay_group__id=pay_group_id,
            company__id=company_uuid,
            is_deleted=False,
        ).last()
        if instance:
            instance.is_deleted = True
            instance.is_active = False
            instance.save()
            response = {
                "message": "pay grade deleted successfully",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pay grade unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class DeductionAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = OtherDeductionSettings.objects.filter(
            company__id=company_uuid, is_deleted=False
        ).order_by("-created_at")
        if instance:
            serializer = AllOtherDeductionSerializer(instance, many=True)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "other deductions unavailable", "data": []}
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = OtherDeductionSerializer(
            data=request.data,
            context={"company_uuid": company_uuid, "request_user": request.user},
        )
        serializer.is_valid(raise_exception=True)
        deduction_name = serializer.validated_data.get("deduction_name")
        return Response(
            {
                "message": f"{deduction_name} created successfully",
                "type": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        deduction_id = request.query_params.get("deduction_id")
        deduction_type = request.query_params.get("deduction_type")
        if not valid_uuid_check(deduction_id):
            return Response(
                {"message": "invalid deduction_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = OtherDeductionSettings.objects.filter(
            id=deduction_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            serializer = AllOtherDeductionSerializer(
                instance,
                data=request.data,
                partial=True,
                context={
                    "company_uuid": company_uuid,
                    "deduction_ins": instance,
                    "request_user": request.user,
                },
            )
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["added_by"] = request.user
            serializer.save()
            response = {
                "message": "deduction updated successfully",
                # "data": serializer.data
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "deduction unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        deduction_id = request.query_params.get("deduction_id")
        if not valid_uuid_check(deduction_id):
            return Response(
                {"message": "invalid deduction_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = OtherDeductionSettings.objects.filter(
            id=deduction_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            instance.is_deleted = True
            instance.is_active = False
            instance.save()
            response = {
                "message": "deduction deleted successfully",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "deduction unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class DepartmentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = (
            CompanyDepartmentSettings.objects.values("id", "department_name")
            .filter(company__id=company_uuid, is_deleted=False)
            .order_by("-created_at")
        )
        if instance:
            serializer = AllDepartmentSerializer(instance, many=True)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "department unavailable", "data": []}
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = MultipleDepartmentSerializer(
            data=request.data, context={"company_uuid": company_uuid}
        )
        company_ins = Company.objects.get(id=company_uuid)
        serializer.is_valid(raise_exception=True)

        department_data = serializer.validated_data.get("department_data")
        for department in department_data:
            department_name = department.get("department_name")
            department_head_name = department.get("department_head_name")
            this_department = CompanyDepartmentSettings.objects.create(
                company=company_ins,
                employer=company_ins.user,
                department_name=department_name,
                added_by=request.user,
            )
            if department_head_name:
                this_department.department_head_name = department_head_name
                this_department.save()
        response = {"message": "department added successfully"}
        return Response(response, status=status.HTTP_200_OK)

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        department_id = request.query_params.get("department_id")
        if not valid_uuid_check(department_id):
            return Response(
                {"message": "invalid department_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyDepartmentSettings.objects.filter(
            id=department_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            serializer = AllDepartmentSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["added_by"] = request.user
            data = CompanyDepartmentSettings.objects.filter(
                company__id=company_uuid,
                department_name=request.data.get("department_name"),
                is_deleted=False,
            ).exists()
            if data:
                response = {
                    "message": "department already exist",
                    "data": serializer.data,
                }
                return Response(response, status=status.HTTP_200_OK)
            serializer.save()
            response = {
                "message": "department updated successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "department unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        department_id = request.query_params.get("department_id")
        if not valid_uuid_check(department_id):
            return Response(
                {"message": "invalid department_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyDepartmentSettings.objects.filter(
            id=department_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            instance.is_deleted = True
            instance.is_active = False
            instance.save()
            response = {
                "message": "department deleted successfully",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "department unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class CreateDepartmentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanCreateDepartment,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = MultipleDepartmentSerializer(
            data=request.data, context={"company_uuid": company_uuid}
        )
        company_ins = Company.objects.get(id=company_uuid)
        serializer.is_valid(raise_exception=True)

        department_data = serializer.validated_data.get("department_data")
        for department in department_data:
            department_name = department.get("department_name")
            department_head_name = department.get("department_head_name")
            this_department = CompanyDepartmentSettings.objects.create(
                company=company_ins,
                employer=company_ins.user,
                department_name=department_name,
                added_by=request.user,
            )
            if department_head_name:
                this_department.department_head_name = department_head_name
                this_department.save()
        response = {"message": "department added successfully"}
        return Response(response, status=status.HTTP_200_OK)


class EditDepartmentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanEditDepartment,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        department_id = request.query_params.get("department_id")
        if not valid_uuid_check(department_id):
            return Response(
                {"message": "invalid department_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyDepartmentSettings.objects.filter(
            id=department_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            serializer = AllDepartmentSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["added_by"] = request.user
            data = CompanyDepartmentSettings.objects.filter(
                company__id=company_uuid,
                department_name=request.data.get("department_name"),
                is_deleted=False,
            ).exists()
            if data:
                response = {
                    "message": "department already exist",
                    "data": serializer.data,
                }
                return Response(response, status=status.HTTP_200_OK)
            serializer.save()
            response = {
                "message": "department updated successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "department unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class DeleteDepartmentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeleteDepartment,
    ]
    authentication_classes = [CustomUserAuthentication]

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        department_id = request.query_params.get("department_id")
        if not valid_uuid_check(department_id):
            return Response(
                {"message": "invalid department_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CompanyDepartmentSettings.objects.filter(
            id=department_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            instance.is_deleted = True
            instance.is_active = False
            instance.save()
            response = {
                "message": "department deleted successfully",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "department unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class SalaryComponentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        # instance = SalaryComponentSettings.objects.filter(
        #     company__id=company_uuid, is_deleted=False).order_by("-created_at")
        instance = (
            SalaryComponentSettings.objects.filter(
                company__id=company_uuid, is_deleted=False
            )
            .annotate(
                custom_order=Case(
                    When(salary_name="Basic", then=Value(1)),
                    When(salary_name="Housing", then=Value(2)),
                    When(salary_name="Transport", then=Value(3)),
                    default=Value(4),
                    output_field=CharField(),
                )
            )
            .order_by("custom_order", "-created_at")
        )
        if instance:
            serializer = AllSalaryComponentSerializer(instance, many=True)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "salary component unavailable", "data": []}
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = SalaryComponentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        salary_name = serializer.validated_data.get("salary_name")
        calculation_type = serializer.validated_data.get("calculation_type")
        amount = serializer.validated_data.get("amount")
        is_active = serializer.validated_data.get("is_active")
     
        company_ins = Company.objects.get(id=company_uuid)
        salary_component = SalaryComponentSettings.objects.filter(
            company__id=company_uuid,
            salary_name=salary_name,
            calculation_type=calculation_type,
            is_deleted=False,
        ).exists()
        if salary_component:
            response = {"message": "salary component already exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:
            SalaryComponentSettings.objects.create(
                company=company_ins,
                employer=company_ins.user,
                salary_name=salary_name,
                calculation_type=calculation_type,
                amount=amount,
                is_active=is_active,
                added_by=request.user,
            )
            response = {"message": "salary component added successfully"}
            return Response(response, status=status.HTTP_200_OK)

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        salary_component_id = request.query_params.get("salary_component_id")
        if not valid_uuid_check(salary_component_id):
            return Response(
                {"message": "invalid salary_component_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = SalaryComponentSettings.objects.filter(
            id=salary_component_id, company__id=company_uuid, is_deleted=False
        ).first()
        if instance:
            if instance.default_component:
                serializer = AllSalaryComponentSerializer(
                    instance, data=request.data, partial=True
                )
                serializer.is_valid(raise_exception=True)
                serializer.validated_data["added_by"] = request.user
                if "salary_name" in serializer.validated_data:
                    del serializer.validated_data["salary_name"]
                else:
                    pass
                if "calculation_type" in serializer.validated_data:
                    if request.data.get("calculation_type") == "PERCENTAGE_NET":
                        if "amount" in serializer.validated_data:
                            if request.data.get("amount") > 100:
                                response = {
                                    "message": "percentage cannot be greater than 100"
                                }
                                return Response(
                                    response, status=status.HTTP_400_BAD_REQUEST
                                )
                        else:
                            if instance.amount > 100:
                                response = {
                                    "message": "percentage cannot be greater than 100"
                                }
                                return Response(
                                    response, status=status.HTTP_400_BAD_REQUEST
                                )

                else:
                    if "amount" in serializer.validated_data:

                        if instance.calculation_type == "PERCENTAGE_NET":
                            if request.data.get("amount") > 100:
                                response = {
                                    "message": "percentage cannot be greater than 100"
                                }
                                return Response(
                                    response, status=status.HTTP_400_BAD_REQUEST
                                )

                serializer.save()
                response = {
                    "message": "salary component updated successfully",
                    "data": serializer.data,
                }
                return Response(response, status=status.HTTP_200_OK)
            else:
                serializer = AllSalaryComponentSerializer(
                    instance, data=request.data, partial=True
                )
                serializer.is_valid(raise_exception=True)
                serializer.validated_data["added_by"] = request.user
                # data = SalaryComponentSettings.objects.filter(
                #     company__id=company_uuid,
                #     salary_name=request.data.get("salary_name"),
                #     is_deleted=False,
                # ).exists()
                # if data:
                #     response = {
                #         "message": "salary component already exist",
                #         "data": serializer.data,
                #     }
                #     return Response(response, status=status.HTTP_400_BAD_REQUEST)
                serializer.save()
                response = {
                    "message": "salary component updated successfully",
                    "data": serializer.data,
                }
                return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "salary component unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        salary_component_id = request.query_params.get("salary_component_id")
        if not valid_uuid_check(salary_component_id):
            return Response(
                {"message": "invalid salary_component_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = SalaryComponentSettings.objects.filter(
            id=salary_component_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            if not instance.default_component:
                instance.is_deleted = True
                instance.is_active = False
                instance.save()
                response = {
                    "message": "salary component deleted successfully",
                }
                return Response(response, status=status.HTTP_200_OK)
            else:
                response = {
                    "message": "default salary component cannot be deleted",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:
            response = {"message": "salary component unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class CustomComponentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = CustomComponentSettings.objects.filter(
            company__id=company_uuid, is_deleted=False
        ).order_by("-created_at")
        if instance:
            serializer = AllCustomComponentSerializer(instance, many=True)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "custom component unavailable", "data": []}
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = CustomComponentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        custom_component_name = serializer.validated_data.get("custom_component_name")
        calculation_type = serializer.validated_data.get("calculation_type")
        amount = serializer.validated_data.get("amount")
        custom_name = serializer.validated_data.get("custom_name")
        frequency = serializer.validated_data.get("frequency")
        is_active = serializer.validated_data.get("is_active")
        part_of_employee_structure = serializer.validated_data.get(
            "part_of_employee_structure"
        )
        calculate_pay_schedule_basis = serializer.validated_data.get(
            "calculate_pay_schedule_basis"
        )
        show_on_payslip = serializer.validated_data.get("show_on_payslip")
        company_ins = Company.objects.get(id=company_uuid)
        salary_component = CustomComponentSettings.objects.filter(
            company__id=company_uuid,
            custom_component_name=custom_component_name,
            calculation_type=calculation_type,
            is_deleted=False,
        ).exists()
        if salary_component:
            response = {"message": "custom component already exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:
            dependency = []
            for custom_id in custom_name:
                if not valid_uuid_check(custom_id):
                    return Response(
                        {"message": "invalid custom_name_id"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                component = SalaryComponentSettings.objects.filter(
                    id=custom_id, company__id=company_uuid, is_deleted=False
                ).last()
                if component:
                    dependency.append(component)
                else:
                    return Response(
                        {"message": "custom component unavailable"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

            if calculation_type == "CUSTOM_PERCENTAGE":
                if dependency == []:
                    response = {"message": "custom name cannot be empty"}
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            add_one_off = CustomComponentSettings.objects.create(
                company=company_ins,
                employer=company_ins.user,
                custom_component_name=custom_component_name,
                calculation_type=calculation_type,
                amount=amount,
                frequency=frequency,
                is_active=is_active,
                part_of_employee_structure=part_of_employee_structure,
                calculate_pay_schedule_basis=calculate_pay_schedule_basis,
                show_on_payslip=show_on_payslip,
                added_by=request.user,
            )
            add_one_off.custom_name.add(*custom_name)
            response = {"message": "custom component added successfully"}
            return Response(response, status=status.HTTP_200_OK)

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        custom_component_id = request.query_params.get("custom_component_id")
        if not valid_uuid_check(custom_component_id):
            return Response(
                {"message": "invalid custom_component_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CustomComponentSettings.objects.filter(
            id=custom_component_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            serializer = AllCustomComponentSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["added_by"] = request.user
            data = CustomComponentSettings.objects.filter(
                company__id=company_uuid,
                custom_component_name=request.data.get("custom_component_name"),
                is_deleted=False,
            ).exists()
            if data:
                response = {
                    "message": "custom component already exist",
                    "data": serializer.data,
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            dependency = []
            if "calculation_type" in serializer.validated_data:
                if request.data.get("calculation_type") == "PERCENTAGE_NET":
                    if "amount" in serializer.validated_data:
                        if request.data.get("amount") > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                    else:
                        if instance.amount > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )

                elif request.data.get("calculation_type") == "CUSTOM_PERCENTAGE":

                    if "amount" in serializer.validated_data:
                        if request.data.get("amount") > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                    else:
                        if instance.amount > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )

                    if "custom_name" in serializer.validated_data:

                        for custom_id in request.data.get("custom_name"):
                            if not valid_uuid_check(custom_id):
                                return Response(
                                    {"message": "invalid custom_name_id"},
                                    status=status.HTTP_400_BAD_REQUEST,
                                )
                            component = SalaryComponentSettings.objects.filter(
                                id=custom_id, company__id=company_uuid, is_deleted=False
                            ).last()
                            if component:
                                dependency.append(component)
                            else:
                                return Response(
                                    {"message": "unavailable custom_name(s)"},
                                    status=status.HTTP_404_NOT_FOUND,
                                )

                        if dependency == []:
                            response = {"message": "custom name cannot be empty"}
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                        instance.custom_name.add(*dependency)
            else:
                if "amount" in serializer.validated_data:
                    if instance.calculation_type == "PERCENTAGE_NET":
                        if request.data.get("amount") > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                    elif instance.calculation_type == "CUSTOM_PERCENTAGE":
                        if request.data.get("amount") > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                else:
                    all_custom = [
                        name
                        for name in instance.custom_name.all().values_list(
                            "salary_name", flat=True
                        )
                    ]
                    if all_custom == []:
                        response = {"message": "custom names cannot empty"}
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if "custom_name" in serializer.validated_data:

                for custom_id in request.data.get("custom_name"):
                    if not valid_uuid_check(custom_id):
                        return Response(
                            {"message": "invalid custom_name_id"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                    component = SalaryComponentSettings.objects.filter(
                        id=custom_id, company__id=company_uuid, is_deleted=False
                    ).last()
                    if component:
                        dependency.append(component)
                    else:
                        return Response(
                            {"message": "unavailable custom_name(s)"},
                            status=status.HTTP_404_NOT_FOUND,
                        )

                if dependency == []:
                    response = {"message": "custom name cannot be empty"}
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                instance.custom_name.add(*dependency)

            serializer.save()

            response = {
                "message": "custom component updated successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "custom component unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        custom_component_id = request.query_params.get("custom_component_id")
        if not valid_uuid_check(custom_component_id):
            return Response(
                {"message": "invalid custom_component_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = CustomComponentSettings.objects.filter(
            id=custom_component_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            instance.is_deleted = True
            instance.is_active = False
            instance.save()
            response = {
                "message": "custom component deleted successfully",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "custom component unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class BenefitComponentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = BenefitComponentSettings.objects.filter(
            company__id=company_uuid, is_deleted=False
        ).order_by("-created_at")
        if instance:
            serializer = AllBenefitComponentSerializer(instance, many=True)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "benefit component unavailable", "data": []}
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = BenefitComponentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        benefit_name = serializer.validated_data.get("benefit_name")
        calculation_type = serializer.validated_data.get("calculation_type")
        frequency = serializer.validated_data.get("frequency")
        amount = serializer.validated_data.get("amount")
        custom_name = serializer.validated_data.get("custom_name")
        is_active = serializer.validated_data.get("is_active")
        company_ins = Company.objects.get(id=company_uuid)
        salary_component = BenefitComponentSettings.objects.filter(
            company__id=company_uuid,
            benefit_name=benefit_name,
            calculation_type=calculation_type,
            is_deleted=False,
        ).exists()
        if salary_component:
            response = {"message": "benefit component already exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:
            dependency = []
            for custom_id in custom_name:
                component = SalaryComponentSettings.objects.filter(
                    id=custom_id, company__id=company_uuid, is_deleted=False
                ).last()
                if component:
                    dependency.append(component)
                else:
                    return Response(
                        {"message": "salary component unavailable"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

            if calculation_type == "CUSTOM_PERCENTAGE":
                if dependency == []:
                    response = {"message": "custom name cannot be empty"}
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            add_benefit = BenefitComponentSettings.objects.create(
                company=company_ins,
                employer=company_ins.user,
                benefit_name=benefit_name,
                calculation_type=calculation_type,
                frequency=frequency,
                amount=amount,
                is_active=is_active,
                added_by=request.user,
            )
            add_benefit.custom_name.add(*custom_name)
            response = {"message": "benefit component added successfully"}
            return Response(response, status=status.HTTP_200_OK)

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        benefit_component_id = request.query_params.get("benefit_component_id")
        # company_ins = Company.objects.get(id=company_uuid)
        if not valid_uuid_check(benefit_component_id):
            return Response(
                {"message": "invalid benefit_component_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        instance = BenefitComponentSettings.objects.filter(
            id=benefit_component_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            serializer = AllBenefitComponentSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["added_by"] = request.user
            # data = BenefitComponentSettings.objects.filter(
            #     company__id=company_uuid,
            #     benefit_name=request.data.get("benefit_name"),
            #     is_deleted=False,
            # ).exists()
            # if data:
            #     response = {
            #         "message": "benefit component already exist",
            #         "data": serializer.data,
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            dependency = []
            if "calculation_type" in serializer.validated_data:
                if request.data.get("calculation_type") == "PERCENTAGE_NET":
                    if "amount" in serializer.validated_data:
                        if request.data.get("amount") > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                    else:
                        if instance.amount > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )

                elif request.data.get("calculation_type") == "CUSTOM_PERCENTAGE":

                    if "amount" in serializer.validated_data:
                        if request.data.get("amount") > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                    else:
                        if instance.amount > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )

                    if "custom_name" in serializer.validated_data:

                        for custom_id in request.data.get("custom_name"):
                            if not valid_uuid_check(custom_id):
                                return Response(
                                    {"message": "invalid custom_name_id"},
                                    status=status.HTTP_400_BAD_REQUEST,
                                )
                            component = SalaryComponentSettings.objects.filter(
                                id=custom_id, company__id=company_uuid, is_deleted=False
                            ).last()
                            if component:
                                dependency.append(component)
                            else:
                                return Response(
                                    {"message": "unavailable custom_name(s)"},
                                    status=status.HTTP_404_NOT_FOUND,
                                )

                        if dependency == []:
                            response = {"message": "custom name cannot be empty"}
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                        instance.custom_name.add(*dependency)
            else:
                if "amount" in serializer.validated_data:
                    if instance.calculation_type == "PERCENTAGE_NET":
                        if request.data.get("amount") > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                    elif instance.calculation_type == "CUSTOM_PERCENTAGE":
                        if request.data.get("amount") > 100:
                            response = {
                                "message": "percentage cannot be greater than 100"
                            }
                            return Response(
                                response, status=status.HTTP_400_BAD_REQUEST
                            )
                else:
                    all_custom = [
                        name
                        for name in instance.custom_name.all().values_list(
                            "salary_name", flat=True
                        )
                    ]
                    if all_custom == []:
                        response = {"message": "custom names cannot empty"}
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if "custom_name" in serializer.validated_data:

                for custom_id in request.data.get("custom_name"):
                    if not valid_uuid_check(custom_id):
                        return Response(
                            {"message": "invalid custom_name_id"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                    component = SalaryComponentSettings.objects.filter(
                        id=custom_id, company__id=company_uuid, is_deleted=False
                    ).last()
                    if component:
                        dependency.append(component)
                    else:
                        return Response(
                            {"message": "unavailable custom_name(s)"},
                            status=status.HTTP_404_NOT_FOUND,
                        )

                if dependency == []:
                    response = {"message": "custom name cannot be empty"}
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                instance.custom_name.add(*dependency)

            serializer.save()

            response = {
                "message": "benefit component updated successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "benefit component unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        benefit_component_id = request.query_params.get("benefit_component_id")
        if not valid_uuid_check(benefit_component_id):
            return Response(
                {"message": "invalid benefit_component_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # company_ins = Company.objects.get(id=company_uuid)
        instance = BenefitComponentSettings.objects.filter(
            id=benefit_component_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            instance.is_deleted = True
            instance.is_active = False
            instance.save()
            response = {
                "message": "benefit component deleted successfully",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "benefit component unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class CompanyReportSheet(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company = Company.objects.get(id=company_uuid)
        CompanyPayrollSettings.create_company_payroll_settings(company=company)

        filtered_payroll_data = CompanyEmployeeList.objects.filter(company__id=company_uuid, is_active=True)
        custom_tax_band = CompanyTaxBand.objects.filter(company=company, is_active=True).first()
        payroll_settings_ins = CompanyPayrollSettings.objects.filter(company=company).first()
        if payroll_settings_ins:
            if payroll_settings_ins.standard_tax:
                company_tax = "STANDARD_TAX"
            else:
                if custom_tax_band:
                    company_tax = "CUSTOM_TAX"
                else:
                    company_tax = "OFF"
        else:
            if custom_tax_band:
                company_tax = "CUSTOM_TAX"
            else:
                company_tax = "OFF"

        payroll_check_status, pay_settings_check = enable_payroll_settings_check(company=company, employee_data=filtered_payroll_data, company_tax=company_tax)
        if not payroll_check_status:
            return Response({
                "message": pay_settings_check,
                "report_url": pay_settings_check
            }, status=status.HTTP_400_BAD_REQUEST)
        
        get_report = company_report(company_uuid, company_tax, pay_settings_check)
        return Response(
            {"message": "successful", "report_url": get_report},
            status=status.HTTP_200_OK,
        )


class EmployeeOnboardingAPIView(APIView):
    # permission_classes = [IsAuthenticated, UserIsActive, AdminPermission]
    # authentication_classes = [CustomUserAuthentication]
    def put(self, request):
        invite_id = request.query_params.get("invite_id")
        if not valid_uuid_check(invite_id):

            return Response({"message":"invalid invite_id"}, status=status.HTTP_400_BAD_REQUEST)
        invite_ins = CompanyEmployeeOnboardingForm.objects.filter(invite_id=invite_id, is_deleted=False).last()

        if invite_ins:
            if invite_ins.is_completed:
                return Response(
                    {"message": "form already completed"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            else:
                serializer = EmployeeOnboardingSerializer(
                    invite_ins, data=request.data, partial=True
                )
                serializer.is_valid(raise_exception=True)
                serializer.save()

                invite_ins.is_completed = True
                invite_ins.approval_status = "AWAITING_APPROVAL"
                invite_ins.save()
                return Response(
                    {"message": "submitted successfully"}, status=status.HTTP_200_OK
                )
        else:
            return Response(
                {"message": "invalid invite"}, status=status.HTTP_400_BAD_REQUEST
            )


class EmployeeFamilyContactAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            serializer = EmployeeFamilyContactSerializer(employee)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            serializer = EditEmployeeFamilyContactSerializer(
                employee, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(
                {"message": "family contact(s) updated successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class EmployeeAccountDetailsAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee_ins:
            account_ins = CompanyEmployeeAccountDetails.objects.filter(
                company__id=company_uuid, employee=employee_ins.employee, is_active=True
            )
            serializer = EmployeeAccountSerializer(account_ins, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user

        serializer = CreateEmployeeAccountSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        bank_name = serializer.validated_data.get("bank_name")
        account_name = serializer.validated_data.get("account_name")
        account_number = serializer.validated_data.get("account_number")
        bvn_number = serializer.validated_data.get("bvn_number")
        bank_code = serializer.validated_data.get("bank_code")
        swift_code = serializer.validated_data.get("swift_code")
        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee_ins:
            account_ins = (
                CompanyEmployeeAccountDetails.objects.filter(
                    company=employee_ins.company,
                    employee=employee_ins.employee,
                    is_active=True,
                ).count()
                or 0
            )
            if account_ins >= 2:
                return Response(
                    {"error": "cannot create more than two accounts"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            else:

                CompanyEmployeeAccountDetails.objects.create(
                    company=employee_ins.company,
                    employee=employee_ins.employee,
                    bank_name=bank_name,
                    account_name=account_name,
                    account_number=account_number,
                    bvn_number=bvn_number,
                    bank_code=bank_code,
                    swift_code=swift_code,
                )

            return Response(
                {"message": "account created successfully"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        account_id = request.query_params.get("account_id")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            account_ins = CompanyEmployeeAccountDetails.objects.filter(
                company=employee.company, employee=employee.employee, id=account_id
            ).last()
            if account_ins:
                serializer = EditEmployeeAccountSerializer(
                    account_ins, data=request.data, partial=True
                )
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(
                    {"message": "account details updated successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "invalid account details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        account_id = request.query_params.get("account_id")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            account_ins = CompanyEmployeeAccountDetails.objects.filter(
                company=employee.company, employee=employee.employee, id=account_id
            ).last()
            if account_ins:
                account_ins.is_deleted = True
                account_ins.is_active = False
                account_ins.save()

                return Response(
                    {"message": "account deleted successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "invalid account details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class EmployeeEducationDetailsAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee_ins:
            education_ins = CompanyEmployeeEducationDetails.objects.filter(
                company__id=company_uuid,
                employee=employee_ins.employee,
                is_deleted=False,
            )
            serializer = EmployeeEducationSerializer(education_ins, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_ins = Company.objects.filter(id=company_uuid).last()
        employee_user = request.user

        serializer = EmployeeEducationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee_ins:
            serializer.save(employee=employee_user, company=company_ins)
            return Response(
                {"message": "education record created successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        education_id = request.query_params.get("education_id")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            education_ins = CompanyEmployeeEducationDetails.objects.filter(
                company=employee.company,
                employee=employee.employee,
                id=education_id,
                is_deleted=False,
            ).last()
            if education_ins:
                serializer = EmployeeEducationSerializer(
                    education_ins, data=request.data, partial=True
                )
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(
                    {"message": "education details updated successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "invalid education details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        education_id = request.query_params.get("education_id")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            education_ins = CompanyEmployeeEducationDetails.objects.filter(
                company=employee.company,
                employee=employee.employee,
                id=education_id,
                is_deleted=False,
            ).last()
            if education_ins:
                education_ins.is_deleted = True
                education_ins.save()

                return Response(
                    {"message": "education deleted successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "invalid education details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class EmployeeExperienceDetailsAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee_ins:
            experience_ins = CompanyEmployeeExperienceDetails.objects.filter(
                company__id=company_uuid,
                employee=employee_ins.employee,
                is_deleted=False,
            )
            serializer = EmployeeExperienceSerializer(experience_ins, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_ins = Company.objects.filter(id=company_uuid).last()
        employee_user = request.user

        serializer = EmployeeExperienceSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee_ins:
            serializer.save(employee=employee_user, company=company_ins)
            return Response(
                {"message": "education record created successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        experience_id = request.query_params.get("experience_id")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            experience_ins = CompanyEmployeeExperienceDetails.objects.filter(
                company=employee.company,
                employee=employee.employee,
                id=experience_id,
                is_deleted=False,
            ).last()
            if experience_ins:
                serializer = EmployeeExperienceSerializer(
                    experience_ins, data=request.data, partial=True
                )
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(
                    {"message": "education details updated successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "invalid education details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        experience_id = request.query_params.get("experience_id")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            experience_ins = CompanyEmployeeExperienceDetails.objects.filter(
                company=employee.company,
                employee=employee.employee,
                id=experience_id,
                is_deleted=False,
            ).last()
            if experience_ins:
                experience_ins.is_deleted = True
                experience_ins.save()

                return Response(
                    {"message": "experience deleted successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "invalid experience details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class EmployeeCertificationsAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee_ins:
            experience_ins = CompanyEmployeeCertifications.objects.filter(
                company__id=company_uuid,
                employee=employee_ins.employee,
                is_deleted=False,
            )
            serializer = EmployeeCertificationSerializer(experience_ins, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company_ins = Company.objects.filter(id=company_uuid).last()
        employee_user = request.user

        serializer = EmployeeCertificationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee_ins:
            serializer.save(employee=employee_user, company=company_ins)
            return Response(
                {"message": "certification record created successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        certification_id = request.query_params.get("certification_id")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            certification_ins = CompanyEmployeeCertifications.objects.filter(
                company=employee.company,
                employee=employee.employee,
                id=certification_id,
                is_deleted=False,
            ).last()
            if certification_ins:
                serializer = EmployeeCertificationSerializer(
                    certification_ins, data=request.data, partial=True
                )
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(
                    {"message": "certification updated successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "invalid certification"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        certification_id = request.query_params.get("certification_id")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            certification_ins = CompanyEmployeeCertifications.objects.filter(
                company=employee.company,
                employee=employee.employee,
                id=certification_id,
                is_deleted=False,
            ).last()
            if certification_ins:
                certification_ins.is_deleted = True
                certification_ins.save()

                return Response(
                    {"message": "certification deleted successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "invalid certification details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class AddEmployeeDepartmentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployDepartment,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        department_id = request.query_params.get("department_id")
        serializer = MultipleEmployeeDepartmentSerializer(
            data=request.data,
            context={"company_uuid": company_uuid, "department_id": department_id},
        )
        serializer.is_valid(raise_exception=True)

        employee_department_data = serializer.validated_data.get(
            "employee_department_data"
        )
        for employee_department in employee_department_data:
            employee_id = employee_department.get("employee_id")

            employee_ins = CompanyEmployeeList.objects.filter(id=employee_id, company__id=company_uuid, is_deleted=False).first()

            # department_ins = CompanyDepartmentSettings.objects.filter(
            #     id = department_id,
            #     company__id=company_uuid,
            #     is_deleted=False
            #     ).last()
            # employee_ins.employee_department = department_ins
            # employee_ins.save()

            if employee_ins.employee_department:
                former_department = employee_ins.employee_department
            else:
                former_department = None

            department_ins = CompanyDepartmentSettings.objects.filter(
                id=department_id, company__id=company_uuid, is_deleted=False
            ).first()
            employee_ins.employee_department = department_ins
            employee_ins.save()
            current_department = department_ins.department_name
            if former_department is None:
                send_email.delay(
                    recipient=employee_ins.employee_email,
                    subject="Employee Redeployment",
                    template_dir="employee_redeployment.html",
                    company_name=employee_ins.company.company_name,
                    current_department=current_department,
                )
            else:
                send_email.delay(
                    recipient=employee_ins.employee_email,
                    subject="Employee Redeployment",
                    template_dir="employee_redeployment_update.html",
                    company_name=employee_ins.company.company_name,
                    former_department=former_department.department_name,
                    current_department=current_department,
                )
        response = {"message": "deployed successfully"}
        return Response(response, status=status.HTTP_200_OK)


class RemoveMultipleEmployeeDepartmentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployDepartment,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        department_id = request.query_params.get("department_id")
        serializer = MultipleEmployeeDepartmentSerializer(
            data=request.data,
            context={"company_uuid": company_uuid, "department_id": department_id},
        )
        serializer.is_valid(raise_exception=True)

        employee_department_data = serializer.validated_data.get(
            "employee_department_data"
        )
        for employee_department in employee_department_data:
            employee_id = employee_department.get("employee_id")

            employee_ins = CompanyEmployeeList.objects.filter(id=employee_id, company__id=company_uuid, is_deleted=False).first()

            employee_ins.employee_department = None
            employee_ins.save()

        response = {"message": "department removed successfully"}
        return Response(response, status=status.HTTP_200_OK)


class ReInviteEmployeeAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanAddEmployeePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        """Re Invite Employee"""
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")

        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, id=employee_id, is_active=True
        ).first()
        if employee_ins:
            company_ins = Company.objects.filter(id=company_uuid).last()
            invite_employee = CompanyEmployeeList.invite_employee(
                company_ins=company_ins, email=employee_ins.employee_email
            )
            return Response(
                {"success": "employee re-invited successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class CreateEmployeeByInviteAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanAddEmployeePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        """Invite Employee Form"""
        company_uuid = request.query_params.get("company_uuid")
        serializer = CreateEmployeeInviteSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        all_employee = serializer.validated_data.get("all_employees")

        company_ins = Company.objects.filter(id=company_uuid).first()
        employee_data = CompanyEmployeeList.create_employee_invite(
            company_ins, all_employee, request.user
        )

        return Response(employee_data, status=status.HTTP_200_OK)


class EmployeeInformationApproval(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanAddEmployeePermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")

        all_new_employees = CompanyEmployeeOnboardingForm.objects.filter(employee_company__id=company_uuid, approval_status="AWAITING_APPROVAL", is_deleted=False)

        serializer = EmployeeOnboardingSerializer(all_new_employees, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        approval_id = request.query_params.get("approval_id")

        approval_ins = CompanyEmployeeOnboardingForm.objects.filter(id=approval_id, employee_company__id=company_uuid, approval_status="AWAITING_APPROVAL", is_deleted=False).last()

        if approval_ins:
            employee_instance = User.objects.filter(
                email=approval_ins.employee_email
            ).first()
            if employee_instance:
                employee = employee_instance
            else:
                employee = None
            serializer = EmployeeApprovalInfoSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            approval_status = serializer.validated_data.get("approval_status")
            if approval_status == "REJECT":
                approval_ins.approval_status = "NULL"
                approval_ins.is_completed = False
                approval_ins.save()
                send_email.delay(
                    recipient=approval_ins.employee_email,
                    subject="Onboarding Rejected",
                    template_dir="rejected_onboarding.html",
                    company_name=approval_ins.employee_company.company_name,
                    form_invite_link=f"https://www.home.paybox360.com/employee-invite-link/{approval_ins.invite_id}",
                )

                return Response(
                    {"message": "approval has been rejected"}, status=status.HTTP_200_OK
                )
            elif approval_status == "ACCEPT":

                new_employee_ins = CompanyEmployeeList.objects.filter(employee_email=approval_ins.employee_email, company__id=company_uuid, is_deleted=False).first()

                if new_employee_ins:
                    if approval_ins.approval_status == "APPROVED":
                        return Response(
                            {"message": "request has already been approved"},
                            status=status.HTTP_200_OK,
                        )
                    approval_ins.approval_status = "APPROVED"
                    approval_ins.is_completed = True
                    approval_ins.save()

                    new_employee_ins.employee_first_name = (
                        approval_ins.employee_first_name
                    )
                    new_employee_ins.employee_last_name = (
                        approval_ins.employee_last_name
                    )
                    new_employee_ins.employee_phone_number = (
                        approval_ins.employee_phone_number
                    )
                    new_employee_ins.employee_alternate_email = (
                        approval_ins.employee_alternate_email
                    )
                    # new_employee_ins.employee_birth_date = (
                    #     approval_ins.employee_birth_date
                    # )
                    # new_employee_ins.employee_gender = approval_ins.employee_gender
                    # # new_employee_ins.employee_staff_id = approval_ins.employee_staff_id
                    # # new_employee_ins.employee_staff_id_reporting_line = approval_ins.employee_staff_id_reporting_line
                    # new_employee_ins.employee_state = approval_ins.employee_state
                    # new_employee_ins.employee_city = approval_ins.employee_city
                    # new_employee_ins.employee_town = approval_ins.employee_town
                    # new_employee_ins.employee_bus_stop = approval_ins.employee_bus_stop
                    # new_employee_ins.employee_street_name = (
                    #     approval_ins.employee_street_name
                    # )
                    # new_employee_ins.employee_house_no = approval_ins.employee_house_no
                    # new_employee_ins.employee_postal_code = (
                    #     approval_ins.employee_postal_code
                    # )
                    # new_employee_ins.employee_first_next_of_kin_firstname = (
                    #     approval_ins.employee_first_next_of_kin_firstname
                    # )
                    # new_employee_ins.employee_first_next_of_kin_lastname = (
                    #     approval_ins.employee_first_next_of_kin_lastname
                    # )
                    # new_employee_ins.employee_first_next_of_kin_phone_number = (
                    #     approval_ins.employee_first_next_of_kin_phone_number
                    # )
                    # new_employee_ins.employee_first_next_of_kin_relationship = (
                    #     approval_ins.employee_first_next_of_kin_relationship
                    # )
                    # new_employee_ins.employee_first_next_of_kin_address = (
                    #     approval_ins.employee_first_next_of_kin_address
                    # )
                    # new_employee_ins.employee_second_next_of_kin_firstname = (
                    #     approval_ins.employee_second_next_of_kin_firstname
                    # )
                    # new_employee_ins.employee_second_next_of_kin_lastname = (
                    #     approval_ins.employee_second_next_of_kin_lastname
                    # )
                    # new_employee_ins.employee_second_next_of_kin_phone_number = (
                    #     approval_ins.employee_second_next_of_kin_phone_number
                    # )
                    # new_employee_ins.employee_second_next_of_kin_relationship = (
                    #     approval_ins.employee_second_next_of_kin_relationship
                    # )
                    # new_employee_ins.employee_second_next_of_kin_address = (
                    #     approval_ins.employee_second_next_of_kin_address
                    # )
                    new_employee_ins.employee = employee
                    if new_employee_ins.is_suspended is False:
                        new_employee_ins.employee_status = (
                            "ACTIVE" if employee else "NOT_JOINED"
                        )
                        new_employee_ins.is_active = True if employee else False
                    

                    new_employee_ins.save()
                    send_email.delay(
                        recipient=approval_ins.employee_email,
                        subject="Onboarding Approved",
                        template_dir="approved_onboarding.html",
                        company_name=approval_ins.employee_company.company_name,
                    )

                return Response({"message": "approval has accepted successfully"}, status=status.HTTP_200_OK)
            elif approval_status == "DELETE":
                new_employee_ins = CompanyEmployeeList.objects.filter(employee_email=approval_ins.employee_email, company__id=company_uuid, is_deleted=False).first()
                if new_employee_ins:
                    approval_ins.approval_status = "DELETED"
                    approval_ins.is_deleted = True
                    approval_ins.save()

                    new_employee_ins.is_deleted = True
                    new_employee_ins.employee_status = "DELETED"
                    new_employee_ins.save()
                    
                return Response({"message": "approval has been deleted successfully"}, status=status.HTTP_200_OK)

            else:
                return Response(
                    {"error": "invalid approval_status choice"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "approval does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class GetTaxBandAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        tax_band_ins = CompanyTaxBand.objects.filter(
            company__id=company_uuid, is_deleted=False
        )
        serializer = TaxBandSerializer(tax_band_ins, many=True)
        return Response({"tax_band_data": serializer.data}, status=status.HTTP_200_OK)


class AddTaxBandAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = TaxBandSerializer(
            data=request.data,
            context={"company_uuid": company_uuid, "request_user": request.user},
        )
        serializer.is_valid(raise_exception=True)
        return Response({"message": "tax band created"}, status=status.HTTP_200_OK)


class EditTaxBandAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        tax_band_id = request.query_params.get("tax_band_id")
        tax_band_ins = CompanyTaxBand.objects.filter(
            id=tax_band_id, company__id=company_uuid, is_deleted=False
        ).last()
        if not tax_band_ins:
            return Response(
                {"message": "tax band unavailable"}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = EditTaxBandSerializer(
            tax_band_ins,
            data=request.data,
            context={
                "company_uuid": company_uuid,
                "request_user": request.user,
                "tax_band_id": tax_band_id,
            },
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({"message": "tax band edited"}, status=status.HTTP_200_OK)


class DeleteTaxBandAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        tax_band_id = request.query_params.get("tax_band_id")
        tax_band_ins = CompanyTaxBand.objects.filter(
            id=tax_band_id, company__id=company_uuid, is_deleted=False
        ).last()
        if not tax_band_ins:
            return Response(
                {"message": "tax band unavailable"}, status=status.HTTP_404_NOT_FOUND
            )
        tax_band_ins.is_deleted = True
        tax_band_ins.save()
        return Response({"message": "tax band deleted"}, status=status.HTTP_200_OK)


class RejectRunningPayrollAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        DeletePayrollPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def delete(self, request):
        bulk_id = request.query_params.get("bulk_id")
        company_uuid = request.query_params.get("company_uuid")
        payroll_table = PayrollTable.objects.filter(company__id=company_uuid, bulk_id=bulk_id, payroll_deleted=False)
        instance = payroll_table.filter(status__in=["APPROVAL", "DISBURSE", "OMITTED"])
        if not instance:
            return Response(
                {"message": "payroll data not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        instance.update(payroll_deleted=True, status="OMITTED")
        detail_data = CompanyDetailsData.objects.filter(bulk_id=bulk_id).first()
        if detail_data:
            if not payroll_table.filter(status="DISBURSED").first():
                detail_data.deleted_by = request.user
                detail_data.is_deleted = True
                detail_data.save()

        return Response(
            {"message": "payroll rejected successfully"}, status=status.HTTP_200_OK
        )


class ApplyDeductionBonusAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        PayrollApprovalPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = ApplyDeductionBonusSerializer(
            data=request.data, context={"company_uuid": company_uuid}
        )
        serializer.is_valid(raise_exception=True)
        return Response(
            {"message": "applied successfully"},
            status=status.HTTP_404_NOT_FOUND,
        )


class FixALLPayrollNameAPIView(APIView):
    permission_classes = [IsAuthenticated, IsStaff]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        all_payroll = PayrollTable.objects.all()
        for payroll in all_payroll:
            get_company = payroll.company
            get_email = payroll.payroll_user.email
            get_instance = CompanyEmployeeList.objects.filter(
                company=get_company, employee__email=get_email
            ).first()
            if get_instance:
                payroll.first_name = (
                    get_instance.employee_first_name
                    if get_instance.employee_first_name
                    else None
                )
                payroll.last_name = (
                    get_instance.employee_last_name
                    if get_instance.employee_last_name
                    else None
                )
                payroll.email = get_instance.employee_email
                payroll.gender = get_instance.employee_gender
                payroll.save()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class FixALLPayrollNameByCompanyAPIView(APIView):
    permission_classes = [IsAuthenticated, IsStaff]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        if not company_uuid:
            return Response(
                {"message": "no company_uuid as parameter"}, status=status.HTTP_200_OK
            )

        all_payroll = PayrollTable.objects.filter(company__id=company_uuid)
        for payroll in all_payroll:
            get_company = payroll.company
            get_email = payroll.payroll_user.email
            get_instance = CompanyEmployeeList.objects.filter(
                company=get_company, employee__email=get_email
            ).first()
            if get_instance:
                payroll.first_name = (
                    get_instance.employee_first_name
                    if get_instance.employee_first_name
                    else None
                )
                payroll.last_name = (
                    get_instance.employee_last_name
                    if get_instance.employee_last_name
                    else None
                )
                payroll.email = get_instance.employee_email
                payroll.gender = get_instance.employee_gender
                payroll.save()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class FixEmployeeNameTitleAPIView(APIView):
    permission_classes = [IsAuthenticated, IsStaff]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):

        get_instance = CompanyEmployeeList.objects.all()
        for instance in get_instance:
            instance.employee_first_name = (
                instance.employee_first_name.title()
                if instance.employee_first_name
                else None
            )
            instance.employee_last_name = (
                instance.employee_last_name.title()
                if instance.employee_last_name
                else None
            )
            instance.save()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class SearchEmployee(generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = SearchEmployeeSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )
    search_fields = (
        "employee_email",
        "employee_first_name",
        "employee_last_name",
        "employee_phone_number",
    )

    def get_queryset(self):
        company_uuid = self.request.query_params.get("company_uuid")
        employee = (
            CompanyEmployeeList.objects.exclude(Q(is_deleted=True))
            .filter(company__id=company_uuid)
            .order_by(Lower("employee_last_name"))
        )
        return employee


class SearchPayrollApproval(generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = SearchPayrollApprovalSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )
    search_fields = ("email", "first_name", "last_name", "phone_number")

    def get_queryset(self):
        company_uuid = self.request.query_params.get("company_uuid")
        employee = (
            PayrollTable.objects.exclude(Q(payroll_deleted=True))
            .filter(company__id=company_uuid, status="APPROVAL")
            .order_by(Lower("last_name"))
        )
        return employee


class SearchPayrollDisburse(generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = SearchPayrollDisburseSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )
    search_fields = ("email", "first_name", "last_name", "phone_number")

    def get_queryset(self):
        company_uuid = self.request.query_params.get("company_uuid")
        employee = (
            PayrollTable.objects.exclude(Q(payroll_deleted=True))
            .filter(company__id=company_uuid, status="DISBURSE")
            .order_by(Lower("last_name"))
        )
        return employee


class GetEmployeeDataPercentAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee_ins = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee_ins:
            data_percentage = check_employee_onboarding_percentage(employee_ins)
        else:
            data_percentage = 0
        response = {"message": "success", "percent": data_percentage}
        return Response(response, status=status.HTTP_200_OK)


class InstantWageHistoryAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):

        page = request.GET.get("page", 1)
        instant_wage_history_qs = DebitCreditRecordOnAccount.objects.filter(
            user=request.user, requisition_type="INSTANT_WAGE"
        ).order_by("-id")
        instant_wage_data = WalletHistoryPaginator.paginate(
            request, instant_wage_history_qs, page
        )
        serializer = UserWalletHistorySerializer(instant_wage_data, many=True)
        try:
            data = {
                "data_type": "INSTANT_WAGE_DATA",
                "data": serializer.data,
                "total_page": instant_wage_data.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": instant_wage_data.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "INSTANT_WAGE_DATA",
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class EditEmployeeProfileAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee:
            serializer = EditEmployeeProfileSerializer(
                employee, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(
                {"message": "employee updated successfully"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class AdminEditEmployeeProfileAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee:
            serializer = EmployeeProfileSerializer(employee)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee:
            serializer = EditEmployeeProfileSerializer(
                employee, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(
                {"message": "employee updated successfully"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class AdminEmployeeFamilyContactAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee:
            serializer = EmployeeFamilyContactSerializer(employee)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee:
            serializer = EditEmployeeFamilyContactSerializer(
                employee, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(
                {"message": "family contact(s) updated successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class AdminEmployeeAccountDetailsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            account_ins = CompanyEmployeeAccountDetails.objects.filter(
                company__id=company_uuid, employee=employee_ins.employee, is_active=True
            )
            serializer = EmployeeAccountSerializer(account_ins, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class AdminEmployeeEducationDetailsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            education_ins = CompanyEmployeeEducationDetails.objects.filter(
                company__id=company_uuid,
                employee=employee_ins.employee,
                is_deleted=False,
            )
            serializer = EmployeeEducationSerializer(education_ins, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class AdminEmployeeExperienceDetailsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            experience_ins = CompanyEmployeeExperienceDetails.objects.filter(
                company__id=company_uuid,
                employee=employee_ins.employee,
                is_deleted=False,
            )
            serializer = EmployeeExperienceSerializer(experience_ins, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class AdminEmployeeCertificationsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            experience_ins = CompanyEmployeeCertifications.objects.filter(
                company__id=company_uuid,
                employee=employee_ins.employee,
                is_deleted=False,
            )
            serializer = EmployeeCertificationSerializer(experience_ins, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )


class PensionFundAdminAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        instance = (
            PensionFundAdminSettings.objects.values("id", "pfa_name")
            .filter(company__id=company_uuid, is_deleted=False)
            .order_by("-created_at")
        )
        if instance:
            serializer = AllPensionFundAdminSerializer(instance, many=True)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pension fund unavailable", "data": []}
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = MultiplePensionFundAdminSerializer(
            data=request.data, context={"company_uuid": company_uuid}
        )
        company_ins = Company.objects.get(id=company_uuid)
        serializer.is_valid(raise_exception=True)

        pfa_data = serializer.validated_data.get("pfa_data")
        for pfa in pfa_data:
            pfa_name = pfa.get("pfa_name")
            pfa_account_number = pfa.get("pfa_account_number")
            pfa_bank_name = pfa.get("pfa_bank_name")
            pfa_account_name = pfa.get("pfa_account_name")
            pfa_bank_code = pfa.get("pfa_bank_code")
            pfa_sort_code = pfa.get("pfa_sort_code")
            pfa_email = pfa.get("pfa_email")
            company_code = pfa.get("company_code")
            PensionFundAdminSettings.objects.create(
                company=company_ins,
                employer=company_ins.user,
                pfa_name=pfa_name,
                pfa_account_number=pfa_account_number,
                pfa_bank_name=pfa_bank_name,
                pfa_account_name=pfa_account_name,
                pfa_bank_code=pfa_bank_code,
                pfa_sort_code=pfa_sort_code,
                pfa_email=pfa_email,
                company_code=company_code,
                added_by=request.user,
            )
        response = {"message": "pension fund admin(s) added successfully"}
        return Response(response, status=status.HTTP_200_OK)

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        pfa_id = request.query_params.get("pfa_id")

        # company_ins = Company.objects.get(id=company_uuid)
        instance = PensionFundAdminSettings.objects.filter(
            id=pfa_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            serializer = AllPensionFundAdminSerializer(
                instance, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["added_by"] = request.user
            data = PensionFundAdminSettings.objects.filter(
                company__id=company_uuid,
                pfa_name=request.data.get("pfa_name"),
                is_deleted=False,
            ).exists()
            if data:
                response = {
                    "message": "pension fund admin already exist",
                    "data": serializer.data,
                }
                return Response(response, status=status.HTTP_200_OK)
            serializer.save()
            response = {
                "message": "pension fund admin updated successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pension fund admin unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request):
        company_uuid = request.query_params.get("company_uuid")
        pfa_id = request.query_params.get("pfa_id")

        # company_ins = Company.objects.get(id=company_uuid)
        instance = PensionFundAdminSettings.objects.filter(
            id=pfa_id, company__id=company_uuid, is_deleted=False
        ).last()
        if instance:
            instance.is_deleted = True
            instance.is_active = False
            instance.save()
            response = {
                "message": "pension fund admin deleted successfully",
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pension fund admin unavailable"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class AdminEmployeePensionFundAdminAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = MultipleEmployeePensionFundAdminSerializer(
            data=request.data, context={"company_uuid": company_uuid}
        )
        serializer.is_valid(raise_exception=True)

        employee_pfa_data = serializer.validated_data.get("employee_pfa_data")
        for employee_pfa in employee_pfa_data:
            pfa_id = employee_pfa.get("pfa_id")
            employee_id = employee_pfa.get("employee_id")
            pfa_pin = employee_pfa.get("pfa_pin")

            employee_ins = CompanyEmployeeList.objects.filter(id=employee_id, company__id=company_uuid, is_deleted=False).first()

            pfa_ins = PensionFundAdminSettings.objects.filter(
                id=pfa_id, company__id=company_uuid, is_deleted=False
            ).last()
            employee_ins.pension_fund_admin = pfa_ins
            employee_ins.pension_pin = pfa_pin
            employee_ins.save()
        response = {"message": "pfa added successfully"}
        return Response(response, status=status.HTTP_200_OK)


class AdminResetEmployeePayrollPinAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            if not employee_ins.employee:
                return Response(
                    {"message": "Employee is not active"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            serializer = AdminResetEmployeePayrollPINSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            pin = serializer.validated_data.get("pin")
            repeat_pin = serializer.validated_data.get("repeat_pin")
            if pin != repeat_pin:
                return Response(
                    {"message": "Employee pin and confirm pin must match!"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            try:
                with transaction.atomic():
                    # remove pin
                    user = employee_ins.employee
                    user.requisition_transaction_pin = None
                    user.save()
                    update_user_pin = User.create_payroll_pin(user, pin)

                    return Response(
                        {"message": "Employee pin been has been changed successfully"},
                        status=status.HTTP_200_OK,
                    )
            except:
                return Response(
                    {"message": "Something went wrong, please try again!"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"message": "Emplopee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AdminBlockEmployeeLoginAccessAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")

        instance = CompanyEmployeeList.objects.filter(company__id=company_uuid, id=employee_id, is_deleted=False).first()

        if instance:
            instance.is_active = False
            instance.is_suspended = True
            instance.employee_status = "SUSPENDED"
            instance.save()
            return Response(
                {"message": "employee suspended successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "user not found"},
                status=status.HTTP_404_NOT_FOUND,
            )


class AdminNudgeEmployeeUpdateProfileAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            if not employee_ins.employee:
                return Response(
                    {"message": "Employee is not active"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            send_email.delay(
                recipient=employee_ins.employee_email,
                subject="Update Profile",
                template_dir="update_employee_profile.html",
                company_name=employee_ins.company.company_name,
            )
            return Response(
                {"message": "Employee notified successfully"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AdminDisableEmployeeWorkNotificationAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            if not employee_ins.is_work_anniversary_enabled:
                return Response(
                    {"message": "Employee work anniversary already disabled!"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            employee_ins.is_work_anniversary_enabled = False
            employee_ins.save()
            return Response(
                {"message": "Employee work anniversary disabled successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AdminDisableEmployeeBirthdayNotificationAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            if not employee_ins.is_birthday_enabled:
                return Response(
                    {"message": "Employee work anniversary already disabled!"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            employee_ins.is_birthday_enabled = False
            employee_ins.save()
            return Response(
                {"message": "Employee work anniversary disabled successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AdminEnableEmployeeWorkNotificationAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            if employee_ins.is_work_anniversary_enabled:
                return Response(
                    {"message": "Employee work anniversary already enabled!"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            employee_ins.is_work_anniversary_enabled = True
            employee_ins.save()
            return Response(
                {"message": "Employee work anniversary enabled successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AdminEnableEmployeeBirthdayNotificationAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            if employee_ins.is_birthday_enabled:
                return Response(
                    {"message": "Employee work anniversary already enabled!"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            employee_ins.is_birthday_enabled = True
            employee_ins.save()
            return Response(
                {"message": "Employee work anniversary enabled successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AdminRedeployEmployeeDepartmentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployDepartment,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            serializer = AdminRedeployEmployeeDepartmentSerializer(
                data=request.data, context={"company_uuid": company_uuid}
            )
            serializer.is_valid(raise_exception=True)

            if employee_ins.employee_department:
                former_department = employee_ins.employee_department
            else:
                former_department = None

            department_id = serializer.validated_data.get("department_id")
            department_ins = CompanyDepartmentSettings.objects.filter(
                id=department_id, company__id=company_uuid, is_deleted=False
            ).last()
            employee_ins.employee_department = department_ins
            employee_ins.save()
            current_department = department_ins.department_name
            if former_department is None:
                send_email.delay(
                    recipient=employee_ins.employee_email,
                    subject="Employee Redeployment",
                    template_dir="employee_redeployment.html",
                    company_name=employee_ins.company.company_name,
                    current_department=current_department,
                )
            else:
                send_email.delay(
                    recipient=employee_ins.employee_email,
                    subject="Employee Redeployment",
                    template_dir="employee_redeployment_update.html",
                    company_name=employee_ins.company.company_name,
                    former_department=former_department.department_name,
                    current_department=current_department,
                )
            response = {
                "message": f"employee redeployed to {current_department} successfully"
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AdminRemoveEmployeeDepartmentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployDepartment,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if not employee_ins:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        employee_ins.employee_department = None
        employee_ins.save()

        response = {"message": f"successful"}
        return Response(response, status=status.HTTP_200_OK)


class AdminPromoteEmployeeAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if employee_ins:
            serializer = AdminPromoteEmployeeSerializer(
                data=request.data, context={"company_uuid": company_uuid}
            )
            serializer.is_valid(raise_exception=True)

            if employee_ins.employee_pay_grade:
                former_pay_grade = employee_ins.employee_pay_grade
            else:
                former_pay_grade = None

            pay_grade_id = serializer.validated_data.get("pay_grade_id")
            pay_grade_ins = CompanyPayGradeSettings.objects.filter(
                id=pay_grade_id, company__id=company_uuid, is_deleted=False
            ).last()
            employee_ins.employee_pay_grade = pay_grade_ins
            employee_ins.save()
            current_pay_grade = pay_grade_ins.pay_grade_name
            if former_pay_grade is None:
                send_email.delay(
                    recipient=employee_ins.employee_email,
                    subject="Employee Promotion",
                    template_dir="employee_promotion.html",
                    company_name=employee_ins.company.company_name,
                    current_pay_grade=current_pay_grade,
                )
            else:
                send_email.delay(
                    recipient=employee_ins.employee_email,
                    subject="Employee Promotion",
                    template_dir="employee_promotion_update.html",
                    company_name=employee_ins.company.company_name,
                    former_pay_grade=former_pay_grade.pay_grade_name,
                    current_pay_grade=current_pay_grade,
                )
            response = {
                "message": f"employee promoted to {current_pay_grade} successfully"
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ModulePensionFundAdminAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        instance = PensionFundAdminSettings.objects.filter(
            company__isnull=True, is_active=True
        ).order_by("pfa_name")
        if instance:
            serializer = AllPensionFundAdminSerializer(instance, many=True)
            response = {"message": "successful", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"message": "pension fund unavailable", "data": []}
            return Response(response, status=status.HTTP_200_OK)


class AddEmployeePensionFundAdminAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")

        serializer = AddEmployeePensionFundAdminSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        pfa_id = serializer.validated_data.get("pfa_id")
        pfa_pin = serializer.validated_data.get("pfa_pin")

        employee_ins = CompanyEmployeeList.objects.filter(id=employee_id, company__id=company_uuid, is_deleted=False).first()

        if employee_ins:
            pfa_ins = PensionFundAdminSettings.objects.filter(
                id=pfa_id, company__isnull=True, is_deleted=False
            ).last()
            employee_ins.pension_fund_admin = pfa_ins
            employee_ins.pension_pin = pfa_pin
            employee_ins.save()
            response = {"message": "pfa added successfully"}
            return Response(response, status=status.HTTP_200_OK)
        else:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class EmployeePFAListAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    @staticmethod
    def get(request):
        page = request.GET.get("page", 1)
        company_uuid = request.query_params.get("company_uuid")

        employees_pfa_base = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, pension_fund_admin__isnull=False, is_deleted=False
        )
        employees_pfa = employees_pfa_base.values("pension_fund_admin").distinct()
        employees_pfa_list = PayrollPaginator.paginate(request, employees_pfa, page)
        serializer = ListEmployeePFASerializer(employees_pfa, many=True)
        try:
            data = {
                "data_type": "EMPLOYEES_PFA_LIST",
                "data": serializer.data,
                "total_page": employees_pfa_list.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": employees_pfa_list.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "EMPLOYEES_PFA_LIST",
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class AllPFAEmployeeListAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    @staticmethod
    def get(request):
        page = request.GET.get("page", 1)
        company_uuid = request.query_params["company_uuid"]

        employees_pfa_base = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, pension_fund_admin__isnull=False, is_deleted=False
        )
        employees_pfa = employees_pfa_base.values("pension_fund_admin").distinct()
        employees_pfa_list = PayrollPaginator.paginate(request, employees_pfa, page)

        total_pfa = employees_pfa.count() or 0
        total_pfa_sum = (
            employees_pfa_base.aggregate(Sum("employee_pension_amount"))[
                "employee_pension_amount__sum"
            ]
            or 0
        )
        total_employees = employees_pfa_base.count() or 0

        serializer = AllPFAEmployeeListSerializer(
            employees_pfa, many=True, context={"company_uuid": company_uuid}
        )
        try:
            data = {
                "data_type": "EMPLOYEES_PFA_LIST",
                "total_pfa": total_pfa,
                "total_pfa_sum": total_pfa_sum,
                "total_employee": total_employees,
                "data": serializer.data,
                "total_page": employees_pfa_list.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": employees_pfa_list.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "EMPLOYEES_PFA_LIST",
                "total_pfa": total_pfa,
                "total_pfa_sum": total_pfa_sum,
                "total_employee": total_employees,
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class EmployeeDepartmentListAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    @staticmethod
    def get(request):
        page = request.GET.get("page", 1)
        company_uuid = request.query_params["company_uuid"]

        employees_department_base = CompanyEmployeeList.objects.filter(
            company__id=company_uuid,
            employee_department__isnull=False,
            is_deleted=False,
        )
        employees_department = employees_department_base.values(
            "employee_department"
        ).distinct()
        employees_department_list = PayrollPaginator.paginate(
            request, employees_department, page
        )
        serializer = AllDepartmentEmployeeListSerializer(
            employees_department, many=True, context={"company_uuid": company_uuid}
        )
        try:
            data = {
                "data_type": "EMPLOYEES_DEPARTMENT_LIST",
                "data": serializer.data,
                "total_page": employees_department_list.paginator.num_pages,
                "page_count": len(serializer.data),
                "total_data_count": employees_department_list.paginator.count,
            }
        except AttributeError:
            data = {
                "data_type": "EMPLOYEES_DEPARTMENT_LIST",
                "data": [],
                "total_page": 0,
                "page_count": len(serializer.data),
                "total_data_count": 0,
            }
        return Response(data, status=status.HTTP_200_OK)


class AdminUnBlockEmployeeLoginAccessAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")

        instance = CompanyEmployeeList.objects.filter(company__id=company_uuid, id=employee_id, is_deleted=False).first()

        if instance:
            instance.is_active = True
            instance.is_suspended = False
            instance.employee_status = "ACTIVE"
            instance.save()
            return Response(
                {"message": "employee is now active"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "user not found"},
                status=status.HTTP_404_NOT_FOUND,
            )


class RunSelectedPayrollAPIVIew(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        UserHasPin,
        CanRunPayrollPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        company_uuid = request.query_params.get("company_uuid")

        serializer = AddSelectedPayrollSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        payroll_pin = serializer.validated_data.get("payroll_pin")
        payroll_date = serializer.validated_data.get("payroll_date")
        # is_recurring = serializer.validated_data.get("is_recurring")
        narration = serializer.validated_data.get("narration")
        payroll_month = serializer.validated_data.get("payroll_month")
        payroll_year = serializer.validated_data.get("payroll_year")
        employees = serializer.validated_data.get("employees")
        multiple_payroll = serializer.validated_data.get("multiple_payroll")

        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=payroll_pin
        )
        if not check_pin:
            return Response(
                {"status": False, "message": "Payroll pin is incorrect!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            month_map = {
                "january": 1,
                "february": 2,
                "march": 3,
                "april": 4,
                "may": 5,
                "june": 6,
                "july": 7,
                "august": 8,
                "september": 9,
                "october": 10,
                "november": 11,
                "december": 12,
            }
            if payroll_month not in month_map:
                return Response(
                    {"message": "payroll month not in correct format"},
                    status.HTTP_400_BAD_REQUEST,
                )

            # company_ins = Company.objects.get(id=company_uuid)
            run_payroll = PayrollTable.create_approval_payroll(
                user=request.user,
                company_uuid=company_uuid,
                payroll_date=payroll_date,
                is_recurring=False,
                narration=narration,
                payroll_month=payroll_month,
                payroll_year=payroll_year,
                employees=employees,
                multiple_payroll=multiple_payroll
            )
            if not run_payroll["status"]:
                return Response(run_payroll, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(run_payroll, status=status.HTTP_200_OK)


class AddEmployeeTemplateAPIView(APIView):
    def get(self, request):
        csv_file_path = "add_employee_data.csv"
        # Check if the file exists
        if os.path.exists(csv_file_path):
            with open(csv_file_path, "rb") as file:
                return Response(
                    {"message": "success", "data": file.read()},
                    content_type="text/csv",
                    status=status.HTTP_200_OK,
                )
        else:
            return Response(
                {"message": "File not found"}, status=status.HTTP_404_NOT_FOUND
            )


class AddEmployeeWorkTypeAPIView(APIView):
    permission_classes = [
        EmployeeIsActive,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        serializer = AddEmployeeWorkTypeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        all_employee_email = serializer.validated_data.get("employees")
        work_type = serializer.validated_data.get("work_type")
        successful = []
        unsuccessful = []
        for employee_email in all_employee_email:

            employee_ins = CompanyEmployeeList.objects.filter(company__id=company_id, employee_email=employee_email, is_deleted=False).first()

            if employee_ins:
                employee_ins.work_type = work_type
                employee_ins.save()
                successful.append(employee_email)
            else:
                unsuccessful.append(employee_email)
        data = {
            "message": "successful",
            "all_employee_email": serializer.data,
            "successful": successful,
            "unsuccessful": unsuccessful,
        }
        return Response(data, status=status.HTTP_200_OK)
    
class AddEmployeeStaffTypeAPIView(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        serializer = AddEmployeeStaffTypeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        all_employee_email = serializer.validated_data.get("employees")
        staff_type = serializer.validated_data.get("staff_type")
        successful = []
        unsuccessful = []
        for employee_email in all_employee_email:
            employee_ins = CompanyEmployeeList.objects.filter(company__id=company_id, employee_email=employee_email, is_deleted=False).first()
            if employee_ins:
                employee_ins.staff_type = staff_type
                employee_ins.save()
                successful.append(employee_email)
            else:
                unsuccessful.append(employee_email)
        data = {
            "message": "successful",
            "all_employee_email": serializer.data,
            "successful": successful,
            "unsuccessful": unsuccessful

        }
        return Response(data, status=status.HTTP_200_OK)



class FetchCompanyPermissionRolesAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = FetchPermissionsSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_deleted",
        "can_disburse",
        "can_approve",
        "can_add_member",
        "can_edit_member",
        "can_delete_member",
        "can_delete_payroll",
        "can_run_payroll",
        "can_edit_payroll_settings",
        "can_create_leave_type",
        "can_edit_leave_type",
        "can_delete_leave_type",
        "can_create_leave_policy",
        "can_edit_leave_policy",
        "can_delete_leave_policy",
        "can_approve_leave",
        "can_create_role",
        "can_create_department",
        "can_edit_department",
        "can_delete_department",
        "can_create_department_role",
        "can_edit_department_role",
        "can_delete_department_role",
        "can_deploy_department_role",
        "can_deploy_department",
    )
    search_fields = ("role_name", "role_description", "company__company_name")

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        permissions_available = ManagePermissionsRole.objects.filter(
            company__id=company_id, is_deleted=False
        ).order_by("-created_at")
        return permissions_available

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class CreateCompanyRolesAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanCreatePermissionRoles,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()

        serializer = CreatePermissionRoleSerializer(
            data=request.data, context={"company_id": company_id}
        )
        serializer.is_valid(raise_exception=True)
        role_name = serializer.validated_data.get("role_name")
        serializer.validated_data["company"] = company
        ManagePermissionsRole.create_role(**serializer.validated_data)
        return Response(
            {"message": f"{role_name} permission created successfully"},
            status=status.HTTP_200_OK,
        )


class EditCompanyRolesAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanEditPermissionRoles,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_id = request.query_params.get("company_uuid")
        role_id = request.query_params.get("role_id")
        if not valid_uuid_check(role_id):
            raise ValidationError({"message": "invalid role ID"})

        company = Company.objects.filter(id=company_id).first()

        role_instance = ManagePermissionsRole.objects.filter(
            id=role_id, company=company, is_deleted=False
        ).first()
        if not role_instance:
            return Response(
                {"message": "role not found"}, status=status.HTTP_400_BAD_REQUEST
            )

        serializer = EditPermissionRoleSerializer(
            role_instance,
            data=request.data,
            partial=True,
            context={"company_id": company_id},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"message": "update permission successfully"}, status=status.HTTP_200_OK
        )


class DeleteCompanyRolesAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeletePermissionRoles,
    ]
    authentication_classes = [CustomUserAuthentication]

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        role_id = request.query_params.get("role_id")
        if not valid_uuid_check(role_id):
            raise ValidationError({"message": "invalid role ID"})

        company = Company.objects.filter(id=company_id).first()

        role_instance = ManagePermissionsRole.objects.filter(
            id=role_id, company=company, is_deleted=False
        ).first()
        if not role_instance:
            return Response(
                {"message": "role not found"}, status=status.HTTP_400_BAD_REQUEST
            )
        role_instance.is_deleted = True
        role_instance.save()
        CompanyEmployeeList.objects.filter(company=company).update(employee_role=None)
        return Response(
            {"message": "Role deleted successfully"}, status=status.HTTP_200_OK
        )


class FetchDepartmentRolesAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = FetchDepartmentRoleSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = ()
    search_fields = ("role_name", "role_description", "company__company_name")

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        permissions_available = ManageDepartmentRole.objects.filter(
            company__id=company_id, is_deleted=False
        ).order_by("-created_at")
        return permissions_available

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class CreateDepartmentRolesAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanCreateDepartmentRoles,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()

        serializer = CreateDepartmentRoleSerializer(
            data=request.data, context={"company_id": company_id}
        )
        serializer.is_valid(raise_exception=True)
        role_name = serializer.validated_data.get("role_name")
        serializer.validated_data["company"] = company
        ManageDepartmentRole.create_role(**serializer.validated_data)
        return Response(
            {"message": f"{role_name} roles created successfully"},
            status=status.HTTP_200_OK,
        )


class EditDepartmentRolesAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanEditDepartmentRoles,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_id = request.query_params.get("company_uuid")
        role_id = request.query_params.get("role_id")
        if not valid_uuid_check(role_id):
            raise ValidationError({"message": "invalid role ID"})

        company = Company.objects.filter(id=company_id).first()

        role_instance = ManageDepartmentRole.objects.filter(
            id=role_id, company=company, is_deleted=False
        ).first()
        if not role_instance:
            return Response(
                {"message": "role not found"}, status=status.HTTP_400_BAD_REQUEST
            )

        serializer = EditDepartmentRoleSerializer(
            role_instance,
            data=request.data,
            partial=True,
            context={"company_id": company_id},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"message": "update role successfully"}, status=status.HTTP_200_OK
        )


class DeleteDepartmentRolesAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeleteDepartmentRoles,
    ]
    authentication_classes = [CustomUserAuthentication]

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        role_id = request.query_params.get("role_id")
        if not valid_uuid_check(role_id):
            raise ValidationError({"message": "invalid role ID"})

        company = Company.objects.filter(id=company_id).first()

        role_instance = ManageDepartmentRole.objects.filter(
            id=role_id, company=company, is_deleted=False
        ).first()
        if not role_instance:
            return Response(
                {"message": "role not found"}, status=status.HTTP_400_BAD_REQUEST
            )
        role_instance.is_deleted = True
        role_instance.save()
        CompanyEmployeeList.objects.filter(company=company).update(
            employee_department_role=None
        )
        return Response(
            {"message": "role deleted successfully"}, status=status.HTTP_200_OK
        )


class FetchAllDepartmentAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListDepartmentSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = ()
    search_fields = ("department_name", "department_head_name")

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        all_department = CompanyDepartmentSettings.objects.filter(
            company__id=company_id, is_deleted=False
        ).order_by("-created_at")
        return all_department

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class DepartmentModuleAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    @staticmethod
    def get(request):
        company_uuid = request.query_params.get("company_uuid")

        all_department = CompanyDepartmentSettings.objects.filter(
            company__id=company_uuid, is_deleted=False
        ).order_by("-created_at")

        serializer = DepartmentModuleSerializer(all_department, many=True)
        try:
            data = {
                "data_type": "EMPLOYEES_DEPARTMENT_LIST",
                "data": serializer.data,
                "total_data_count": len(serializer.data),
            }
        except AttributeError:
            data = {
                "data_type": "EMPLOYEES_DEPARTMENT_LIST",
                "data": [],
                "total_data_count": len(serializer.data),
            }
        return Response(data, status=status.HTTP_200_OK)


class FetchSingleDepartmentAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = self.request.query_params.get("company_uuid")
        department_id = self.request.query_params.get("department_id")
        if not valid_uuid_check(department_id):
            return Response(
                {"message": "invalid department ID"}, status=status.HTTP_400_BAD_REQUEST
            )
        all_department = CompanyDepartmentSettings.objects.filter(
            id=department_id, company__id=company_id, is_deleted=False
        ).first()
        serializer = ListDepartmentSerializer(all_department)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AdminEmployeeDepartmentRoleAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployDepartmentRole,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if not employee_ins:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = AdminEmployeeDepartmentRoleSerializer(
            data=request.data, context={"company_uuid": company_uuid}
        )
        serializer.is_valid(raise_exception=True)

        role_id = serializer.validated_data.get("role_id")
        role_ins = ManageDepartmentRole.objects.filter(
            id=role_id, company__id=company_uuid, is_deleted=False
        ).last()
        employee_ins.employee_department_role = role_ins
        employee_ins.save()

        response = {"message": f"employee department role added"}
        return Response(response, status=status.HTTP_200_OK)


class AddEmployeeDepartmentRoleAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployDepartmentRole,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        role_id = request.query_params.get("role_id")

        if not valid_uuid_check(role_id):
            return Response(
                {"message": "invalid department ID"}, status=status.HTTP_400_BAD_REQUEST
            )

        serializer = MultipleEmployeeDepartmentSerializer(
            data=request.data,
            context={"company_uuid": company_uuid, "role_id": role_id},
        )
        serializer.is_valid(raise_exception=True)

        employee_department_data = serializer.validated_data.get(
            "employee_department_data"
        )
        for employee_department in employee_department_data:
            employee_id = employee_department.get("employee_id")
            employee_ins = CompanyEmployeeList.objects.filter(
                id=employee_id, company__id=company_uuid, is_deleted=False
            ).first()

            role_ins = ManageDepartmentRole.objects.filter(
                id=role_id, company__id=company_uuid, is_deleted=False
            ).first()
            employee_ins.employee_department_role = role_ins
            employee_ins.save()
        response = {"message": "department role added successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class AddEmployeeRoleAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployEmployeeRole,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        role_id = request.query_params.get("role_id")

        if not valid_uuid_check(role_id):
            return Response(
                {"message": "invalid role ID"}, status=status.HTTP_400_BAD_REQUEST
            )

        serializer = MultipleEmployeeRoleSerializer(
            data=request.data,
            context={"company_uuid": company_uuid, "role_id": role_id},
        )
        serializer.is_valid(raise_exception=True)

        employee_role_data = serializer.validated_data.get(
            "employee_role_data"
        )
        for employee_role in employee_role_data:
            employee_id = employee_role.get("employee_id")
            employee_ins = CompanyEmployeeList.objects.filter(
                id=employee_id, company__id=company_uuid, is_deleted=False
            ).first()

            role_ins = ManagePermissionsRole.objects.filter(
                id=role_id, company__id=company_uuid, is_deleted=False
            ).first()
            employee_ins.employee_role = role_ins
            employee_ins.save()
        response = {"message": "employee(s) role added successfully"}
        return Response(response, status=status.HTTP_200_OK)


class RemoveMultipleEmployeeDepartmentRoleAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployDepartmentRole,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        role_id = request.query_params.get("role_id")
        serializer = MultipleEmployeeDepartmentRoleSerializer(
            data=request.data,
            context={"company_uuid": company_uuid, "role_id": role_id},
        )
        serializer.is_valid(raise_exception=True)

        employee_department_data = serializer.validated_data.get(
            "employee_department_data"
        )
        for employee_department in employee_department_data:
            employee_id = employee_department.get("employee_id")
            employee_ins = CompanyEmployeeList.objects.filter(
                id=employee_id, company__id=company_uuid, is_deleted=False
            ).first()
            employee_ins.employee_department_role = None
            employee_ins.save()

        response = {"message": "department role removed successfully"}
        return Response(response, status=status.HTTP_200_OK)
    
class RemoveMultipleEmployeeRoleAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployEmployeeRole,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        role_id = request.query_params.get("role_id")
        serializer = MultipleEmployeeUserRoleSerializer(
            data=request.data,
            context={"company_uuid": company_uuid, "role_id": role_id},
        )
        serializer.is_valid(raise_exception=True)

        employee_role_data = serializer.validated_data.get(
            "employee_role_data"
        )
        for employee in employee_role_data:
            employee_id = employee.get("employee_id")
            employee_ins = CompanyEmployeeList.objects.filter(
                id=employee_id, company__id=company_uuid, is_deleted=False
            ).first()
            employee_ins.employee_role = None
            employee_ins.save()

        response = {"message": "employee(s) role removed successfully"}
        return Response(response, status=status.HTTP_200_OK)


class AdminRemoveEmployeeDepartmentRoleAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployDepartmentRole,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if not employee_ins:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        employee_ins.employee_department_role = None
        employee_ins.save()

        response = {"message": f"successful"}
        return Response(response, status=status.HTTP_200_OK)
    
class AdminRemoveEmployeeRoleAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        CanDeployEmployeeRole,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_id = request.query_params.get("employee_id")
        employee_ins = CompanyEmployeeList.objects.filter(
            id=employee_id, company__id=company_uuid, is_deleted=False
        ).first()
        if not employee_ins:
            return Response(
                {"message": "Employee does not exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        employee_ins.employee_role = None
        employee_ins.save()

        response = {"message": f"successful"}
        return Response(response, status=status.HTTP_200_OK)


class CompanyPermissionEmployeesAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListCompanyEmployeePermissionSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    filterset_fields = ("employee_role",)
    search_fields = (
        "employee_first_name",
        "employee_last_name",
        "employee_email",
        "employee_role__role_name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID"})

        employees = CompanyEmployeeList.objects.filter(
            company__id=company_id, employee_role__isnull=False, is_deleted=False
        ).order_by(Lower("employee_last_name"))
        return employees

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "EMPLOYEES_DATA",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class AddEmployeeExistingPayrollAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        UserHasPin,
        CanRunPayrollPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        company_uuid = request.query_params.get("company_uuid")

        serializer = AddEmployeeExistingPayrollSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        payroll_pin = serializer.validated_data.get("payroll_pin")
        employees = serializer.validated_data.get("employees")

        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=payroll_pin
        )
        if not check_pin:
            return Response(
                {"status": False, "message": "Payroll pin is incorrect!"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # company_ins = Company.objects.get(id=company_uuid)
        run_payroll = PayrollTable.add_employee_existing_payroll(
            user=request.user,
            company_uuid=company_uuid,
            employees=employees,
        )
        if not run_payroll["status"]:
            return Response(run_payroll, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(run_payroll, status=status.HTTP_200_OK)


class AddBonusExistingPayrollAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, IsStaff]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        company_uuid = request.query_params.get("company_uuid")

        serializer = AddBonusExistingPayrollSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        employees = serializer.validated_data.get("employees")
        all_payroll_qs = PayrollTable.objects.filter(
            company__id=company_uuid,
            payroll_deleted=False,
            status__in=["APPROVAL", "DISBURSE", "OMITTED"],
        )
        success_request = []
        unsuccessful_request = []
        for this_employee in employees:
            email = this_employee["email"]
            amount = this_employee["amount"]
            this_employee_qs = all_payroll_qs.filter(email=email).first()
            if this_employee_qs:
                if this_employee_qs.other_amount > 0:
                    unsuccessful_request.append(
                        {
                            "email": email,
                            "message": "bonus already applied",
                        }
                    )
                else:
                    this_employee_qs.other_amount = amount
                    this_employee_qs.payable_amount = round_amount(
                        this_employee_qs.payable_amount + amount
                    )
                    this_employee_qs.save()
                    success_request.append(email)
            else:
                unsuccessful_request.append(
                    {
                        "email": email,
                        "message": "employee does not exist in the company payroll",
                    }
                )

        response = {
            "message": "success",
            "data": {
                "successful_request": success_request,
                "unsuccessful_request": unsuccessful_request,
            },
        }
        return Response(response, status=status.HTTP_200_OK)

class EmployeeInviteOnboardingAPIView(APIView):
    # permission_classes = [IsAuthenticated, UserIsActive, AdminPermission]
    # authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            return Response({"message":"invalid company_id"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            company_ins = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"error": "Company does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        employee_alternate_email = request.data.get("employee_alternate_email")

        email_pattern = re.compile(r"^[\w\.-]+@[\w\.-]+\.\w+$")
     
        # Email validation
        if not employee_alternate_email or not email_pattern.match(employee_alternate_email):
            return Response({"message": "invalid email address"}, status=status.HTTP_400_BAD_REQUEST)

        pay_schedule = None
        pay_run = None
        employee_type = None

        company_settings = CompanyPayrollSettings.objects.filter(company=company_ins).last()
        if company_settings:
            pay_schedule = company_settings.pay_schedule
            pay_run = company_settings.pay_run
            employee_type = company_settings.employee_type
       
        employee_det = CompanyEmployeeList.objects.filter(company=company_id,
                                                              employee_email=employee_alternate_email, is_deleted=False).first()
        if employee_det:
            return Response({"message": "employee already exist in company"}, status=status.HTTP_400_BAD_REQUEST)

        this_employee = CompanyEmployeeList.objects.create(
            employer=company_ins.user,
            company=company_ins,
            added_by=company_ins.user,
            employee_email=employee_alternate_email,
            employee_status="NOT_JOINED"
        )

        this_employee.pay_schedule = pay_schedule
        this_employee.pay_run = pay_run
        this_employee.staff_type = employee_type
        this_employee.save()

        employee_ins = CompanyEmployeeOnboardingForm.objects.create(
            added_by=company_ins.user,
            employee_company=company_ins,
            employee_email=employee_alternate_email,
        )

        serializer = EmployeeInvitedOnboardingSerializer(employee_ins, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        company_name = company_ins.company_name.replace(" ","%20")
        send_email.delay(recipient=employee_alternate_email, subject="Payroll Invite",
            template_dir="team_invite.html",
            team_name="PAYROLL", company_name=company_ins.company_name,
            call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_ins.id}/{company_name}/salary-dashboard")
        employee_ins.is_completed = True
        employee_ins.approval_status = "AWAITING_APPROVAL"
        employee_ins.save()
        return Response({"message": "submitted successfully"}, status=status.HTTP_200_OK)

class OnboardUserAPIView(APIView):
    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            return Response({"message":"invalid company_id"}, status=status.HTTP_400_BAD_REQUEST)
        try:
            company_ins = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"error": "Company does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = OnboardUserSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data.get("email")
        employee_first_name = serializer.validated_data.get("first_name")
        employee_last_name = serializer.validated_data.get("last_name")
        employee_phone_number = serializer.validated_data.get("phone_number")
        username = serializer.validated_data.get("username")
        state = serializer.validated_data.get("state")
        lga = serializer.validated_data.get("lga")
        nearest_landmark = serializer.validated_data.get("nearest_landmark")
        street = serializer.validated_data.get("street")
        gender = serializer.validated_data.get("gender")
        
        # if User.username_exist(username):
        #     return Response({"message": "username already exist"}, status=status.HTTP_400_BAD_REQUEST)

        pay_schedule = None
        pay_run = None
        employee_type = None

        company_settings = CompanyPayrollSettings.objects.filter(company=company_ins).last()
        if company_settings:
            pay_schedule = company_settings.pay_schedule
            pay_run = company_settings.pay_run
            employee_type = company_settings.employee_type
       

        user_instance = User.objects.filter(email=email).first()

        if user_instance:

            employee_det = CompanyEmployeeList.objects.filter(company=company_ins,
                                                              employee_email=email, is_deleted=False).first()
            if employee_det:
                return Response({"message": "user already exist in company"}, status=status.HTTP_400_BAD_REQUEST)

            else:
                this_employee = CompanyEmployeeList.objects.create(
                    employer=company_ins.user,
                    company=company_ins,
                    added_by=company_ins.user,
                    employee_email=email,
                    employee_status = "NOT_JOINED",
                    is_invited=True
                )

                this_employee.employee_first_name = user_instance.first_name.title() if user_instance.first_name else ""
                this_employee.employee_last_name = user_instance.last_name.title() if user_instance.last_name else ""
                this_employee.pay_schedule = pay_schedule
                this_employee.pay_run = pay_run
                this_employee.staff_type = employee_type
                this_employee.save()

                employee_ins = CompanyEmployeeOnboardingForm.objects.create(
                    added_by=company_ins.user,
                    employee_company=company_ins,
                    employee_email=email,
                )

                employee_ins.employee_first_name = employee_first_name
                employee_ins.employee_last_name = employee_last_name
                employee_ins.employee_phone_number = employee_phone_number
                employee_ins.save()

                company_name = company_ins.company_name.replace(" ","%20")
                send_email.delay(recipient=email, subject="Payroll Invite",
                    template_dir="team_invite.html",
                    team_name="PAYROLL", company_name=company_ins.company_name,
                    call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_ins.id}/{company_name}/salary-dashboard")
                employee_ins.is_completed = True
                employee_ins.approval_status = "AWAITING_APPROVAL"
                employee_ins.save()
            agency_data = {'success': True, 'data': {'error': False, 'status': '201', 'message': 'successful', 'data': {}}} 
            response = agency_data
        else:
            agency_data = CompanyEmployeeList.agency_banking_onboarding(**serializer.validated_data)
            if not agency_data.get("success"):
                return Response(agency_data, status=status.HTTP_400_BAD_REQUEST)
            
            user_instance, created = User.objects.get_or_create(
                email=email,
                defaults={
                    'phone_no': employee_phone_number,
                    'first_name': employee_first_name,
                    'last_name': employee_last_name,
                    'state': state,
                    'lga': lga,
                    'nearest_landmark': nearest_landmark,
                    'street': street,
                    'gender': gender,
                }
            )

            employee_det = CompanyEmployeeList.objects.filter(company=company_ins,
                                                              employee_email=email, is_deleted=False).first()
            if not employee_det:
                this_employee = CompanyEmployeeList.objects.create(
                        employer=company_ins.user,
                        company=company_ins,
                        added_by=company_ins.user,
                        employee_email=email,
                        employee_status = "NOT_JOINED",
                        is_invited=True
                    )

                this_employee.employee_first_name = user_instance.first_name.title() if user_instance.first_name else ""
                this_employee.employee_last_name = user_instance.last_name.title() if user_instance.last_name else ""
                this_employee.pay_schedule = pay_schedule
                this_employee.pay_run = pay_run
                this_employee.staff_type = employee_type
                this_employee.save()

                employee_ins = CompanyEmployeeOnboardingForm.objects.create(
                    added_by=company_ins.user,
                    employee_company=company_ins,
                    employee_email=email,
                )

                employee_ins.employee_first_name = employee_first_name
                employee_ins.employee_last_name = employee_last_name
                employee_ins.employee_phone_number = employee_phone_number
                employee_ins.save()

                company_name = company_ins.company_name.replace(" ","%20")
                send_email.delay(recipient=email, subject="Payroll Invite",
                    template_dir="team_invite.html",
                    team_name="PAYROLL", company_name=company_ins.company_name,
                    call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_ins.id}/{company_name}/salary-dashboard")
                employee_ins.is_completed = True
                employee_ins.approval_status = "AWAITING_APPROVAL"
                employee_ins.save()
            response = agency_data
        return Response(response, status=status.HTTP_200_OK)
    
class OnboardPhoneUserAPIView(APIView):
    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            return Response({"message":"invalid company_id"}, status=status.HTTP_400_BAD_REQUEST)
        try:
            company_ins = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"error": "Company does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = OnboardPhoneUserSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        employee_first_name = serializer.validated_data.get("first_name")
        employee_last_name = serializer.validated_data.get("last_name")
        employee_phone_number = serializer.validated_data.get("phone_number")
        username = serializer.validated_data.get("username")
        state = serializer.validated_data.get("state")
        lga = serializer.validated_data.get("lga")
        nearest_landmark = serializer.validated_data.get("nearest_landmark")
        street = serializer.validated_data.get("street")
        gender = serializer.validated_data.get("gender")

        employee_phone_number = User.format_number_from_back_234(employee_phone_number)
        if employee_phone_number is None:
            return Response({"message":"invalid phone number"}, status=status.HTTP_400_BAD_REQUEST)

        email = f"{employee_phone_number}@paybox360.com"

        # if User.username_exist(username):
        #     return Response({"message": "username already exist"}, status=status.HTTP_400_BAD_REQUEST)

        pay_schedule = None
        pay_run = None
        employee_type = None

        company_settings = CompanyPayrollSettings.objects.filter(company=company_ins).last()
        if company_settings:
            pay_schedule = company_settings.pay_schedule
            pay_run = company_settings.pay_run
            employee_type = company_settings.employee_type
       

        user_instance = User.objects.filter(email=email).first()

        if user_instance:

            employee_det = CompanyEmployeeList.objects.filter(company=company_ins,
                                                              employee_email=email, is_deleted=False).first()
            if employee_det:
                return Response({"message": "user already exist in company"}, status=status.HTTP_400_BAD_REQUEST)

            else:
                this_employee = CompanyEmployeeList.objects.create(
                    employer=company_ins.user,
                    company=company_ins,
                    added_by=company_ins.user,
                    employee_email=email,
                    employee_status = "NOT_JOINED",
                    is_invited=True
                )

                this_employee.employee_first_name = user_instance.first_name.title() if user_instance.first_name else ""
                this_employee.employee_last_name = user_instance.last_name.title() if user_instance.last_name else ""
                this_employee.pay_schedule = pay_schedule
                this_employee.pay_run = pay_run
                this_employee.staff_type = employee_type
                this_employee.save()

                employee_ins = CompanyEmployeeOnboardingForm.objects.create(
                    added_by=company_ins.user,
                    employee_company=company_ins,
                    employee_email=email,
                )

                employee_ins.employee_first_name = employee_first_name
                employee_ins.employee_last_name = employee_last_name
                employee_ins.employee_phone_number = employee_phone_number
                employee_ins.save()

                company_name = company_ins.company_name.replace(" ","%20")
                # send_email.delay(recipient=email, subject="Payroll Invite",
                #     template_dir="team_invite.html",
                #     team_name="PAYROLL", company_name=company_ins.company_name,
                #     call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_ins.id}/{company_name}/salary-dashboard")
                employee_ins.is_completed = True
                employee_ins.approval_status = "AWAITING_APPROVAL"
                employee_ins.save()
            agency_data = {'success': True, 'data': {'error': False, 'status': '201', 'message': 'successful', 'data': {}}} 
            response = agency_data
        else:
            agency_data = CompanyEmployeeList.agency_banking_phone_onboarding(**serializer.validated_data)
            if not agency_data.get("success"):
                return Response(agency_data, status=status.HTTP_400_BAD_REQUEST)
            
            user_instance, created = User.objects.get_or_create(
                email=email,
                defaults={
                    'phone_no': employee_phone_number,
                    'first_name': employee_first_name,
                    'last_name': employee_last_name,
                    'state': state,
                    'lga': lga,
                    'nearest_landmark': nearest_landmark,
                    'street': street,
                    'gender': gender,
                }
            )

            employee_det = CompanyEmployeeList.objects.filter(company=company_ins,
                                                              employee_email=email, is_deleted=False).first()
            if not employee_det:
                this_employee = CompanyEmployeeList.objects.create(
                        employer=company_ins.user,
                        company=company_ins,
                        added_by=company_ins.user,
                        employee_email=email,
                        employee_status = "NOT_JOINED",
                        is_invited=True
                    )

                this_employee.employee_first_name = user_instance.first_name.title() if user_instance.first_name else ""
                this_employee.employee_last_name = user_instance.last_name.title() if user_instance.last_name else ""
                this_employee.pay_schedule = pay_schedule
                this_employee.pay_run = pay_run
                this_employee.staff_type = employee_type
                this_employee.save()

                employee_ins = CompanyEmployeeOnboardingForm.objects.create(
                    added_by=company_ins.user,
                    employee_company=company_ins,
                    employee_email=email,
                )

                employee_ins.employee_first_name = employee_first_name
                employee_ins.employee_last_name = employee_last_name
                employee_ins.employee_phone_number = employee_phone_number
                employee_ins.save()

                company_name = company_ins.company_name.replace(" ","%20")
                # send_email.delay(recipient=email, subject="Payroll Invite",
                #     template_dir="team_invite.html",
                #     team_name="PAYROLL", company_name=company_ins.company_name,
                #     call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_ins.id}/{company_name}/salary-dashboard")
                employee_ins.is_completed = True
                employee_ins.approval_status = "AWAITING_APPROVAL"
                employee_ins.save()
            response = agency_data
        return Response(response, status=status.HTTP_200_OK)

class OnboardCheckUserAPIView(APIView):
    def post(self, request):
        email = request.query_params.get("email")
        company_id = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            return Response({"message":"invalid company_id"}, status=status.HTTP_400_BAD_REQUEST)
        email_pattern = re.compile(r"^[\w\.-]+@[\w\.-]+\.\w+$")

        if not email or not email_pattern.match(email):
            response = {
                "success": False,
                "message": "invalid email address",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        email = email.lower()
        agency_data = EmployeeOnboarding.check_agency_onboarding_data(email)
        if agency_data.get("status") in ["400", "500"]:
            return Response(agency_data, status=status.HTTP_400_BAD_REQUEST)
        elif agency_data.get("status") == "200":
            return Response(agency_data, status=status.HTTP_200_OK)
        else:
            fetch_agency_data = EmployeeOnboarding.fetch_liberty_user_via_email(email)
            if fetch_agency_data.get("status") == "200":
                user = fetch_agency_data.get("data").get("user")
                email = user.get("email")
                first_name = user.get("first_name")
                last_name = user.get("last_name")
                phone_number = user.get("phone_number")
                state = user.get("state")
                lga = user.get("lga")
                nearest_landmark = user.get("nearest_landmark")
                street = user.get("street")
                gender = user.get("gender")

                user_instance, created = User.objects.get_or_create(
                    email=email,
                    defaults={
                        'phone_no': phone_number,
                        'first_name': first_name,
                        'last_name': last_name,
                        'state': state,
                        'lga': lga,
                        'nearest_landmark': nearest_landmark,
                        'street': street,
                        'gender': gender,
                    }
                )
                company_ins = Company.objects.get(id=company_id)
                pay_schedule = None
                pay_run = None
                employee_type = None

                company_settings = CompanyPayrollSettings.objects.filter(company=company_ins).last()
                if company_settings:
                    pay_schedule = company_settings.pay_schedule
                    pay_run = company_settings.pay_run
                    employee_type = company_settings.employee_type
            

                user_instance = User.objects.filter(email=email).first()

                if user_instance:

                    employee_det = CompanyEmployeeList.objects.filter(company=company_ins,
                                                                    employee_email=email, is_deleted=False).first()
                    if employee_det:
                        return Response({"message": "user already exist in company"}, status=status.HTTP_400_BAD_REQUEST)

                    else:
                        this_employee = CompanyEmployeeList.objects.create(
                            employer=company_ins.user,
                            company=company_ins,
                            added_by=company_ins.user,
                            employee_email=email,
                            employee_status = "NOT_JOINED",
                            is_invited=True
                        )

                        this_employee.employee_first_name = user_instance.first_name.title() if user_instance.first_name else ""
                        this_employee.employee_last_name = user_instance.last_name.title() if user_instance.last_name else ""
                        this_employee.pay_schedule = pay_schedule
                        this_employee.pay_run = pay_run
                        this_employee.staff_type = employee_type
                        this_employee.save()

                        employee_ins = CompanyEmployeeOnboardingForm.objects.create(
                            added_by=company_ins.user,
                            employee_company=company_ins,
                            employee_email=email,
                        )

                        employee_ins.employee_first_name = user_instance.first_name.title() if user_instance.first_name else ""
                        employee_ins.employee_last_name = user_instance.last_name.title() if user_instance.last_name else ""
                        employee_ins.employee_phone_number = phone_number
                        employee_ins.save()

                        company_name = company_ins.company_name.replace(" ","%20")
                        send_email.delay(recipient=email, subject="Payroll Invite",
                            template_dir="team_invite.html",
                            team_name="PAYROLL", company_name=company_ins.company_name,
                            call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_ins.id}/{company_name}/salary-dashboard")
                        employee_ins.is_completed = True
                        employee_ins.approval_status = "AWAITING_APPROVAL"
                        employee_ins.save()

                response = {
                    "error": False,
                    "status": "201",
                    "message": "onboard successfully",

                }
                return Response(response, status=status.HTTP_201_CREATED)
            else:
                return Response(fetch_agency_data, status=status.HTTP_400_BAD_REQUEST)
            
class OnboardCheckUserPhoneAPIView(APIView):
    def post(self, request):
        phone_number = request.query_params.get("phone_number")
        company_id = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            return Response({"message":"invalid company_id"}, status=status.HTTP_400_BAD_REQUEST)
        
        phone_number = User.format_number_from_back_234(phone_number)
        if phone_number is None:
            return Response({"message":"invalid phone number"}, status=status.HTTP_400_BAD_REQUEST)
        
        agency_data = EmployeeOnboarding.check_agency_onboarding_phone_data(phone_number)
        if agency_data.get("status") in ["400", "500"]:
            return Response(agency_data, status=status.HTTP_400_BAD_REQUEST)
        elif agency_data.get("status") == "200":
            return Response(agency_data, status=status.HTTP_200_OK)
        else:
            fetch_agency_data = EmployeeOnboarding.fetch_liberty_user_via_phone_number(phone_number)
            if fetch_agency_data.get("status") == "200":
                user = fetch_agency_data.get("data").get("user")
                email = user.get("email")
                first_name = user.get("first_name")
                last_name = user.get("last_name")
                phone_number = user.get("phone_number")
                state = user.get("state")
                lga = user.get("lga")
                nearest_landmark = user.get("nearest_landmark")
                street = user.get("street")
                gender = user.get("gender")

                user_instance, created = User.objects.get_or_create(
                    email=email,
                    defaults={
                        'phone_no': phone_number,
                        'first_name': first_name,
                        'last_name': last_name,
                        'state': state,
                        'lga': lga,
                        'nearest_landmark': nearest_landmark,
                        'street': street,
                        'gender': gender,
                    }
                )
                company_ins = Company.objects.get(id=company_id)
                pay_schedule = None
                pay_run = None
                employee_type = None

                company_settings = CompanyPayrollSettings.objects.filter(company=company_ins).last()
                if company_settings:
                    pay_schedule = company_settings.pay_schedule
                    pay_run = company_settings.pay_run
                    employee_type = company_settings.employee_type
            

                user_instance = User.objects.filter(email=email).first()

                if user_instance:

                    employee_det = CompanyEmployeeList.objects.filter(company=company_ins,
                                                                    employee_email=email, is_deleted=False).first()
                    if employee_det:
                        return Response({"message": "user already exist in company"}, status=status.HTTP_400_BAD_REQUEST)

                    else:
                        this_employee = CompanyEmployeeList.objects.create(
                            employer=company_ins.user,
                            company=company_ins,
                            added_by=company_ins.user,
                            employee_email=email,
                            employee_status = "NOT_JOINED",
                            is_invited=True
                        )

                        this_employee.employee_first_name = user_instance.first_name.title() if user_instance.first_name else ""
                        this_employee.employee_last_name = user_instance.last_name.title() if user_instance.last_name else ""
                        this_employee.pay_schedule = pay_schedule
                        this_employee.pay_run = pay_run
                        this_employee.staff_type = employee_type
                        this_employee.save()

                        employee_ins = CompanyEmployeeOnboardingForm.objects.create(
                            added_by=company_ins.user,
                            employee_company=company_ins,
                            employee_email=email,
                        )

                        employee_ins.employee_first_name = user_instance.first_name.title() if user_instance.first_name else ""
                        employee_ins.employee_last_name = user_instance.last_name.title() if user_instance.last_name else ""
                        employee_ins.employee_phone_number = phone_number
                        employee_ins.save()

                        company_name = company_ins.company_name.replace(" ","%20")
                        # send_email.delay(recipient=email, subject="Payroll Invite",
                        #     template_dir="team_invite.html",
                        #     team_name="PAYROLL", company_name=company_ins.company_name,
                        #     call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-view/{company_ins.id}/{company_name}/salary-dashboard")
                        employee_ins.is_completed = True
                        employee_ins.approval_status = "AWAITING_APPROVAL"
                        employee_ins.save()

                response = {
                    "error": False,
                    "status": "201",
                    "message": "onboard successfully",

                }
                return Response(response, status=status.HTTP_201_CREATED)
            else:
                return Response(fetch_agency_data, status=status.HTTP_400_BAD_REQUEST)

        
class EditEmployeePayrollSavingsAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, IsStaff]
    # permission_classes = [IsAuthenticated, UserIsActive]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        company_uuid = request.query_params.get("company_uuid")

        if not valid_uuid_check(company_uuid):
            return Response({"message":"invalid company_id"}, status=status.HTTP_400_BAD_REQUEST)
        try:
            company_ins = Company.objects.get(id=company_uuid)
        except Company.DoesNotExist:
            return Response({"error": "Company does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        CompanyEmployeeList.objects.filter(company=company_ins).update(employee_payable_amount=0)
        edited_payroll_ins = SavingPayrollEditData.objects.create(
            editor = request.user,
            company = company_ins,
            data = request.data
        )
        serializer = EditEmployeeDataFromSavingsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        employees = serializer.validated_data.get("employees")
        all_employee_list = CompanyEmployeeList.objects.filter(
            company__id=company_uuid,
            is_deleted=False
        )
        edited_payroll_ins.interacted = True if all_employee_list else False
        edited_payroll_ins.save()
        
        success_request = []
        unsuccessful_request = []
        for this_employee in employees:
            email = this_employee["email"]
            payable_amount = this_employee["payable_amount"]
            net_amount = this_employee["net_amount"]
            gross_amount = this_employee.get("gross_amount")
            deduction_amount = this_employee.get("deduction_amount")
            bonus_amount = this_employee.get("bonus_amount")
            this_employee_qs = all_employee_list.filter(employee_email=email).first()

            if this_employee_qs:

                this_employee_qs.employee_payable_amount = round_amount(payable_amount)
                this_employee_qs.employee_net_amount = round_amount(net_amount)
                if gross_amount:
                    this_employee_qs.employee_gross_amount = round_amount(gross_amount)
                if deduction_amount:
                    this_employee_qs.employee_other_deductions = round_amount(deduction_amount)
                if bonus_amount:
                    this_employee_qs.employee_other_amount = round_amount(bonus_amount)
                    
                this_employee_qs.save()
                
                success_request.append(email)
            else:
                unsuccessful_request.append(
                    {
                        "email": email,
                        "message": "employee does not exist in the company",
                    }
                )

        response = {
            "message": "success",
            "data": {
                "successful_request": success_request,
                "unsuccessful_request": unsuccessful_request,
            },
        }
        return Response(response, status=status.HTTP_200_OK)

class ResendCompanyPensionAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        serializer = ResendPensionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        bulk_id = serializer.validated_data.get("bulk_id")
        all_payable_id = serializer.validated_data.get("all_payable_id")
        company_data = CompanyDetailsData.objects.filter(
            company__id=company_uuid, bulk_id=bulk_id, is_deleted=False
        ).first()
        if not company_data:
            return Response(
                {
                    "error": "error",
                    "message": "No PFA to disburse",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        # get account and wallet instance of sender
        company_ins = Company.objects.get(id=company_uuid)

        if company_ins.company_wallet_type == "CORPORATE":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="PAYROLL"
                )
            except AccountSystem.DoesNotExist:
                account_ins = None
        elif company_ins.company_wallet_type == "MAIN":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="NON_CORP_PAYROLL"
                )
            except AccountSystem.DoesNotExist:
                account_ins = None
                wallet_balance = 0
        else:
            return Response(
                {
                    "error": "171",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
       
        this_payroll_date = f"{company_data.payroll_year}-{company_data.payroll_month}-01"
        last_parts = this_payroll_date.split("-")
        month_map = {
            "january": 1,
            "february": 2,
            "march": 3,
            "april": 4,
            "may": 5,
            "june": 6,
            "july": 7,
            "august": 8,
            "september": 9,
            "october": 10,
            "november": 11,
            "december": 12,
        }
        # Convert the month name to numerical value
        last_date_month = month_map[last_parts[1].lower()]
        # Create a datetime object
        last_date_object = datetime(
            int(last_parts[0]), last_date_month, int(last_parts[2])
        )
        
        # automate pfa        
        send_automated_pfa.delay(
            company_id=company_uuid, 
            payroll_date=last_date_object, 
            company_details_id=company_data.id,
            user_id=account_ins.user.id,
            account_id=account_ins.id,
            all_payable_id=all_payable_id
        )

        return Response(
            {"message": "pension sent"}, status=status.HTTP_200_OK
        )

class InvitedEmployeeListAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = EmployeeOnboardingSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    # filterset_fields = (
   
    # )
    search_fields = (
        "employee_first_name",
        "employee_last_name",
        "employee_email",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        
        all_new_employees = CompanyEmployeeOnboardingForm.objects.filter(employee_company__id=company_id, approval_status="AWAITING_APPROVAL", is_deleted=False)

        return all_new_employees

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "EMPLOYEES_DATA",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class AllMultiplePayrollAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = UniquePayrollSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    search_fields = (
        "bulk_id",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")

        all_running_payroll = PayrollTable.objects.filter(
                company__id=company_id, status__in=["APPROVAL", "DISBURSE", "OMITTED"], payroll_deleted=False
            ).values_list("bulk_id", flat=True).distinct().order_by("-payroll_date")
        bulk_ids = list(all_running_payroll)
        queryset = CompanyDetailsData.objects.filter(bulk_id__in=bulk_ids).order_by("-date_created")
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class RunMultiplePayrollAPIVIew(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        UserHasPin,
        CanRunPayrollPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):

        company_uuid = request.query_params.get("company_uuid")

        serializer = AddPayrollSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        payroll_pin = serializer.validated_data.get("payroll_pin")
        payroll_date = serializer.validated_data.get("payroll_date")
        # is_recurring = serializer.validated_data.get("is_recurring")
        narration = serializer.validated_data.get("narration")
        payroll_month = serializer.validated_data.get("payroll_month")
        payroll_year = serializer.validated_data.get("payroll_year")

        check_pin = User.check_sender_payroll_pin(
            user=request.user, pincode=payroll_pin
        )
        if not check_pin:
            return Response(
                {"status": False, "message": "Payroll pin is incorrect!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            month_map = {
                "january": 1,
                "february": 2,
                "march": 3,
                "april": 4,
                "may": 5,
                "june": 6,
                "july": 7,
                "august": 8,
                "september": 9,
                "october": 10,
                "november": 11,
                "december": 12,
            }
            if payroll_month not in month_map:
                return Response(
                    {"message": "payroll month not in correct format"},
                    status.HTTP_400_BAD_REQUEST,
                )

            # company_ins = Company.objects.get(id=company_uuid)
            run_payroll = PayrollTable.create_approval_payroll(
                user=request.user,
                company_uuid=company_uuid,
                payroll_date=payroll_date,
                is_recurring=False,
                narration=narration,
                payroll_month=payroll_month,
                payroll_year=payroll_year,
                employees=[],
                multiple_payroll=True,
            )
            if not run_payroll["status"]:
                return Response(run_payroll, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(run_payroll, status=status.HTTP_200_OK)
            
class EmployeeWorkForceAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")

        this_date_time = timezone.now()

        three_years_ago = this_date_time - relativedelta(years=3)
        four_years_ago = this_date_time - relativedelta(years=4)
        seven_years_ago = this_date_time - relativedelta(years=7)
        eight_years_ago = this_date_time - relativedelta(years=8)
        fourteen_years_ago = this_date_time - relativedelta(years=14)
        fifteen_years_ago = this_date_time - relativedelta(years=15)

        all_employee_qs = CompanyEmployeeList.objects.filter(company__id=company_uuid, is_deleted=False)

        zero_to_three_years_employee = all_employee_qs.filter(created_at__gte=three_years_ago, created_at__lte=this_date_time).count() or 0
        four_to_seven_years_employee = all_employee_qs.filter(created_at__lte=four_years_ago, created_at__gte=seven_years_ago).count() or 0
        eight_to_fourteen_years_employee = all_employee_qs.filter(created_at__lte=eight_years_ago, created_at__gte=fourteen_years_ago).count() or 0
        above_fifteen_years_employee = all_employee_qs.filter(created_at__lte=fifteen_years_ago).count() or 0

        data = {
            "zero_to_three_years_employee":zero_to_three_years_employee,
            "four_to_seven_years_employee": four_to_seven_years_employee,
            "eight_to_fourteen_years_employee": eight_to_fourteen_years_employee,
            "above_fifteen_years_employee": above_fifteen_years_employee,
        }
        return Response(data, status=status.HTTP_200_OK)
    
class EmployeeMetricsAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")

        all_employee_qs = CompanyEmployeeList.objects.filter(company__id=company_uuid, is_deleted=False)

        work_force_head_count = all_employee_qs.count() or 0
        work_force_male = all_employee_qs.filter(employee_gender="MALE").count() or 0
        work_force_female = all_employee_qs.filter(employee_gender="FEMALE").count() or 0

        # Calculate the length of service in years (accounting for leap years)
        all_employee_service_qs = all_employee_qs.annotate(
            length_of_service=ExpressionWrapper(
                Extract(Now(), 'epoch') - Extract(F('created_at'), 'epoch'),
                output_field=fields.FloatField()
            ) / (365.25 * 24 * 60 * 60)
        )

        # Get the average length of service in years
        average_length_of_service = all_employee_service_qs.aggregate(
            average_service=Avg('length_of_service')
        )

        # Print the average length of service in years
        average_service_in_years = average_length_of_service['average_service'] if average_length_of_service['average_service'] else 0


        # Calculate the age in years by subtracting birth date from current date and dividing by 365.25
        all_employee_age_qs = all_employee_qs.annotate(
            age=ExpressionWrapper(
                Extract(Now(), 'epoch') - Extract(F('employee_birth_date'), 'epoch'),
                output_field=fields.FloatField()
            ) / (365.25 * 24 * 60 * 60)
            
        )
        # Calculate the average age of employees
        average_age_data = all_employee_age_qs.aggregate(average_age=Avg('age'))
        # Get the average age rounded to one decimal place
        average_age = average_age_data['average_age'] if average_age_data['average_age'] is not None else 0

        data = {
            "work_force_head_count":work_force_head_count,
            "work_force_male": work_force_male,
            "work_force_female": work_force_female,
            "average_length_of_service": f"{average_service_in_years:.1f} years",
            "average_age": f"{average_age:.1f} years",
        }
        return Response(data, status=status.HTTP_200_OK)

class EmployeeDepartmentMetricAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")

        this_date_time = timezone.now()

        all_employee_qs = CompanyEmployeeList.objects.filter(company__id=company_uuid, is_deleted=False, employee_department__isnull=False)
        all_employee_all_amount = all_employee_qs.aggregate(Sum("employee_payable_amount"))["employee_payable_amount__sum"] or 0

        department_data = all_employee_qs.values('employee_department').annotate(count=Count('employee_department'))
        departments = [entry['employee_department'] for entry in department_data]

        all_department_data =  []
        count = 0
        for department in departments:
            this_department = all_employee_qs.filter(employee_department__id=department)
            department_name = this_department.first().employee_department.department_name
            department_amount = this_department.aggregate(Sum("employee_payable_amount"))["employee_payable_amount__sum"] or 0
            try:
                department_percent = (department_amount /  all_employee_all_amount) * 100
            except ZeroDivisionError:
                department_percent = 0
            count += 1

            all_department_data.append({
                "number": count,
                "department_name": department_name,
                "department_amount": department_amount,
                "department_percent":  department_percent
                })
        data = {
            "department_data":all_department_data,
        }
        return Response(data, status=status.HTTP_200_OK)

class CompanyPayrollCalculatorAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company = Company.objects.get(id=company_uuid)
        CompanyPayrollSettings.create_company_payroll_settings(company=company)
        serializer = CompanyPayrollCalculatorSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        net_amount = serializer.validated_data.get("net_amount")
        other_deductions = serializer.validated_data.get("other_deductions")
        other_bonus = serializer.validated_data.get("other_bonus")
        life_insurance = serializer.validated_data.get("life_insurance")
        hmo = serializer.validated_data.get("hmo")
        voluntary_pension = serializer.validated_data.get("voluntary_pension")
        
        custom_tax_band = CompanyTaxBand.objects.filter(company=company, is_active=True).first()
        payroll_settings_ins = CompanyPayrollSettings.objects.filter(company=company).first()
        if payroll_settings_ins:
            if payroll_settings_ins.standard_tax:
                company_tax = "STANDARD_TAX"
            else:
                if custom_tax_band:
                    company_tax = "CUSTOM_TAX"
                else:
                    company_tax = "OFF"
        else:
            if custom_tax_band:
                company_tax = "CUSTOM_TAX"
            else:
                company_tax = "OFF"

        payroll_check_status, pay_settings_check = enable_payroll_settings_calculator_check(company=company, company_tax=company_tax)
        if not payroll_check_status:
            return Response({
                "message": pay_settings_check,
            }, status=status.HTTP_400_BAD_REQUEST)
        
        pay_value = enable_payroll_settings_calculator(
            company=company, 
            company_tax=company_tax, 
            net_calculation_type=pay_settings_check, 
            employee_net_amount=net_amount, 
            other_deductions=other_deductions, 
            other_bonus=other_bonus, 
            life_insurance=life_insurance,
            hmo=hmo, 
            voluntary_pension=voluntary_pension
            )
        return Response(
            {"message": "successful", "data": pay_value},
            status=status.HTTP_200_OK,
        )


class ManualCompanyPensionAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        IsStaff

    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = ManualPensionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise ValidationError({"message": "invalid Company ID"})
        
        pension_list = serializer.validated_data.get("pension_list")
        company_name = serializer.validated_data.get("company_name")
        payroll_date = serializer.validated_data.get("payroll_date")
        employer_code = serializer.validated_data.get("employer_code")
        company_email = serializer.validated_data.get("company_email")
        hr_email = serializer.validated_data.get("hr_email")
         
        
        # get account and wallet instance of sender
        company_ins = Company.objects.get(id=company_uuid)

        if company_ins.company_wallet_type == "CORPORATE":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="PAYROLL"
                )
            except AccountSystem.DoesNotExist:
                account_ins = None
        elif company_ins.company_wallet_type == "MAIN":
            try:
                account_ins = AccountSystem.objects.get(
                    company=company_ins, account_type="NON_CORP_PAYROLL"
                )
            except AccountSystem.DoesNotExist:
                account_ins = None
                wallet_balance = 0
        else:
            return Response(
                {
                    "error": "171",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        wallet_ins = Wallet.get_wallet_instance(account_ins.user, account_ins)
        if wallet_ins is None:
            return Response(
                {
                    "error": "173",
                    "message": "You do not have sufficient balance to make this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        wallet_balance = wallet_ins.balance
        pfa_count = len(pension_list) or 0
        pfa_amount = sum(item['pension_amount'] for item in pension_list)
        total_pfa_charges = pfa_count * 150
        total_pfa_amount = total_pfa_charges + pfa_amount

        if wallet_balance < total_pfa_amount:
            response = {
                "error": "169",
                "message": "You do not have sufficient balance to make this transaction",
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)
     
        # automate pfa        
        manual_send_automated_pfa.delay(
            payroll_date=payroll_date, 
            user_id=account_ins.user.id,
            account_id=account_ins.id,
            pension_list=pension_list,
            employer_code=employer_code,
            company_name=company_name,
            company_email=company_email,
            hr_email=hr_email

        )
        return Response(
            {"message": "pension sent"}, status=status.HTTP_200_OK
        )
    
class ManualCompanyPensionNoPaymentAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        IsStaff

    ]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = ManualPensionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise ValidationError({"message": "invalid Company ID"})
        
        pension_list = serializer.validated_data.get("pension_list")
        company_name = serializer.validated_data.get("company_name")
        payroll_date = serializer.validated_data.get("payroll_date")
        employer_code = serializer.validated_data.get("employer_code")
        company_email = serializer.validated_data.get("company_email")
        hr_email = serializer.validated_data.get("hr_email")
         
        # automate pfa        
        manual_send_automated_pfa_no_payment.delay(
            payroll_date=payroll_date, 
            pension_list=pension_list,
            employer_code=employer_code,
            company_name=company_name,
            company_email=company_email,
            hr_email=hr_email

        )
        return Response(
            {"message": "pension sent"}, status=status.HTTP_200_OK
        )
    
class PayrollTransactionHistory(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyTransactionHistorySerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
       "status", "transaction_type", "transfer_stage"
    )
    search_fields = (
        "transaction_ref", "beneficiary_account_number", "beneficiary_account_name", "beneficiary_wallet_id"
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        company = Company.objects.get(id=company_id)
        
        transaction_data = Transaction.objects.filter(
          user=company.user, payout_type="PAYROLL"
        )
       
        return transaction_data

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "TRANSACTION_HISTORY",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class PensionTransactionHistory(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = CompanyPensionTransactionHistorySerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
       "status", "transaction_type", "transfer_stage"
    )
    search_fields = (
        "transaction_ref", "beneficiary_account_number", "beneficiary_account_name", "beneficiary_wallet_id"
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID"})
        
        transaction_data = Transaction.objects.filter(
          company_id=company_id, payout_type="PENSION", 
          transaction_type="BANK_TRANSFER", 
          status__in=["PENDING", "SUCCESSFUL"]
        )
       
        return transaction_data

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "PENSION_TRANSACTION_HISTORY",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)

class EmployeeVerifyUtilityBillAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()

        if not employee:
            return Response(
                {"message": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        if employee.is_phone_number_verified:
            return Response(
                {"message": "employee data has already been verified"}, status=status.HTTP_400_BAD_REQUEST
            )

        employee_state = employee.employee_state
        employee_city = employee.employee_city
        employee_town = employee.employee_town
        employee_bus_stop = employee.employee_bus_stop
        employee_street_name = employee.employee_street_name
        # "employee_house_no",
        if employee_state is None:
            return Response({"message": "employee state is required"}, status=status.HTTP_400_BAD_REQUEST)
        if employee_city is None:
            return Response({"message": "employee city is required"}, status=status.HTTP_400_BAD_REQUEST)
        if employee_bus_stop is None:
            return Response({"message": "employee bus stop is required"}, status=status.HTTP_400_BAD_REQUEST)
        if employee_town is None:
            return Response({"message": "employee town is required"}, status=status.HTTP_400_BAD_REQUEST)
        if employee_street_name is None:
            return Response({"message": "employee street name is required"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = VerifyUtilityBillSerializer(employee, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        utility_bill = serializer.validated_data.pop("utility_bill")
        utility_message = serializer.validated_data.pop("utility_message")
        utility_status = serializer.validated_data.pop("utility_status")
        # verification_image_data = serializer.validated_data.pop("verification_image_data")
        utility_date = serializer.validated_data.pop("utility_date")
        utility_address = serializer.validated_data.pop("utility_address")

        const_data = ConstantTable.get_constant_instance()

        user_home_address = f"{employee_street_name} {employee_bus_stop} {employee_city} {employee_state}"
        try:
            address_match_score = address_match_percentage(utility_address, user_home_address)
        except AttributeError:
            return Response(
                {"message": "an error occurred processing this image"}, status=status.HTTP_400_BAD_REQUEST
            )
        # employee.employee_utility_bill = utility_bill
        # employee.employee_utility_bill_address_match_score = address_match_score
        # employee.employee_utility_bill_message = utility_message
        # employee.save()
        verification_ins = serializer.save()
        verification_ins.employee_utility_bill_address_match_score = address_match_score
        verification_ins.employee_utility_bill_message = utility_message
        verification_ins.save()

        if utility_status is False:
            return Response(
                {"message": "utility address does not match"}, status=status.HTTP_400_BAD_REQUEST
            )
                    
        if utility_date == "N/A":
            return Response(
                {"message": "utility bill should have a valid purchase date"}, status=status.HTTP_400_BAD_REQUEST
            )

        if utility_address == "N/A":
            return Response(
                {"message": "utility bill has no valid address"}, status=status.HTTP_400_BAD_REQUEST
            )

        is_expired = is_utility_bill_expired(utility_date, const_data.allowed_utility_month)
        if is_expired:
            return Response(
                {"message": f"utility bill should be within {const_data.allowed_utility_month} month of purchase"}, status=status.HTTP_400_BAD_REQUEST
            )

        if address_match_score >= const_data.user_address_match_percentage:
            employee.is_phone_number_verified = True
            employee.save()

            response = {
                "message": "Employee data has been verified"
            }
            return Response(response, status=status.HTTP_200_OK)

        else:
            return Response(
                {"message": "Employee address does not match"}, status=status.HTTP_400_BAD_REQUEST
            )
        
class EmployeeSendGuarantorVerificationAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user

        employee_data = CompanyEmployeeList.objects.filter(company__id=company_uuid)

        employee = employee_data.filter(employee=employee_user, is_active=True).first()

        if not employee:
            return Response(
                {"message": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        if employee.is_guarantor_phone_number_verified:
            return Response(
                {"message": "employee guarantor has already been verified"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        if employee.employee_guarantor_phone_number is None:
            return Response(
                {"message": "employee guarantor phone number is empty"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        phone_number = User.format_number_from_back_234(employee.employee_guarantor_phone_number)
        if phone_number is None:
            return Response(
                {"message": "employee guarantor phone number is incorrect"}, status=status.HTTP_400_BAD_REQUEST
            )

        if employee_data.filter(employee_guarantor_phone_number=phone_number, is_guarantor_phone_number_verified=True).exists():
            return Response(
                    {"message": "Guarantor phone number has already been verified by another user"}, status=status.HTTP_400_BAD_REQUEST
                )
        
        if request.user.phone_no:
            if request.user.phone_no == phone_number:
                return Response(
                    {"message": "you cannot use your phone number as a guarantor"}, status=status.HTTP_400_BAD_REQUEST
                )

        employee_phone_number = employee.employee_phone_number
        if employee_phone_number:
            employee_phone_number = User.format_number_from_back_234(employee_phone_number)
            if employee_phone_number is not None:
                if employee_phone_number == phone_number:
                    return Response(
                        {"message": "employee phone number is the same with guarantor"}, status=status.HTTP_400_BAD_REQUEST
                    )
        guarantor_invite_id = generate_guarantor_invite_id()
        VerificationData.create_verification_dump(
            employee=employee,
            guarantor_invite_id=guarantor_invite_id
        )

        employee.guarantor_invite_id = guarantor_invite_id
        employee.employee_guarantor_phone_number = phone_number
        employee.save()
        send_verification = send_verification_sms(phone=phone_number, invite_id=guarantor_invite_id)

        response = {
            "message": "message has been sent to your guarantor"
        }
        return Response(response, status=status.HTTP_200_OK)


class GuarantorVerifyUtilityBillAPIView(APIView):
    def post(self, request):
        invite_id = request.query_params.get("invite_id")
        employee = CompanyEmployeeList.objects.filter(
            guarantor_invite_id=invite_id
        ).first()

        if employee is None:
            return Response(
                {"message": "invalid verification"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        if employee.is_guarantor_phone_number_verified:
            return Response(
                {"message": "guarantor data has already been verified"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = VerifyGuarantorUtilityBillSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
    
        guarantor_state = serializer.validated_data.get("guarantor_state")
        guarantor_city = serializer.validated_data.get("guarantor_city")
        guarantor_town = serializer.validated_data.get("guarantor_town")
        guarantor_bus_stop = serializer.validated_data.get("guarantor_bus_stop")
        guarantor_street_name = serializer.validated_data.get("guarantor_street_name")


        utility_bill = serializer.validated_data.get("utility_bill")
        utility_message = serializer.validated_data.get("utility_message")
        utility_status = serializer.validated_data.get("utility_status")
        verification_image_data = serializer.validated_data.get("verification_image_data")
        utility_date = serializer.validated_data.get("utility_date")
        utility_address = serializer.validated_data.get("utility_address")

        const_data = ConstantTable.get_constant_instance()
        guarantor_home_address = f"{guarantor_street_name} {guarantor_bus_stop} {guarantor_city} {guarantor_state}"
        employee_home_address = f"{employee.employee_street_name} {employee.employee_bus_stop} {employee.employee_city} {employee.employee_state}"

        employee_address_match_score = address_match_percentage(employee_home_address, guarantor_home_address)

        employee.employee_guarantor_state = guarantor_state
        employee.employee_guarantor_city = guarantor_city 
        employee.employee_guarantor_town = guarantor_town
        employee.employee_guarantor_bus_stop = guarantor_bus_stop
        employee.employee_guarantor_street_name = guarantor_street_name
        employee.save()

        if employee_address_match_score >= const_data.user_guarantor_address_match_percentage:
            return Response(
                {"message": "employee and guarantor address match"}, status=status.HTTP_400_BAD_REQUEST
            )
        try:
            address_match_score = address_match_percentage(utility_address, guarantor_home_address)
        except AttributeError:
            return Response(
                {"message": "an error occurred processing this image"}, status=status.HTTP_400_BAD_REQUEST
            )
        employee.guarantor_utility_bill = utility_bill
        employee.guarantor_utility_bill_address_match_score = address_match_score
        employee.guarantor_utility_bill_message = utility_message
        employee.save()


        if utility_status is False:
            return Response(
                {"message": "utility address does not match"}, status=status.HTTP_400_BAD_REQUEST
            )
                    
        if utility_date == "N/A":
            return Response(
                {"message": "utility bill should have a valid purchase date"}, status=status.HTTP_400_BAD_REQUEST
            )

        if utility_address == "N/A":
            return Response(
                {"message": "utility bill has no valid address"}, status=status.HTTP_400_BAD_REQUEST
            )

        is_expired = is_utility_bill_expired(utility_date, const_data.allowed_utility_month)
        if is_expired:
            return Response(
                {"message": f"utility bill should be within {const_data.allowed_utility_month} month of purchase"}, status=status.HTTP_400_BAD_REQUEST
            )
    
        if address_match_score >= const_data.user_address_match_percentage:
            employee.is_guarantor_phone_number_verified = True
            employee.save()

            response = {
                "message": "Guarantor data has been verified"
            }
            return Response(response, status=status.HTTP_200_OK)

        else:
            return Response(
                {"message": "Guarantor address does not match"}, status=status.HTTP_400_BAD_REQUEST
            )

class EmployeeVerificationAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee is None:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )
        serializer = EmployeeVerificationSerializer(employee)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_active=True
        ).first()
        if employee is None:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )
        serializer = EmployeeVerificationSerializer(
            employee,
            data=request.data,
            partial=True,
            context={"employee_instance": employee},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"message": "employee updated successfully"}, status=status.HTTP_200_OK
        )
    
class SendPreviousEmployerEmailAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        employee_user = request.user
        employee = CompanyEmployeeList.objects.filter(
            company__id=company_uuid, employee=employee_user, is_deleted=False
        ).first()
        if employee is None:
            return Response(
                {"error": "employee does not exist"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        if employee.previous_employer_verified is True:
            return Response(
                {"error": "verification already completed"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = PreviousEmployerEmailSerializer(employee, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        previous_employer_email = serializer.validated_data.get("previous_employer_email")
        previous_employer_name = serializer.validated_data.get("previous_employer_name")
        serializer.validated_data["previous_employer_triggered"] = True
        serializer.save()

        this_company_name = employee.company.company_name
        this_previous_company_name = previous_employer_name

        company_name = this_company_name.replace(" ","%20") 
        previous_employer_name = this_previous_company_name.replace(" ","%20")

        verification = PreviousCompanyEmployeeVerifications.objects.create(
            employee=employee,
            company=employee.company,
            previous_employer_email=previous_employer_email
        )

        send_email.delay(
            recipient=previous_employer_email, 
            subject="Employee Verification Request",
            company_name=employee.company.company_name,
            employee_full_name=employee.full_name,
            previous_company_name=this_previous_company_name,
            template_dir="previous_employer_verification.html",
            form_invite_link=f"https://www.home.paybox360.com/previous-company-verification/{verification.id}/{company_name}/"
        )

        return Response(
            {"message": "previous employer verification sent"}, status=status.HTTP_200_OK
        )
    
class PreviousEmployerVerificationAPIView(APIView):

    def post(self, request):
        verification_id = request.query_params.get("verification_id")

        try:
            verification = PreviousCompanyEmployeeVerifications.objects.get(id=verification_id)
        except PreviousCompanyEmployeeVerifications.DoesNotExist:
            return Response(
                {"error": "invalid verification"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        if verification.employee.previous_employer_verified is True:
            return Response(
                {"error": "verification already completed"}, status=status.HTTP_400_BAD_REQUEST
            )
        serializer = PreviousEmployerVerificationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        confirm_employee_full_name = serializer.validated_data.get("confirm_employee_full_name")
        confirm_employee_job_title = serializer.validated_data.get("confirm_employee_job_title")
        confirm_employee_start_date = serializer.validated_data.get("confirm_employee_start_date")
        confirm_employee_end_date = serializer.validated_data.get("confirm_employee_end_date")
        confirm_employee_has_resigned = serializer.validated_data.get("confirm_employee_has_resigned")
        confirm_employee_resignation_method = serializer.validated_data.get("confirm_employee_resignation_method")
        confirm_employee_unresolved_issues = serializer.validated_data.get("confirm_employee_unresolved_issues")
        confirm_employee_rehire_eligiblility = serializer.validated_data.get("confirm_employee_rehire_eligiblility")

        verification.employee.confirm_employee_full_name = confirm_employee_full_name
        verification.employee.confirm_employee_job_title = confirm_employee_job_title
        verification.employee.confirm_employee_start_date = confirm_employee_start_date
        verification.employee.confirm_employee_end_date = confirm_employee_end_date
        verification.employee.confirm_employee_has_resigned = confirm_employee_has_resigned
        verification.employee.confirm_employee_resignation_method = confirm_employee_resignation_method
        verification.employee.confirm_employee_unresolved_issues = confirm_employee_unresolved_issues
        verification.employee.confirm_employee_rehire_eligiblility = confirm_employee_rehire_eligiblility
        verification.employee.previous_employer_verified = True
        verification.employee.save()
        return Response(
            {"message": "verification successful"}, status=status.HTTP_200_OK
        )

class VerifiedPreviousComanyEmployeesAPIView(generics.ListAPIView):
    permission_classes = [
        IsAuthenticated,
        UserIsActive,
        EmployeeIsActive,
        AdminPermission,
    ]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomPagination
    serializer_class = ListPreviousEmployerVerificationSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_deleted",
        "employee_department",
        "employee_pay_grade",
        "confirm_employee_has_resigned"

    )
    search_fields = (
        "employee_first_name",
        "employee_last_name",
        "employee_email",
        "previous_employer_email",
        "previous_employer_name",
        "confirm_employee_full_name",
    )

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        if not valid_uuid_check(company_id):
            raise ValidationError({"message": "invalid Company ID"})

        employees = CompanyEmployeeList.objects.filter(
            company__id=company_id, previous_employer_verified=True
        ).order_by(Lower("employee_last_name"))
        return employees

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "data_type": "EMPLOYEES_DATA",
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
    
class FetchPenccoPFAsAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        
        all_pfa = PenccoAPI.get_all_pfa()
        return Response(all_pfa, status=status.HTTP_200_OK)
    
class GetPenccoScheduleAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        schedule_id = request.query_params.get("schedule_id")
        if not schedule_id:
            return Response(
                {"params": "schedule_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )
        pension_data = SendPensionData.objects.filter(
            id=schedule_id, company__id=company_id
        ).first()
        if not pension_data:
            return Response(
                {"message": "no pension data found for this schedule"}, status=status.HTTP_404_NOT_FOUND
            )

        try:
            this_schedule_id = int(pension_data.pencco_schedule_id)
        except (ValueError, TypeError): 
            return Response(
                {"messsage": "pension data not available"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        all_pfa = PenccoAPI.get_pension_schedule(schedule_id=this_schedule_id)
        return Response(all_pfa, status=status.HTTP_200_OK)
    
class GetEmployeePenccoScheduleAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        schedule_id = request.query_params.get("schedule_id")
        pension_pin = request.query_params.get("pension_pin")
        search_params = request.query_params.get("search_params")
        page = request.query_params.get("page", 1)
        if not schedule_id:
            return Response(
                {"params": "schedule_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )
        pension_data = SendPensionData.objects.filter(
            id=schedule_id, company__id=company_id
        ).first()
        if not pension_data:
            return Response(
                {"message": "no pension data found for this schedule"}, status=status.HTTP_404_NOT_FOUND
            )

        try:
            this_schedule_id = int(pension_data.pencco_schedule_id)
        except (ValueError, TypeError): 
            return Response(
                {"messsage": "pension data not available"}, status=status.HTTP_400_BAD_REQUEST
            )
        
        all_pfa = PenccoAPI.get_employee_pension_schedule(
            schedule_id=this_schedule_id, 
            pension_pin=pension_pin, 
            search_params=search_params,
            page=page,
        )
        return Response(all_pfa, status=status.HTTP_200_OK)
    
class SendManualPenccoScheduleAPIView(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, IsStaff]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        bulk_id = request.query_params.get("bulk_id")

        company_data = CompanyDetailsData.objects.filter(
            company__id=company_id, bulk_id=bulk_id, is_deleted=False
        ).first()
        if not company_data:
            return Response(
                {
                    "error": "error",
                    "message": "No available Pension data for this company",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
       
        this_payroll_date = f"{company_data.payroll_year}-{company_data.payroll_month}-01"

        converted_date = convert_string_to_date(date_string=this_payroll_date)
        
        employer_code = CompanyPayrollSettings.fetch_payroll_settings_by_id(company_id=company_id)
        if not employer_code:
            return Response(
                {"error": "error", "message": "employer code is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        send_manual_pencco_pfa_schedule.delay(
            company_id=company_id, 
            payroll_date=converted_date, 
            bulk_id=bulk_id,
            employer_code=employer_code
        )
        return Response({
            "message": "Pension schedule has been sent to Pencco",
            "company_id": company_id,
            "bulk_id": bulk_id,
            "payroll_date": this_payroll_date
        }, status=status.HTTP_200_OK)