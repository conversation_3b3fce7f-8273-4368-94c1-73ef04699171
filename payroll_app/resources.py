from django.contrib.auth import get_user_model
from import_export import resources, fields
from import_export.widgets import ForeignKeyWidget

from payroll_app.models import (
    BenefitComponentSettings,
    CompanyAnnouncement,
    CompanyDepartmentSettings,
    CompanyEmployeeAccountDetails,
    CompanyEmployeeCertifications,
    CompanyEmployeeEducationDetails,
    CompanyEmployeeExperienceDetails, 
    CompanyEmployeeList,
    CompanyEmployeeOnboardingForm,
    CompanyPayGradeSettings,
    CompanyPayGroupSettings,
    CompanyTaxBand,
    CustomComponentSettings,
    EmployeeCharge,
    ManageDepartmentRole,
    ManagePermissionsRole,
    OtherDeductionSettings,
    OtherDependencySettings, 
    PayrollTable, 
    CompanyDetailsData, 
    InstantWagePayroll, 
    InstantWagePayrollRefund,
    OneClickTransaction, 
    Beneficiary,
    CompanyPayrollSettings,
    PensionFundAdminSettings,
    PreviousCompanyEmployeeVerifications,
    SalaryComponentSettings,
    SavingPayrollEditData,
    SendPensionData,
    VerificationData
)

User = get_user_model()


class PayrollTableResource(resources.ModelResource):
    class Meta:
        model = PayrollTable

    def dehydrate_payroll_admin(self, obj):
        return obj.payroll_admin.email if obj.payroll_admin else ""
    
    def dehydrate_payroll_user(self, obj):
        return obj.payroll_user.email if obj.payroll_user else ""
    
    def dehydrate_company_owner(self, obj):
        return obj.company_owner.email if obj.company_owner else ""
    
    def dehydrate_employee(self, obj):
        return obj.employee.full_name if obj.employee else ""
    
    def dehydrate_company(self, obj):
        return obj.company.company_name if obj.company else ""

class UserResource(resources.ModelResource):
    class Meta:
        model = User


class CompanyEmployeeListResource(resources.ModelResource):
    class Meta:
        model = CompanyEmployeeList
    
    def dehydrate_employee(self, obj):
        return obj.employee.email if obj.employee else ""
    
    def dehydrate_employer(self, obj):
        return obj.employer.email if obj.employer else ""
    
    def dehydrate_company(self, obj):
        return obj.company.company_name if obj.company else ""
    
    def dehydrate_employee_department(self, obj):
        return obj.employee_department.department_name if obj.employee_department else ""
    
    def dehydrate_employee_pay_grade(self, obj):
        return obj.employee_pay_grade.pay_grade_name if obj.employee_pay_grade else ""
    
    def dehydrate_employer_role(self, obj):
        return obj.employer_role.role_name if obj.employer_role else ""
    
    def dehydrate_employee_department_role(self, obj):
        return obj.employee_department_role.role_name if obj.employee_department_role else ""
    
    def dehydrate_added_by(self, obj):
        return obj.added_by.email if obj.added_by else ""
    
    def dehydrate_pension_fund_admin(self, obj):
        return obj.pension_fund_admin.pfa_name if obj.pension_fund_admin else ""
    
    def dehydrate_employee_branch(self, obj):
        return obj.employee_branch.name if obj.employee_branch else ""

class CompanyDetailsDataResource(resources.ModelResource):
    class Meta:
        model = CompanyDetailsData

class InstantWagePayrollResource(resources.ModelResource):
    class Meta:
        model = InstantWagePayroll

class CompanyAnnouncementResource(resources.ModelResource):
    class Meta:
        model = CompanyAnnouncement

class InstantWagePayrollRefundResource(resources.ModelResource):
    class Meta:
        model = InstantWagePayrollRefund

class OneClickTransactionResource(resources.ModelResource):
    class Meta:
        model = OneClickTransaction

class BeneficiaryResource(resources.ModelResource):
    class Meta:
        model = Beneficiary

class CompanyPayrollSettingsResource(resources.ModelResource):
    class Meta:
        model = CompanyPayrollSettings
class CompanyPayGroupSettingsResource(resources.ModelResource):
    class Meta:
        model = CompanyPayGroupSettings

class CompanyPayGradeSettingsResource(resources.ModelResource):
    class Meta:
        model = CompanyPayGradeSettings

class OtherDeductionSettingsResource(resources.ModelResource):
    class Meta:
        model = OtherDeductionSettings

class CompanyDepartmentSettingsResource(resources.ModelResource):
    class Meta:
        model = CompanyDepartmentSettings

class SalaryComponentSettingsResource(resources.ModelResource):
    class Meta:
        model = SalaryComponentSettings

class CustomComponentSettingsResource(resources.ModelResource):
    class Meta:
        model = CustomComponentSettings

class BenefitComponentSettingsResource(resources.ModelResource):
    class Meta:
        model = BenefitComponentSettings

class CompanyEmployeeOnboardingFormResource(resources.ModelResource):
    class Meta:
        model = CompanyEmployeeOnboardingForm

class CompanyEmployeeAccountDetailsResource(resources.ModelResource):
    class Meta:
        model = CompanyEmployeeAccountDetails

class CompanyEmployeeEducationDetailsResource(resources.ModelResource):
    class Meta:
        model = CompanyEmployeeEducationDetails

class CompanyEmployeeExperienceDetailsResource(resources.ModelResource):
    class Meta:
        model = CompanyEmployeeExperienceDetails

class CompanyEmployeeCertificationsResource(resources.ModelResource):
    class Meta:
        model = CompanyEmployeeCertifications

class OtherDependencySettingsResource(resources.ModelResource):
    class Meta:
        model = OtherDependencySettings

class CompanyTaxBandResource(resources.ModelResource):
    class Meta:
        model = CompanyTaxBand

class PensionFundAdminSettingsResource(resources.ModelResource):
    class Meta:
        model = PensionFundAdminSettings

class ManagePermissionsRoleResource(resources.ModelResource):
    class Meta:
        model = ManagePermissionsRole

class ManageDepartmentRoleResource(resources.ModelResource):
    class Meta:
        model = ManageDepartmentRole

class SavingPayrollEditDataResource(resources.ModelResource):
    class Meta:
        model = SavingPayrollEditData

class SendPensionDataResource(resources.ModelResource):
    class Meta:
        model = SendPensionData

class EmployeeChargeResource(resources.ModelResource):
    class Meta:
        model = EmployeeCharge

class VerificationDataResource(resources.ModelResource):
    class Meta:
        model = VerificationData

class PreviousCompanyEmployeeVerificationsResource(resources.ModelResource):
    class Meta:
        model = PreviousCompanyEmployeeVerifications