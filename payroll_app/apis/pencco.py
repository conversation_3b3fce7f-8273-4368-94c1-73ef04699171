from requests import exceptions, request
import json
from config import settings
from payroll_app.apis.func import split_percentage_sum

class PenccoAPI:
    """
    This class is a placeholder for the Pencco API integration.
    """
    @classmethod
    def pencco_settings(cls):
        """
        This method retrieves the Pencco API settings.
        """
        from core.models import ConstantTable
        constant = ConstantTable.get_constant_instance()
        if constant.pencco_mode == "LIVE":
            base_url = "https://employer.gemscloud.app/api"
            api_key = settings.PENCCO_LIVE_API_KEY
            pencco_mode = "LIVE"
        elif constant.pencco_mode == "TEST":
            base_url = "https://employer-dev.gemscloud.app/api"
            api_key = settings.PENCCO_TEST_API_KEY
            pencco_mode = "TEST"
        else:
            base_url = ""
            api_key = ""
            pencco_mode = "TEST"

        return {
            "pencco_mode": pencco_mode,
            "base_url": base_url,
            "headers": {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "x-api-key": api_key
            }   
        }
    
    @classmethod
    def request_handler(cls, request_type: str, params: dict, pencco_mode: str):

        try:
            response = request(request_type, **params)
            try:
                data = response.json()
            except json.JSONDecodeError:
                data = response.text
            return {
                "status_code": response.status_code,
                "status": True,
                "data": data,
                "error": None,
                "pencco_mode": pencco_mode
            }
        except exceptions.RequestException as error:
            return {
                "status_code": 400,
                "status": False,
                "data": {},
                "error": str(error),
                "pencco_mode": pencco_mode
            }

    @classmethod
    def get_all_pfa(cls):
        """
        Fetch all PFAs from the Pencco API.
        """
        settings = cls.pencco_settings()
        base_url = settings.get("base_url", "")
        headers = settings.get("headers", {})
        pencco_mode = settings.get("pencco_mode", "TEST")
        
        url = f"{base_url}/pfas"
        response = cls.request_handler(
            "GET",
            dict(
                url=url,
                headers=headers,
                # data={},
            ),
            pencco_mode
        )
        return response

    @classmethod
    def send_pension_schedule(cls, payload: dict):
        """
        Send pension schedule to the Pencco API.
        """
        settings = cls.pencco_settings()
        base_url = settings.get("base_url", "")
        headers = settings.get("headers", {})
        pencco_mode = settings.get("pencco_mode", "TEST")

        url = f"{base_url}/schedules"
        response = cls.request_handler(
            "POST",
            dict(
                url=url,
                headers=headers,
                data=json.dumps(payload),
            ),
            pencco_mode
        )
        return response
    
    @classmethod
    def prepare_pension_schedule_payload(
        cls, 
        employer_code: str, 
        start_date: str, 
        end_date: str, 
        is_arreas: bool, 
        pension_list: list,
        total_schedule_amount: float,
        external_schedule_id: str
        ):
        """
        Prepare the payload for sending pension schedule.
        """
        payload = {
            "employer_code": f"{employer_code}",
            "period_start": f"{start_date}",
            "period_end": f"{end_date}",
            "is_arrears": is_arreas,
            "schedule_lines": pension_list,
            "external_schedule_id": f"{external_schedule_id}",
            "total_schedule_amount": total_schedule_amount
        }
        return payload
    
    @classmethod
    def get_pension_schedule(cls, schedule_id: int):
        """
        Fetch a specific pension schedule by its ID.
        """
        settings = cls.pencco_settings()
        base_url = settings.get("base_url", "")
        headers = settings.get("headers", {})
        pencco_mode = settings.get("pencco_mode", "TEST")

        url = f"{base_url}/schedules/{schedule_id}"
        response = cls.request_handler(
            "GET",
            dict(
                url=url,
                headers=headers,
            ),
            pencco_mode
        )
        return response
    
    @classmethod
    def get_employee_pension_schedule(cls, schedule_id: int, page :int, pension_pin: str = None, search_params: str = None):
        """
        Fetch a specific pension schedule by its ID.
        """
        settings = cls.pencco_settings()
        base_url = settings.get("base_url", "")
        headers = settings.get("headers", {})
        pencco_mode = settings.get("pencco_mode", "TEST")

        if pension_pin:
            pension_pin = pension_pin
        else:
            pension_pin = "null"
        
        if search_params:
            search_params = search_params
        else:
            search_params = "null"

        url = f"{base_url}/schedules/{schedule_id}/lines?per_page=100&page={page}&rsa={pension_pin}&search={search_params}"
        response = cls.request_handler(
            "GET",
            dict(
                url=url,
                headers=headers,
            ),
            pencco_mode
        )
        return response